# M-Pesa B2C Integration for G20Shop

## 📁 Enhanced Folder Structure (v2.0)

```
backend/src/services/mpesa/b2c/
├── 📁 core/
│   └── B2CCore.js              # Core M-Pesa API interactions
├── 📁 config/
│   └── B2CConfig.js            # Configuration management
├── 📁 utils/
│   └── B2CUtils.js             # Utility functions
├── 📁 validators/
│   └── B2CValidator.js         # Data validation
├── B2CService.js               # Main service layer
├── B2C_ENV_SETUP.md            # Environment setup guide
└── README.md                   # This documentation

backend/src/api/
├── 📁 controllers/mpesa/
│   └── b2c.controller.js       # HTTP request handlers
├── 📁 routes/mpesa/b2c/
│   ├── index.js                # Route index
│   ├── admin.routes.js         # Admin-only routes
│   ├── user.routes.js          # User routes
│   ├── webhook.routes.js       # Webhook endpoints
│   └── utility.routes.js       # Public utility routes
└── 📁 validators/mpesa/
    └── b2c.validator.js        # Request validation middleware

backend/src/db/models/
└── B2CTransaction.js           # Database model

backend/
├── setup-b2c-table.sql        # Database setup script
└── .env                        # Environment variables
```

## 🚀 Features

### ✅ **Transaction Types**
- **Refunds** - Customer refunds for orders
- **Salary Payments** - Employee salary disbursements
- **Promotional Payments** - Customer rewards and promotions
- **General Payments** - Other business-to-customer payments

### ✅ **Security & Validation**
- Phone number validation (Kenyan format)
- Amount limits (KES 1 - 150,000)
- Rate limiting for API endpoints
- Input sanitization and validation
- Secure callback handling

### ✅ **Monitoring & Logging**
- Comprehensive transaction logging
- Real-time status tracking
- Error handling and reporting
- Performance metrics
- Callback data sanitization

### ✅ **Admin Features**
- Transaction history with filtering
- Real-time statistics and analytics
- Manual transaction triggering
- Bulk payment processing
- Account balance monitoring

### ✅ **User Features**
- View personal B2C transactions
- Transaction status checking
- Refund tracking
- Receipt management

## 🛠️ Setup Instructions

### 1. **Database Setup**
```bash
# Execute the SQL script to create tables
psql -U postgres -d techgear -f setup-b2c-table.sql
```

### 2. **Environment Variables**
Add to your `.env` file:
```bash
# M-Pesa B2C Configuration
MPESA_ENVIRONMENT=sandbox
MPESA_CONSUMER_KEY=your_consumer_key
MPESA_CONSUMER_SECRET=your_consumer_secret
MPESA_BUSINESS_SHORT_CODE=your_business_shortcode
MPESA_INITIATOR_NAME=your_initiator_name
MPESA_SECURITY_CREDENTIAL=your_security_credential

# B2C Callback URLs
MPESA_B2C_RESULT_URL=https://yourdomain.com/api/mpesa/b2c/callback/result
MPESA_B2C_QUEUE_TIMEOUT_URL=https://yourdomain.com/api/mpesa/b2c/callback/timeout
```

### 3. **Install Dependencies**
```bash
cd backend
npm install axios moment crypto
```

### 4. **Test the Integration**
```bash
# Start the server
npm run dev

# Test health endpoint
curl http://localhost:3000/api/mpesa/b2c/health
```

## 📡 API Endpoints

### **Admin Endpoints** (Require admin authentication)

#### Process Refund
```http
POST /api/mpesa/b2c/refund
Content-Type: application/json
Authorization: Bearer admin_token

{
  "orderId": "order-uuid",
  "phoneNumber": "************",
  "amount": 1000,
  "reason": "Customer requested refund"
}
```

#### Process Salary Payment
```http
POST /api/mpesa/b2c/salary
Content-Type: application/json
Authorization: Bearer admin_token

{
  "employeePhone": "************",
  "amount": 50000,
  "employeeId": "EMP001",
  "payrollPeriod": "2024-01"
}
```

#### Process Promotional Payment
```http
POST /api/mpesa/b2c/promotion
Content-Type: application/json
Authorization: Bearer admin_token

{
  "customerPhone": "************",
  "amount": 500,
  "promotionCode": "WELCOME2024",
  "campaignId": "CAMP001"
}
```

#### Get Transaction History
```http
GET /api/mpesa/b2c/transactions?status=COMPLETED&page=1&limit=20
Authorization: Bearer admin_token
```

#### Get Transaction Statistics
```http
GET /api/mpesa/b2c/stats?startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer admin_token
```

### **User Endpoints** (Require user authentication)

#### Get My Transactions
```http
GET /api/mpesa/b2c/my-transactions?transactionType=REFUND
Authorization: Bearer user_token
```

#### Get Specific Transaction
```http
GET /api/mpesa/b2c/my-transaction/transaction-id
Authorization: Bearer user_token
```

### **Webhook Endpoints** (No authentication - Safaricom callbacks)

#### Result Callback
```http
POST /api/mpesa/b2c/callback/result/:transactionId
```

#### Timeout Callback
```http
POST /api/mpesa/b2c/callback/timeout/:transactionId
```

### **Utility Endpoints**

#### Health Check
```http
GET /api/mpesa/b2c/health
```

#### Transaction Types
```http
GET /api/mpesa/b2c/transaction-types
```

## 🔧 Usage Examples

### JavaScript/Node.js
```javascript
const B2CService = require('./services/mpesa/b2c/B2CService');

// Process a refund
const refundResult = await B2CService.processRefund({
  orderId: 'order-123',
  phoneNumber: '************',
  amount: 1000,
  reason: 'Product defect'
});

// Check transaction status
const status = await B2CService.getTransactionStatus('transaction-id');

// Get transaction history
const history = await B2CService.getTransactionHistory({
  status: 'COMPLETED',
  transactionType: 'REFUND',
  startDate: '2024-01-01',
  endDate: '2024-01-31'
});
```

### Frontend (React/JavaScript)
```javascript
// Process refund (admin only)
const processRefund = async (orderId, phoneNumber, amount, reason) => {
  const response = await fetch('/api/mpesa/b2c/refund', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${adminToken}`
    },
    body: JSON.stringify({
      orderId,
      phoneNumber,
      amount,
      reason
    })
  });

  return await response.json();
};

// Get user's transactions
const getMyTransactions = async () => {
  const response = await fetch('/api/mpesa/b2c/my-transactions', {
    headers: {
      'Authorization': `Bearer ${userToken}`
    }
  });

  return await response.json();
};
```

## 📊 Database Schema

### B2CTransaction Model
```sql
- id (UUID, Primary Key)
- conversationId (String, Unique)
- mpesaConversationId (String)
- phoneNumber (String, Validated)
- amount (Decimal, 1-150000)
- transactionType (Enum: REFUND, SALARY, PROMOTION, GENERAL)
- commandId (Enum: BusinessPayment, SalaryPayment, PromotionPayment)
- status (Enum: PENDING, SUBMITTED, COMPLETED, FAILED, TIMEOUT, CANCELLED)
- mpesaReceiptNumber (String)
- orderId (UUID, Foreign Key)
- userId (String)
- metadata (JSON)
- callbackData (JSON)
- initiatedAt (Timestamp)
- completedAt (Timestamp)
```

## 🔍 Monitoring & Debugging

### Log Messages to Watch For
```
✅ B2C payment initiated successfully
❌ B2C payment failed
📞 B2C Result callback received
⏰ B2C transaction timed out
🔧 Manually triggering B2C payment
```

### Common Issues & Solutions

1. **Invalid Security Credential**
   - Check environment variables
   - Verify credential format
   - Ensure correct environment (sandbox/production)

2. **Callback URL Not Reachable**
   - Verify URLs are publicly accessible
   - Check HTTPS configuration
   - Test with tools like ngrok for local development

3. **Transaction Failures**
   - Check account balance
   - Verify phone number format
   - Ensure amount is within limits

## 🧪 Testing

### Test Data (Sandbox)
```javascript
// Test phone numbers
const testPhones = [
  '************',
  '************',
  '************'
];

// Test amounts
const testAmounts = [1, 100, 1000, 5000, 10000];

// Test transaction types
const testTypes = ['REFUND', 'SALARY', 'PROMOTION', 'GENERAL'];
```

### Test Scenarios
1. **Successful Refund** - Process refund for valid order
2. **Failed Payment** - Invalid phone number or amount
3. **Timeout Handling** - Network issues or delays
4. **Callback Processing** - Verify callback data handling
5. **Rate Limiting** - Test API rate limits
6. **Bulk Payments** - Process multiple payments

## 🚀 Production Deployment

1. **Update Environment Variables**
   - Switch to production credentials
   - Update callback URLs to production domain
   - Enable HTTPS

2. **Security Checklist**
   - Encrypt security credentials properly
   - Implement IP whitelisting for callbacks
   - Enable request logging and monitoring
   - Set up alerts for failed transactions

3. **Performance Optimization**
   - Configure database indexes
   - Set up connection pooling
   - Implement caching for frequent queries
   - Monitor API response times

## 📞 Support

For issues or questions:
1. Check the logs for error messages
2. Verify environment configuration
3. Test with sandbox credentials first
4. Contact Safaricom support for API issues
5. Review the official M-Pesa API documentation

## 🔄 Updates & Maintenance

- Regularly update M-Pesa certificates
- Monitor transaction success rates
- Review and update rate limits
- Keep dependencies updated
- Backup transaction data regularly
