# M-Pesa B2C Enhanced Folder Structure

## 🏗️ **Professional Folder Organization**

This document outlines the enhanced, professional folder structure for the M-Pesa B2C integration, designed for maximum readability, maintainability, and scalability.

## 📁 **Complete Folder Structure**

```
backend/src/services/mpesa/b2c/
├── 📁 core/                    # Core API functionality
│   └── B2CCore.js              # Direct M-Pesa API interactions
├── 📁 config/                  # Configuration management
│   └── B2CConfig.js            # Centralized configuration
├── 📁 utils/                   # Utility functions
│   └── B2CUtils.js             # Helper functions and utilities
├── 📁 validators/              # Data validation
│   └── B2CValidator.js         # Comprehensive validation logic
├── B2CService.js               # Main service layer (orchestrator)
├── B2C_ENV_SETUP.md            # Environment setup guide
└── README.md                   # Complete documentation

backend/src/api/
├── 📁 controllers/mpesa/       # HTTP controllers
│   └── b2c.controller.js       # Request/response handling
├── 📁 routes/mpesa/b2c/        # Organized routing
│   ├── index.js                # Route aggregator
│   ├── admin.routes.js         # Admin-only endpoints
│   ├── user.routes.js          # User-accessible endpoints
│   ├── webhook.routes.js       # M-Pesa webhook handlers
│   └── utility.routes.js       # Public utility endpoints
├── 📁 routes/mpesa/            # Legacy routes
│   └── b2c.routes.js           # Backward compatibility
└── 📁 validators/mpesa/        # Request validation
    └── b2c.validator.js        # Express validation middleware

backend/src/db/models/
└── B2CTransaction.js           # Database model with methods

backend/
├── setup-b2c-table.sql        # Database initialization
└── .env                        # Environment configuration
```

## 🎯 **Design Principles**

### **1. Separation of Concerns**
- **Core**: Direct API interactions with M-Pesa
- **Service**: Business logic orchestration
- **Controller**: HTTP request/response handling
- **Routes**: Endpoint organization by access level
- **Validators**: Data validation and sanitization
- **Utils**: Reusable helper functions
- **Config**: Centralized configuration management

### **2. Modular Architecture**
- Each module has a single responsibility
- Easy to test individual components
- Simple to extend or modify functionality
- Clear dependencies between modules

### **3. Professional Organization**
- Logical grouping of related functionality
- Consistent naming conventions
- Clear file and folder purposes
- Easy navigation for developers

## 📋 **Module Responsibilities**

### **Core Layer (`core/`)**
```javascript
// B2CCore.js - Direct M-Pesa API interactions
- generateAccessToken()
- makeB2CRequest()
- queryAccountBalance()
- queryTransactionStatus()
- reverseTransaction()
```

### **Configuration Layer (`config/`)**
```javascript
// B2CConfig.js - Centralized configuration
- Environment management
- Business rules and limits
- Command ID mappings
- Validation rules
- URL configurations
```

### **Utility Layer (`utils/`)**
```javascript
// B2CUtils.js - Helper functions
- Phone number formatting/validation
- Amount formatting/validation
- Callback data parsing
- Currency formatting
- Security utilities
```

### **Validation Layer (`validators/`)**
```javascript
// B2CValidator.js - Data validation
- Payment data validation
- Refund data validation
- Salary payment validation
- Promotional payment validation
- Callback data validation
```

### **Service Layer**
```javascript
// B2CService.js - Business logic orchestration
- processB2CPayment()
- processRefund()
- processSalaryPayment()
- processPromotionalPayment()
- getTransactionHistory()
```

### **Route Organization (`routes/mpesa/b2c/`)**

#### **Admin Routes (`admin.routes.js`)**
- Payment processing endpoints
- Transaction management
- Bulk operations
- Statistics and reporting
- Account balance queries

#### **User Routes (`user.routes.js`)**
- Personal transaction history
- Refund tracking
- Transaction summaries
- Status checking

#### **Webhook Routes (`webhook.routes.js`)**
- M-Pesa callback handlers
- Result processing
- Timeout handling
- Balance query results

#### **Utility Routes (`utility.routes.js`)**
- Health checks
- Configuration info
- Validation utilities
- Test data (sandbox)

## 🔧 **Usage Examples**

### **Service Layer Usage**
```javascript
const B2CService = require('./services/mpesa/b2c/B2CService');

// Process a refund
const result = await B2CService.processRefund({
  orderId: 'order-123',
  phoneNumber: '************',
  amount: 1000,
  reason: 'Product defect'
});
```

### **Core Layer Usage**
```javascript
const B2CCore = require('./services/mpesa/b2c/core/B2CCore');
const core = new B2CCore();

// Direct API call
const result = await core.makeB2CRequest(paymentData);
```

### **Utility Usage**
```javascript
const B2CUtils = require('./services/mpesa/b2c/utils/B2CUtils');

// Format phone number
const formatted = B2CUtils.formatPhoneNumber('**********');
// Result: '************'

// Validate amount
const validation = B2CUtils.validateAmount(1000);
// Result: { isValid: true, formattedAmount: 1000, errors: [] }
```

### **Configuration Usage**
```javascript
const B2CConfig = require('./services/mpesa/b2c/config/B2CConfig');
const config = new B2CConfig();

// Get command ID for transaction type
const commandId = config.getCommandId('REFUND');
// Result: 'BusinessPayment'

// Validate amount with business rules
const validation = config.validateAmount(1000, 'SALARY');
```

## 🚀 **API Endpoint Organization**

### **Admin Endpoints**
```
POST /api/mpesa/b2c/admin/payment          # General payment
POST /api/mpesa/b2c/admin/refund           # Process refund
POST /api/mpesa/b2c/admin/salary           # Salary payment
POST /api/mpesa/b2c/admin/promotion        # Promotional payment
POST /api/mpesa/b2c/admin/bulk-payment     # Bulk payments
GET  /api/mpesa/b2c/admin/transactions     # Transaction history
GET  /api/mpesa/b2c/admin/stats            # Statistics
GET  /api/mpesa/b2c/admin/account-balance  # Account balance
```

### **User Endpoints**
```
GET /api/mpesa/b2c/user/my-transactions    # User's transactions
GET /api/mpesa/b2c/user/my-refunds         # User's refunds
GET /api/mpesa/b2c/user/transaction-summary # Transaction summary
```

### **Webhook Endpoints**
```
POST /api/mpesa/b2c/webhook/result/:id     # Result callback
POST /api/mpesa/b2c/webhook/timeout/:id    # Timeout callback
POST /api/mpesa/b2c/webhook/balance-result # Balance query result
```

### **Utility Endpoints**
```
GET  /api/mpesa/b2c/utility/health         # Health check
GET  /api/mpesa/b2c/utility/transaction-types # Available types
POST /api/mpesa/b2c/utility/validate-phone   # Phone validation
POST /api/mpesa/b2c/utility/validate-amount  # Amount validation
```

## 📈 **Benefits of This Structure**

### **For Developers**
- **Easy Navigation**: Clear folder hierarchy
- **Quick Understanding**: Self-documenting structure
- **Efficient Development**: Modular components
- **Simple Testing**: Isolated functionality
- **Easy Debugging**: Clear separation of concerns

### **For Maintenance**
- **Scalable**: Easy to add new features
- **Maintainable**: Changes isolated to specific modules
- **Readable**: Clear code organization
- **Extensible**: Simple to extend functionality
- **Professional**: Industry-standard structure

### **For Teams**
- **Collaborative**: Multiple developers can work simultaneously
- **Consistent**: Standardized organization
- **Onboarding**: New team members can quickly understand
- **Documentation**: Self-documenting structure
- **Best Practices**: Follows industry standards

## 🔄 **Migration from Old Structure**

The new structure maintains backward compatibility while providing enhanced organization:

1. **Legacy routes** still work through `b2c.routes.js`
2. **New routes** provide enhanced functionality
3. **Service layer** remains the same interface
4. **Gradual migration** possible without breaking changes

## 📚 **Documentation Structure**

Each module includes comprehensive documentation:
- **Purpose and responsibility**
- **API documentation**
- **Usage examples**
- **Error handling**
- **Testing guidelines**

This enhanced folder structure ensures the M-Pesa B2C integration is professional, maintainable, and scalable for long-term development.
