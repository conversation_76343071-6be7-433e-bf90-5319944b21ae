const express = require('express');
const router = express.Router();
const { authenticateWithClerk } = require('../middlewares/clerk-auth.middleware');
const { authenticate } = require('../middlewares/auth.middleware');

/**
 * Get current user profile (Clerk-based)
 */
router.get('/profile', authenticateWithClerk, async (req, res, next) => {
  try {
    // User data is already available from the middleware
    const user = req.user;
    
    res.status(200).json({
      success: true,
      user: {
        id: user.id,
        clerkId: user.clerkId,
        name: user.name,
        email: user.email,
        role: user.role,
        isActive: user.isActive,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Update current user profile (Clerk-based)
 */
router.put('/profile', authenticateWithClerk, async (req, res, next) => {
  try {
    const { name } = req.body;
    const user = req.user;
    
    // Update user in database
    if (name && name !== user.name) {
      await user.update({ name });
    }
    
    res.status(200).json({
      success: true,
      message: 'Profile updated successfully',
      user: {
        id: user.id,
        clerkId: user.clerkId,
        name: user.name,
        email: user.email,
        role: user.role,
        isActive: user.isActive,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Get user by ID (admin only)
 */
router.get('/:id', authenticateWithClerk, async (req, res, next) => {
  try {
    const { id } = req.params;
    const currentUser = req.user;
    
    // Only allow admins or the user themselves to access this
    if (currentUser.role !== 'admin' && currentUser.id !== id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    const User = require('../../db/models/User');
    const user = await User.findByPk(id, {
      attributes: { exclude: ['password'] }
    });
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    res.status(200).json({
      success: true,
      user: {
        id: user.id,
        clerkId: user.clerkId,
        name: user.name,
        email: user.email,
        role: user.role,
        isActive: user.isActive,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
