/**
 * M-Pesa B2C Routes Index
 * Central routing for all B2C operations
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 2024-01-15
 */

const express = require('express');
const router = express.Router();

// Import route modules
const adminRoutes = require('./admin.routes');
const userRoutes = require('./user.routes');
const webhookRoutes = require('./webhook.routes');
const utilityRoutes = require('./utility.routes');

// Mount route modules
router.use('/admin', adminRoutes);
router.use('/user', userRoutes);
router.use('/webhook', webhookRoutes);
router.use('/utility', utilityRoutes);

// Legacy routes for backward compatibility
router.use('/', require('../b2c.routes'));

module.exports = router;
