const { Product, Category, ProductImage, User, Order, sequelize } = require('../../db/models');
const { ValidationError, NotFoundError } = require('../../utils/errors');
const { Op } = require('sequelize');
const slugify = require('slugify');

/**
 * Admin Dashboard - Get overview statistics
 */
exports.getDashboard = async (req, res) => {
  try {
    // Get basic statistics first
    let totalProducts = 0;
    let totalUsers = 0;
    let totalOrders = 0;
    let recentOrders = [];

    try {
      totalProducts = await Product.count({ where: { isActive: true } });
    } catch (error) {
      console.error('Error counting products:', error.message);
    }

    try {
      totalUsers = await User.count({ where: { role: 'customer' } });
    } catch (error) {
      console.error('Error counting users:', error.message);
    }

    try {
      totalOrders = await Order.count();
    } catch (error) {
      console.error('Error counting orders:', error.message);
      // If Order table doesn't exist or has issues, set to 0
      totalOrders = 0;
    }

    try {
      recentOrders = await Order.findAll({
        limit: 10,
        order: [['createdAt', 'DESC']],
        include: [
          {
            model: User,
            attributes: ['id', 'name', 'email'],
            required: false // LEFT JOIN instead of INNER JOIN
          }
        ]
      });
    } catch (error) {
      console.error('Error fetching recent orders:', error.message);
      // If there's an issue with orders, return empty array
      recentOrders = [];
    }

    res.json({
      success: true,
      data: {
        statistics: {
          totalProducts,
          totalUsers,
          totalOrders
        },
        recentOrders
      }
    });
  } catch (error) {
    console.error('Dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to load dashboard data',
      error: error.message
    });
  }
};

/**
 * Inventory Management - Get all products with admin details
 */
exports.getInventory = async (req, res) => {
  try {
    const { page = 1, limit = 20, search, category, status } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = {};

    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { sku: { [Op.iLike]: `%${search}%` } },
        { brand: { [Op.iLike]: `%${search}%` } }
      ];
    }

    if (status) {
      whereClause.isActive = status === 'active';
    }

    const products = await Product.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: ProductImage,
          as: 'images',
          where: { isPrimary: true },
          required: false
        },
        {
          model: Category,
          as: 'categories',
          where: category ? { slug: category } : undefined,
          required: !!category
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        products: products.rows,
        pagination: {
          total: products.count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(products.count / limit)
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to load inventory',
      error: error.message
    });
  }
};

/**
 * Bulk import products from CSV/Excel data
 */
exports.bulkImportProducts = async (req, res) => {
  try {
    const { products } = req.body;

    if (!products || !Array.isArray(products) || products.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No products provided for import'
      });
    }

    const results = {
      success: true,
      message: 'Bulk import completed',
      imported: 0,
      failed: 0,
      errors: []
    };

    // Process each product
    for (let i = 0; i < products.length; i++) {
      const productData = products[i];
      const rowNumber = i + 2; // +2 because CSV starts from row 2 (after header)

      try {
        // Validate required fields
        if (!productData.name || !productData.brand || !productData.sku || !productData.regularPrice) {
          results.errors.push({
            row: rowNumber,
            error: 'Missing required fields: name, brand, sku, or regularPrice'
          });
          results.failed++;
          continue;
        }

        // Check if SKU already exists
        const existingSku = await Product.findOne({ where: { sku: productData.sku } });
        if (existingSku) {
          results.errors.push({
            row: rowNumber,
            error: `Product with SKU '${productData.sku}' already exists`
          });
          results.failed++;
          continue;
        }

        // Generate slug from name
        const slug = slugify(productData.name, { lower: true, strict: true });

        // Create product
        const product = await Product.create({
          name: productData.name,
          brand: productData.brand,
          model: productData.model || '',
          description: productData.description || '',
          sku: productData.sku,
          regularPrice: productData.regularPrice,
          salePrice: productData.salePrice,
          cost: productData.cost,
          stockQuantity: productData.stockQuantity || 0,
          minOrderQuantity: productData.minOrderQuantity || 1,
          maxOrderQuantity: productData.maxOrderQuantity,
          weight: productData.weight,
          dimensions: productData.dimensions,
          featured: productData.featured || false,
          slug,
          metaTitle: productData.metaTitle || productData.name,
          metaDescription: productData.metaDescription,
          metaKeywords: productData.metaKeywords,
          isActive: true
        });

        // Add categories if provided
        if (productData.categories && productData.categories.length > 0) {
          try {
            const categoryInstances = await Category.findAll({
              where: { id: productData.categories }
            });
            if (categoryInstances.length > 0) {
              await product.addCategories(categoryInstances);
            }
          } catch (categoryError) {
            console.warn(`Failed to add categories for product ${productData.sku}:`, categoryError.message);
          }
        }

        results.imported++;

      } catch (error) {
        console.error(`Error importing product at row ${rowNumber}:`, error);
        results.errors.push({
          row: rowNumber,
          error: error.message || 'Unknown error occurred'
        });
        results.failed++;
      }
    }

    // Update success status based on results
    if (results.imported === 0) {
      results.success = false;
      results.message = 'No products were imported successfully';
    } else if (results.failed > 0) {
      results.message = `Partially successful: ${results.imported} imported, ${results.failed} failed`;
    } else {
      results.message = `Successfully imported all ${results.imported} products`;
    }

    res.status(200).json(results);

  } catch (error) {
    console.error('Bulk import error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process bulk import',
      error: error.message,
      imported: 0,
      failed: 0,
      errors: []
    });
  }
};

/**
 * Get import jobs (for scheduled imports)
 */
exports.getImportJobs = async (req, res) => {
  try {
    const { ImportJob, ImportJobLog } = require('../db/models');

    const jobs = await ImportJob.findAll({
      include: [
        {
          model: ImportJobLog,
          as: 'logs',
          limit: 5,
          order: [['createdAt', 'DESC']]
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        jobs: jobs.map(job => ({
          id: job.id,
          name: job.name,
          description: job.description,
          sourceType: job.sourceType,
          schedule: job.schedule,
          timezone: job.timezone,
          isActive: job.isActive,
          lastRunAt: job.lastRunAt,
          nextRunAt: job.nextRunAt,
          lastRunStatus: job.lastRunStatus,
          totalRuns: job.totalRuns,
          successfulRuns: job.successfulRuns,
          failedRuns: job.failedRuns,
          createdAt: job.createdAt,
          recentLogs: job.logs || []
        }))
      }
    });
  } catch (error) {
    console.error('Get import jobs error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to load import jobs',
      error: error.message
    });
  }
};

/**
 * Create import job (for scheduled imports)
 */
exports.createImportJob = async (req, res) => {
  try {
    const { ImportJob } = require('../db/models');
    const cronScheduler = require('../../services/cronScheduler');
    const importService = require('../../services/importService');

    const {
      name,
      description,
      sourceType,
      sourceConfig,
      schedule,
      timezone = 'UTC',
      isActive = true
    } = req.body;

    // Validate required fields
    if (!name || !sourceType || !schedule) {
      return res.status(400).json({
        success: false,
        message: 'Name, source type, and schedule are required'
      });
    }

    // Validate source configuration
    try {
      importService.validateJobConfig(sourceType, sourceConfig);
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: `Invalid source configuration: ${error.message}`
      });
    }

    // Calculate next run time
    const nextRunAt = cronScheduler.parseNextRunTime(schedule);

    // Create the import job
    const importJob = await ImportJob.create({
      name,
      description,
      sourceType,
      sourceConfig,
      schedule,
      timezone,
      isActive,
      nextRunAt,
      createdBy: req.user?.id || 'system',
      totalRuns: 0,
      successfulRuns: 0,
      failedRuns: 0
    });

    // Schedule the job if active
    if (isActive) {
      await cronScheduler.scheduleJob(importJob);
    }

    res.status(201).json({
      success: true,
      data: {
        id: importJob.id,
        name: importJob.name,
        description: importJob.description,
        sourceType: importJob.sourceType,
        schedule: importJob.schedule,
        timezone: importJob.timezone,
        isActive: importJob.isActive,
        nextRunAt: importJob.nextRunAt,
        createdAt: importJob.createdAt
      },
      message: 'Import job created and scheduled successfully'
    });

  } catch (error) {
    console.error('Create import job error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create import job',
      error: error.message
    });
  }
};

/**
 * Update import job
 */
exports.updateImportJob = async (req, res) => {
  try {
    const { ImportJob } = require('../db/models');
    const cronScheduler = require('../../services/cronScheduler');
    const { id } = req.params;

    const importJob = await ImportJob.findByPk(id);
    if (!importJob) {
      return res.status(404).json({
        success: false,
        message: 'Import job not found'
      });
    }

    const updates = req.body;
    updates.updatedBy = req.user?.id || 'system';

    // If schedule changed, recalculate next run time
    if (updates.schedule && updates.schedule !== importJob.schedule) {
      updates.nextRunAt = cronScheduler.parseNextRunTime(updates.schedule);
    }

    await importJob.update(updates);

    // Reschedule if active
    if (importJob.isActive) {
      await cronScheduler.scheduleJob(importJob);
    }

    res.json({
      success: true,
      data: importJob,
      message: 'Import job updated successfully'
    });

  } catch (error) {
    console.error('Update import job error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update import job',
      error: error.message
    });
  }
};

/**
 * Delete import job
 */
exports.deleteImportJob = async (req, res) => {
  try {
    const { ImportJob } = require('../db/models');
    const { id } = req.params;

    const importJob = await ImportJob.findByPk(id);
    if (!importJob) {
      return res.status(404).json({
        success: false,
        message: 'Import job not found'
      });
    }

    await importJob.destroy();

    res.json({
      success: true,
      message: 'Import job deleted successfully'
    });

  } catch (error) {
    console.error('Delete import job error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete import job',
      error: error.message
    });
  }
};

/**
 * Manually trigger import job
 */
exports.triggerImportJob = async (req, res) => {
  try {
    const cronScheduler = require('../../services/cronScheduler');
    const { id } = req.params;
    const userId = req.user?.id || 'system';

    const result = await cronScheduler.triggerJob(id, userId);

    res.json({
      success: true,
      data: result,
      message: 'Import job triggered successfully'
    });

  } catch (error) {
    console.error('Trigger import job error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to trigger import job',
      error: error.message
    });
  }
};

/**
 * Get import job logs
 */
exports.getImportJobLogs = async (req, res) => {
  try {
    const { ImportJobLog } = require('../db/models');
    const { id } = req.params;
    const { page = 1, limit = 20 } = req.query;

    const offset = (page - 1) * limit;

    const { count, rows: logs } = await ImportJobLog.findAndCountAll({
      where: { importJobId: id },
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        logs,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get import job logs error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get import job logs',
      error: error.message
    });
  }
};

/**
 * Setup import tables (one-time setup)
 */
exports.setupImportTables = async (req, res) => {
  try {
    const sequelize = require('../../config/database');

    console.log('🔄 Creating import tables...');

    // Create import_jobs table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS import_jobs (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        "sourceType" VARCHAR(50) NOT NULL DEFAULT 'file_upload' CHECK ("sourceType" IN ('file_upload', 'ftp', 'http_url', 'google_sheets', 'api_webhook')),
        "sourceConfig" JSONB,
        schedule VARCHAR(255) NOT NULL,
        timezone VARCHAR(50) NOT NULL DEFAULT 'UTC',
        "isActive" BOOLEAN NOT NULL DEFAULT true,
        "lastRunAt" TIMESTAMP WITH TIME ZONE,
        "nextRunAt" TIMESTAMP WITH TIME ZONE,
        "lastRunStatus" VARCHAR(50) CHECK ("lastRunStatus" IN ('pending', 'running', 'success', 'failed', 'cancelled')),
        "lastRunResult" JSONB,
        "totalRuns" INTEGER NOT NULL DEFAULT 0,
        "successfulRuns" INTEGER NOT NULL DEFAULT 0,
        "failedRuns" INTEGER NOT NULL DEFAULT 0,
        "createdBy" VARCHAR(255) NOT NULL,
        "updatedBy" VARCHAR(255),
        "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
      );
    `);

    // Create import_job_logs table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS import_job_logs (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        "importJobId" UUID NOT NULL REFERENCES import_jobs(id) ON DELETE CASCADE,
        status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'success', 'failed', 'cancelled')),
        "startedAt" TIMESTAMP WITH TIME ZONE,
        "completedAt" TIMESTAMP WITH TIME ZONE,
        duration INTEGER,
        "recordsProcessed" INTEGER NOT NULL DEFAULT 0,
        "recordsImported" INTEGER NOT NULL DEFAULT 0,
        "recordsFailed" INTEGER NOT NULL DEFAULT 0,
        errors JSONB,
        "sourceInfo" JSONB,
        result JSONB,
        "triggeredBy" VARCHAR(50) NOT NULL DEFAULT 'schedule' CHECK ("triggeredBy" IN ('schedule', 'manual', 'api')),
        "triggeredByUser" VARCHAR(255),
        "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
      );
    `);

    // Create indexes
    await sequelize.query(`CREATE INDEX IF NOT EXISTS idx_import_jobs_is_active ON import_jobs("isActive");`);
    await sequelize.query(`CREATE INDEX IF NOT EXISTS idx_import_jobs_next_run_at ON import_jobs("nextRunAt");`);
    await sequelize.query(`CREATE INDEX IF NOT EXISTS idx_import_jobs_created_by ON import_jobs("createdBy");`);
    await sequelize.query(`CREATE INDEX IF NOT EXISTS idx_import_job_logs_import_job_id ON import_job_logs("importJobId");`);
    await sequelize.query(`CREATE INDEX IF NOT EXISTS idx_import_job_logs_status ON import_job_logs(status);`);
    await sequelize.query(`CREATE INDEX IF NOT EXISTS idx_import_job_logs_started_at ON import_job_logs("startedAt");`);
    await sequelize.query(`CREATE INDEX IF NOT EXISTS idx_import_job_logs_triggered_by ON import_job_logs("triggeredBy");`);

    console.log('✅ Import tables created successfully!');

    res.json({
      success: true,
      message: 'Import tables created successfully! You can now use scheduled imports.',
      data: {
        tablesCreated: ['import_jobs', 'import_job_logs'],
        indexesCreated: 7
      }
    });

  } catch (error) {
    console.error('❌ Error creating import tables:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create import tables',
      error: error.message
    });
  }
};

/**
 * Add new product to inventory
 */
exports.addProduct = async (req, res) => {
  try {
    const {
      name,
      brand,
      model,
      description,
      sku,
      regularPrice,
      salePrice,
      cost,
      stockQuantity,
      minOrderQuantity = 1,
      maxOrderQuantity,
      weight,
      dimensions,
      featured = false,
      categories = [],
      images = [],
      metaTitle,
      metaDescription,
      metaKeywords
    } = req.body;

    // Generate slug from name
    const slug = slugify(name, { lower: true, strict: true });

    // Check if SKU already exists
    const existingSku = await Product.findOne({ where: { sku } });
    if (existingSku) {
      return res.status(400).json({
        success: false,
        message: 'Product with this SKU already exists'
      });
    }

    // Create product
    const product = await Product.create({
      name,
      brand,
      model,
      description,
      sku,
      regularPrice,
      salePrice,
      cost,
      stockQuantity,
      minOrderQuantity,
      maxOrderQuantity,
      weight,
      dimensions,
      featured,
      slug,
      metaTitle: metaTitle || name,
      metaDescription,
      metaKeywords,
      isActive: true
    });

    // Add categories if provided
    if (categories.length > 0) {
      const categoryInstances = await Category.findAll({
        where: { id: categories }
      });
      await product.addCategories(categoryInstances);
    }

    // Add images if provided
    if (images.length > 0) {
      const imagePromises = images.map((image, index) =>
        ProductImage.create({
          productId: product.id,
          imageUrl: image.url,
          altText: image.altText || name,
          isPrimary: index === 0
        })
      );
      await Promise.all(imagePromises);
    }

    // Fetch complete product with associations
    const completeProduct = await Product.findByPk(product.id, {
      include: [
        { model: ProductImage, as: 'images' },
        { model: Category, as: 'categories' }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Product added successfully',
      data: completeProduct
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to add product',
      error: error.message
    });
  }
};

/**
 * Update product in inventory
 */
exports.updateProduct = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const product = await Product.findByPk(id);
    if (!product) {
      throw new NotFoundError('Product not found');
    }

    // Update slug if name changed
    if (updateData.name && updateData.name !== product.name) {
      updateData.slug = slugify(updateData.name, { lower: true, strict: true });
    }

    // Check SKU uniqueness if changed
    if (updateData.sku && updateData.sku !== product.sku) {
      const existingSku = await Product.findOne({
        where: {
          sku: updateData.sku,
          id: { [Op.ne]: id }
        }
      });
      if (existingSku) {
        return res.status(400).json({
          success: false,
          message: 'Product with this SKU already exists'
        });
      }
    }

    await product.update(updateData);

    // Update categories if provided
    if (updateData.categories) {
      const categoryInstances = await Category.findAll({
        where: { id: updateData.categories }
      });
      await product.setCategories(categoryInstances);
    }

    // Fetch updated product with associations
    const updatedProduct = await Product.findByPk(id, {
      include: [
        { model: ProductImage, as: 'images' },
        { model: Category, as: 'categories' }
      ]
    });

    res.json({
      success: true,
      message: 'Product updated successfully',
      data: updatedProduct
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to update product',
      error: error.message
    });
  }
};

/**
 * Delete product from inventory
 */
exports.deleteProduct = async (req, res) => {
  try {
    const { id } = req.params;

    const product = await Product.findByPk(id);
    if (!product) {
      throw new NotFoundError('Product not found');
    }

    // Soft delete - set isActive to false
    await product.update({ isActive: false });

    res.json({
      success: true,
      message: 'Product deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to delete product',
      error: error.message
    });
  }
};

/**
 * Update stock quantity
 */
exports.updateStock = async (req, res) => {
  try {
    const { id } = req.params;
    const { stockQuantity, operation = 'set' } = req.body;

    const product = await Product.findByPk(id);
    if (!product) {
      throw new NotFoundError('Product not found');
    }

    let newStock;
    switch (operation) {
      case 'add':
        newStock = product.stockQuantity + stockQuantity;
        break;
      case 'subtract':
        newStock = Math.max(0, product.stockQuantity - stockQuantity);
        break;
      case 'set':
      default:
        newStock = stockQuantity;
        break;
    }

    await product.update({ stockQuantity: newStock });

    res.json({
      success: true,
      message: 'Stock updated successfully',
      data: {
        productId: id,
        previousStock: product.stockQuantity,
        newStock,
        operation
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to update stock',
      error: error.message
    });
  }
};

/**
 * Admin Inventory Stats - Get inventory statistics
 */
exports.getInventoryStats = async (req, res) => {
  try {
    // Get total products count
    const totalProducts = await Product.count();

    // Get active products count
    const activeProducts = await Product.count({
      where: { isActive: true }
    });

    // Get low stock products (stock <= 10 or stock <= minOrderQuantity)
    const lowStockProducts = await Product.count({
      where: {
        isActive: true,
        [Op.or]: [
          { stockQuantity: { [Op.lte]: 10 } },
          sequelize.where(
            sequelize.col('stockQuantity'),
            Op.lte,
            sequelize.col('minOrderQuantity')
          )
        ],
        stockQuantity: { [Op.gt]: 0 }
      }
    });

    // Get out of stock products
    const outOfStockProducts = await Product.count({
      where: {
        isActive: true,
        stockQuantity: 0
      }
    });

    // Calculate total inventory value (regularPrice * stockQuantity)
    const totalValueResult = await Product.findAll({
      where: { isActive: true },
      attributes: [
        [sequelize.fn('SUM',
          sequelize.literal('CAST("regularPrice" AS DECIMAL) * CAST("stockQuantity" AS DECIMAL)')
        ), 'totalValue']
      ],
      raw: true
    });

    const totalValue = totalValueResult[0]?.totalValue || 0;

    // Get categories count (if Category model exists)
    let categories = 0;
    try {
      categories = await Category.count();
    } catch (error) {
      console.log('Category model not available, setting categories to 0');
      categories = 0;
    }

    res.json({
      success: true,
      data: {
        totalProducts,
        activeProducts,
        lowStockProducts,
        outOfStockProducts,
        totalValue: parseFloat(totalValue) || 0,
        categories
      }
    });
  } catch (error) {
    console.error('Inventory stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to load inventory statistics',
      error: error.message
    });
  }
};

/**
 * Get all users (admin view)
 */
exports.getUsers = async (req, res) => {
  try {
    const { page = 1, limit = 20, role, search } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = {};

    if (role) {
      whereClause.role = role;
    }

    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const users = await User.findAndCountAll({
      where: whereClause,
      attributes: { exclude: ['password'] },
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        users: users.rows,
        pagination: {
          total: users.count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(users.count / limit)
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to load users',
      error: error.message
    });
  }
};

/**
 * Update user role
 */
exports.updateUserRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { role } = req.body;

    const user = await User.findByPk(id);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    await user.update({ role });

    res.json({
      success: true,
      message: 'User role updated successfully',
      data: {
        userId: id,
        newRole: role
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to update user role',
      error: error.message
    });
  }
};

/**
 * Update user status (activate/deactivate)
 */
exports.updateUserStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;

    const user = await User.findByPk(id);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    await user.update({ isActive });

    res.json({
      success: true,
      message: 'User status updated successfully',
      data: {
        userId: id,
        isActive: isActive
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to update user status',
      error: error.message
    });
  }
};

/**
 * Update user details (admin only)
 */
exports.updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, email, phone, role, isActive } = req.body;

    const user = await User.findByPk(id);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Update user fields
    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (email !== undefined) updateData.email = email;
    if (phone !== undefined) updateData.phone = phone;
    if (role !== undefined) updateData.role = role;
    if (isActive !== undefined) updateData.isActive = isActive;

    await user.update(updateData);

    // Return updated user without password
    const updatedUser = await User.findByPk(id, {
      attributes: { exclude: ['password'] }
    });

    res.json({
      success: true,
      message: 'User updated successfully',
      data: {
        user: updatedUser
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to update user',
      error: error.message
    });
  }
};
