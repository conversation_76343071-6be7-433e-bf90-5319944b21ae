const mpesaService = require('../../services/mpesa.service');
const Order = require('../../db/models/Order');
const MpesaTransaction = require('../../db/models/MpesaTransaction');
const { ValidationError, NotFoundError } = require('../../utils/errors');

/**
 * M-Pesa Payment Controller
 * Handles all M-Pesa payment operations for G20Shop
 */

/**
 * Initiate STK Push payment
 */
exports.initiatePayment = async (req, res) => {
  try {
    const { orderId, phoneNumber, amount, accountReference, transactionDesc } = req.body;

    // Validate required fields
    if (!orderId || !phoneNumber || !amount) {
      throw new ValidationError('Order ID, phone number, and amount are required');
    }

    // Validate phone number format
    const phoneRegex = /^(254|0)[17]\d{8}$/;
    if (!phoneRegex.test(phoneNumber)) {
      throw new ValidationError('Invalid phone number format. Use format: ********** or ************');
    }

    // Validate amount
    if (amount < 1 || amount > 70000) {
      throw new ValidationError('Amount must be between KES 1 and KES 70,000');
    }

    // Check if order exists
    const order = await Order.findByPk(orderId);
    if (!order) {
      throw new NotFoundError('Order not found');
    }

    // Check if order is already paid
    if (order.paymentStatus === 'paid') {
      return res.status(400).json({
        success: false,
        message: 'Order is already paid'
      });
    }

    // Check if there's already a pending transaction for this order
    const existingTransaction = await MpesaTransaction.findOne({
      where: {
        orderId,
        status: 'PENDING'
      }
    });

    if (existingTransaction) {
      return res.status(400).json({
        success: false,
        message: 'Payment request already pending for this order',
        checkoutRequestId: existingTransaction.checkoutRequestId
      });
    }

    // Initiate STK Push
    const stkPushResult = await mpesaService.initiateSTKPush(
      phoneNumber,
      amount,
      accountReference,
      transactionDesc,
      orderId
    );

    if (!stkPushResult.success) {
      return res.status(400).json({
        success: false,
        message: stkPushResult.message || 'Failed to initiate payment',
        error: stkPushResult.error
      });
    }

    // Save transaction to database
    const transaction = await MpesaTransaction.create({
      orderId,
      merchantRequestId: stkPushResult.merchantRequestId,
      checkoutRequestId: stkPushResult.checkoutRequestId,
      phoneNumber,
      amount,
      transactionType: 'STK_PUSH',
      status: 'PENDING',
      resultCode: null,
      resultDesc: 'Payment request sent to customer phone'
    });

    // Update order status
    await order.update({
      paymentStatus: 'pending',
      paymentMethod: 'mpesa'
    });

    res.json({
      success: true,
      message: 'Payment request sent to your phone. Please enter your M-Pesa PIN to complete the payment.',
      data: {
        merchantRequestId: stkPushResult.merchantRequestId,
        checkoutRequestId: stkPushResult.checkoutRequestId,
        customerMessage: stkPushResult.customerMessage,
        transactionId: transaction.id
      }
    });

  } catch (error) {
    console.error('❌ Payment initiation failed:', error);
    res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'Failed to initiate payment',
      error: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

/**
 * Handle STK Push callback from Safaricom
 */
exports.stkPushCallback = async (req, res) => {
  try {
    const { orderId } = req.params;
    const callbackData = req.body;

    console.log('📞 Received STK Push callback for order:', orderId);
    console.log('📞 Callback data:', JSON.stringify(callbackData, null, 2));

    // Validate callback
    const validation = mpesaService.validateCallback(callbackData, orderId);
    if (!validation.valid) {
      console.error('❌ Invalid callback:', validation.reason);
      return res.status(400).json({
        success: false,
        message: validation.reason
      });
    }

    const stkCallback = validation.data;
    const { ResultCode, ResultDesc, MerchantRequestID, CheckoutRequestID } = stkCallback;

    // Find the transaction
    const transaction = await MpesaTransaction.findOne({
      where: {
        orderId,
        checkoutRequestId: CheckoutRequestID
      }
    });

    if (!transaction) {
      console.error('❌ Transaction not found for callback');
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }

    // Find the order
    const order = await Order.findByPk(orderId);
    if (!order) {
      console.error('❌ Order not found for callback');
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Process callback based on result code
    let status, paymentStatus, mpesaReceiptNumber, transactionDate;

    if (ResultCode === 0) {
      // Payment successful
      status = 'SUCCESS';
      paymentStatus = 'paid';

      // Extract payment details from callback metadata
      const callbackMetadata = stkCallback.CallbackMetadata;
      if (callbackMetadata && callbackMetadata.Item) {
        const items = callbackMetadata.Item;

        // Find receipt number
        const receiptItem = items.find(item => item.Name === 'MpesaReceiptNumber');
        mpesaReceiptNumber = receiptItem ? receiptItem.Value : null;

        // Find transaction date
        const dateItem = items.find(item => item.Name === 'TransactionDate');
        if (dateItem) {
          // Convert from format: 20231201143045 to proper date
          const dateStr = dateItem.Value.toString();
          const year = dateStr.substring(0, 4);
          const month = dateStr.substring(4, 6);
          const day = dateStr.substring(6, 8);
          const hour = dateStr.substring(8, 10);
          const minute = dateStr.substring(10, 12);
          const second = dateStr.substring(12, 14);
          transactionDate = new Date(`${year}-${month}-${day}T${hour}:${minute}:${second}`);
        }
      }

      console.log('✅ Payment successful:', {
        orderId,
        receipt: mpesaReceiptNumber,
        amount: transaction.amount
      });

    } else {
      // Payment failed or cancelled
      status = 'FAILED';
      paymentStatus = 'failed';

      console.log('❌ Payment failed:', {
        orderId,
        resultCode: ResultCode,
        resultDesc: ResultDesc
      });
    }

    // Update transaction
    await transaction.update({
      status,
      resultCode: ResultCode,
      resultDesc: ResultDesc,
      mpesaReceiptNumber,
      transactionDate,
      callbackData
    });

    // Update order
    await order.update({
      paymentStatus,
      ...(mpesaReceiptNumber && { mpesaReceiptNumber }),
      ...(transactionDate && { paidAt: transactionDate })
    });

    // Clear cart after successful payment
    if (ResultCode === 0 && order.userId) {
      try {
        const orderService = require('../../services/order.service');
        await orderService.clearCartAfterPayment(orderId);
        console.log('✅ Cart cleared after successful M-Pesa payment');
      } catch (cartError) {
        console.error('❌ Failed to clear cart after payment:', cartError);
        // Don't fail the callback for cart clearing issues
      }
    }

    // TODO: Send customer notification
    // TODO: Trigger order fulfillment if payment successful

    // Respond to Safaricom
    res.json({
      ResultCode: 0,
      ResultDesc: 'Callback processed successfully'
    });

  } catch (error) {
    console.error('❌ STK Push callback processing failed:', error);

    // Still respond with success to Safaricom to avoid retries
    res.json({
      ResultCode: 0,
      ResultDesc: 'Callback received'
    });
  }
};

/**
 * Query payment status
 */
exports.queryPaymentStatus = async (req, res) => {
  try {
    const { checkoutRequestId } = req.params;

    // Find transaction
    const transaction = await MpesaTransaction.findOne({
      where: { checkoutRequestId },
      include: [{ model: Order, as: 'order' }]
    });

    if (!transaction) {
      throw new NotFoundError('Transaction not found');
    }

    // Query STK Push status from M-Pesa
    const statusResult = await mpesaService.querySTKPushStatus(checkoutRequestId);

    if (statusResult.success) {
      // Update transaction if status has changed
      if (statusResult.resultCode !== transaction.resultCode) {
        await transaction.update({
          resultCode: statusResult.resultCode,
          resultDesc: statusResult.resultDesc,
          status: statusResult.resultCode === 0 ? 'SUCCESS' : 'FAILED'
        });
      }
    }

    res.json({
      success: true,
      data: {
        transactionId: transaction.id,
        orderId: transaction.orderId,
        status: transaction.status,
        resultCode: transaction.resultCode,
        resultDesc: transaction.resultDesc,
        amount: transaction.amount,
        phoneNumber: transaction.phoneNumber,
        mpesaReceiptNumber: transaction.mpesaReceiptNumber,
        transactionDate: transaction.transactionDate,
        createdAt: transaction.createdAt
      }
    });

  } catch (error) {
    console.error('❌ Payment status query failed:', error);
    res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'Failed to query payment status'
    });
  }
};

/**
 * Process refund (B2C)
 */
exports.processRefund = async (req, res) => {
  try {
    const { orderId, amount, phoneNumber, reason } = req.body;

    // Validate required fields
    if (!orderId || !amount || !phoneNumber) {
      throw new ValidationError('Order ID, amount, and phone number are required');
    }

    // Find order
    const order = await Order.findByPk(orderId);
    if (!order) {
      throw new NotFoundError('Order not found');
    }

    // Check if order is paid
    if (order.paymentStatus !== 'paid') {
      return res.status(400).json({
        success: false,
        message: 'Order is not paid, cannot process refund'
      });
    }

    // Check refund amount
    if (amount > order.totalAmount) {
      return res.status(400).json({
        success: false,
        message: 'Refund amount cannot exceed order total'
      });
    }

    // Process B2C payment
    const refundResult = await mpesaService.processB2CPayment(
      phoneNumber,
      amount,
      reason || `Refund for order ${orderId}`,
      'Refund Processing'
    );

    if (!refundResult.success) {
      return res.status(400).json({
        success: false,
        message: 'Failed to process refund',
        error: refundResult.error
      });
    }

    // Save refund transaction
    const refundTransaction = await MpesaTransaction.create({
      orderId,
      phoneNumber,
      amount,
      transactionType: 'B2C_REFUND',
      status: 'PENDING',
      resultDesc: 'Refund initiated',
      callbackData: {
        conversationId: refundResult.conversationId,
        originatorConversationId: refundResult.originatorConversationId
      }
    });

    res.json({
      success: true,
      message: 'Refund initiated successfully',
      data: {
        transactionId: refundTransaction.id,
        conversationId: refundResult.conversationId,
        originatorConversationId: refundResult.originatorConversationId
      }
    });

  } catch (error) {
    console.error('❌ Refund processing failed:', error);
    res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'Failed to process refund'
    });
  }
};

/**
 * Get transaction history for an order
 */
exports.getTransactionHistory = async (req, res) => {
  try {
    const { orderId } = req.params;

    const transactions = await MpesaTransaction.findAll({
      where: { orderId },
      order: [['createdAt', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        orderId,
        transactions: transactions.map(tx => ({
          id: tx.id,
          type: tx.transactionType,
          status: tx.status,
          amount: tx.amount,
          phoneNumber: tx.phoneNumber,
          mpesaReceiptNumber: tx.mpesaReceiptNumber,
          resultDesc: tx.resultDesc,
          transactionDate: tx.transactionDate,
          createdAt: tx.createdAt
        }))
      }
    });

  } catch (error) {
    console.error('❌ Transaction history query failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve transaction history'
    });
  }
};
