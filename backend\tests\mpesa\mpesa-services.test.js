/**
 * M-Pesa Services Backend Tests
 * Test suite for M-Pesa service layer functions
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-01-15
 */

const MpesaService = require('../../src/services/mpesa/mpesa.service');
const { sequelize } = require('../../src/config/database');

// Mock external M-Pesa API calls
jest.mock('axios');
const axios = require('axios');

describe('M-Pesa Services Backend Tests', () => {
  let mpesaService;

  beforeAll(async () => {
    await sequelize.sync({ force: true });
    mpesaService = new MpesaService();
  });

  afterAll(async () => {
    await sequelize.close();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('🔐 Authentication Service', () => {
    test('should generate access token successfully', async () => {
      const mockTokenResponse = {
        data: {
          access_token: 'mock_access_token_123',
          expires_in: '3599'
        }
      };

      axios.get.mockResolvedValue(mockTokenResponse);

      const token = await mpesaService.getAccessToken();

      expect(token).toBe('mock_access_token_123');
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining('/oauth/v1/generate'),
        expect.objectContaining({
          auth: expect.any(Object)
        })
      );
    });

    test('should handle authentication errors', async () => {
      axios.get.mockRejectedValue(new Error('Authentication failed'));

      await expect(mpesaService.getAccessToken())
        .rejects
        .toThrow('Authentication failed');
    });

    test('should cache access token', async () => {
      const mockTokenResponse = {
        data: {
          access_token: 'cached_token_123',
          expires_in: '3599'
        }
      };

      axios.get.mockResolvedValue(mockTokenResponse);

      // First call
      const token1 = await mpesaService.getAccessToken();
      // Second call should use cached token
      const token2 = await mpesaService.getAccessToken();

      expect(token1).toBe(token2);
      expect(axios.get).toHaveBeenCalledTimes(1);
    });
  });

  describe('📱 Phone Number Validation', () => {
    test('should validate Kenyan phone numbers', () => {
      const testCases = [
        { input: '254708374149', expected: { isValid: true, formatted: '254708374149' } },
        { input: '0708374149', expected: { isValid: true, formatted: '254708374149' } },
        { input: '+254708374149', expected: { isValid: true, formatted: '254708374149' } },
        { input: '708374149', expected: { isValid: true, formatted: '254708374149' } },
        { input: '1234567890', expected: { isValid: false } },
        { input: '254123456', expected: { isValid: false } },
        { input: '', expected: { isValid: false } }
      ];

      testCases.forEach(testCase => {
        const result = mpesaService.validatePhoneNumber(testCase.input);
        expect(result.isValid).toBe(testCase.expected.isValid);
        if (testCase.expected.formatted) {
          expect(result.formatted).toBe(testCase.expected.formatted);
        }
      });
    });

    test('should provide masked phone numbers', () => {
      const result = mpesaService.validatePhoneNumber('254708374149');
      expect(result.masked).toBe('****4149');
    });

    test('should handle edge cases', () => {
      const edgeCases = [
        null,
        undefined,
        123456789,
        {},
        []
      ];

      edgeCases.forEach(edgeCase => {
        const result = mpesaService.validatePhoneNumber(edgeCase);
        expect(result.isValid).toBe(false);
      });
    });
  });

  describe('💰 Amount Validation', () => {
    test('should validate transaction amounts', () => {
      const testCases = [
        { amount: 10, valid: true },
        { amount: 50000, valid: true },
        { amount: 250000, valid: true },
        { amount: 5, valid: false }, // Below minimum
        { amount: 300000, valid: false }, // Above maximum
        { amount: -100, valid: false }, // Negative
        { amount: 0, valid: false }, // Zero
        { amount: 'invalid', valid: false } // Non-numeric
      ];

      testCases.forEach(testCase => {
        const result = mpesaService.validateAmount(testCase.amount);
        expect(result.isValid).toBe(testCase.valid);
        
        if (testCase.valid) {
          expect(result.formatted).toBeDefined();
          expect(result.estimatedFee).toBeDefined();
        } else {
          expect(result.errors.length).toBeGreaterThan(0);
        }
      });
    });

    test('should calculate fees correctly', () => {
      const feeTestCases = [
        { amount: 100, expectedFee: 11 },
        { amount: 500, expectedFee: 11 },
        { amount: 1000, expectedFee: 15 },
        { amount: 5000, expectedFee: 25 },
        { amount: 10000, expectedFee: 25 }
      ];

      feeTestCases.forEach(testCase => {
        const fee = mpesaService.calculateTransactionFee(testCase.amount);
        expect(fee).toBe(testCase.expectedFee);
      });
    });
  });

  describe('🏪 Business Logic', () => {
    test('should check business hours correctly', () => {
      const businessHours = mpesaService.checkBusinessHours();
      
      expect(businessHours).toHaveProperty('isOpen');
      expect(businessHours).toHaveProperty('currentTime');
      expect(businessHours).toHaveProperty('businessHours');
      expect(typeof businessHours.isOpen).toBe('boolean');
    });

    test('should generate unique conversation IDs', () => {
      const id1 = mpesaService.generateConversationId();
      const id2 = mpesaService.generateConversationId();
      
      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^AG_\d{8}_\d+$/);
      expect(id2).toMatch(/^AG_\d{8}_\d+$/);
    });

    test('should generate unique originator conversation IDs', () => {
      const id1 = mpesaService.generateOriginatorConversationId();
      const id2 = mpesaService.generateOriginatorConversationId();
      
      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^ORG_\d{8}_\d+$/);
      expect(id2).toMatch(/^ORG_\d{8}_\d+$/);
    });

    test('should format currency correctly', () => {
      const testCases = [
        { amount: 1000, expected: 'KES 1,000' },
        { amount: 1000.50, expected: 'KES 1,001' }, // Rounded up
        { amount: 250000, expected: 'KES 250,000' }
      ];

      testCases.forEach(testCase => {
        const formatted = mpesaService.formatCurrency(testCase.amount);
        expect(formatted).toBe(testCase.expected);
      });
    });
  });

  describe('💳 B2C Payment Processing', () => {
    test('should process B2C payment successfully', async () => {
      const mockTokenResponse = {
        data: { access_token: 'mock_token', expires_in: '3599' }
      };
      
      const mockPaymentResponse = {
        data: {
          ConversationID: 'AG_20240115_123456789',
          OriginatorConversationID: 'ORG_20240115_123456789',
          ResponseCode: '0',
          ResponseDescription: 'Accept the service request successfully.'
        }
      };

      axios.get.mockResolvedValue(mockTokenResponse);
      axios.post.mockResolvedValue(mockPaymentResponse);

      const paymentData = {
        phoneNumber: '254708374149',
        amount: 1000,
        transactionType: 'GENERAL',
        remarks: 'Test payment',
        occasion: 'Testing'
      };

      const result = await mpesaService.processB2CPayment(paymentData, 'user_123', 'admin_456');

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('transactionId');
      expect(result.data).toHaveProperty('conversationId');
      expect(result.data.status).toBe('SUBMITTED');
    });

    test('should handle B2C payment errors', async () => {
      const mockTokenResponse = {
        data: { access_token: 'mock_token', expires_in: '3599' }
      };
      
      const mockErrorResponse = {
        data: {
          ResponseCode: '1',
          ResponseDescription: 'Invalid request'
        }
      };

      axios.get.mockResolvedValue(mockTokenResponse);
      axios.post.mockResolvedValue(mockErrorResponse);

      const paymentData = {
        phoneNumber: 'invalid',
        amount: 1000,
        transactionType: 'GENERAL',
        remarks: 'Test payment'
      };

      await expect(mpesaService.processB2CPayment(paymentData, 'user_123', 'admin_456'))
        .rejects
        .toThrow();
    });

    test('should validate payment data before processing', async () => {
      const invalidPaymentData = {
        phoneNumber: 'invalid',
        amount: -100,
        transactionType: 'INVALID_TYPE'
      };

      await expect(mpesaService.processB2CPayment(invalidPaymentData, 'user_123', 'admin_456'))
        .rejects
        .toThrow();
    });
  });

  describe('💸 Refund Processing', () => {
    test('should process refund successfully', async () => {
      // First create a transaction to refund
      const mockTokenResponse = {
        data: { access_token: 'mock_token', expires_in: '3599' }
      };
      
      const mockRefundResponse = {
        data: {
          ConversationID: 'AG_20240115_refund_123',
          OriginatorConversationID: 'ORG_20240115_refund_123',
          ResponseCode: '0',
          ResponseDescription: 'Accept the service request successfully.'
        }
      };

      axios.get.mockResolvedValue(mockTokenResponse);
      axios.post.mockResolvedValue(mockRefundResponse);

      const refundData = {
        orderId: 'ORD_123456',
        phoneNumber: '254708374149',
        amount: 500,
        reason: 'Customer request'
      };

      const result = await mpesaService.processRefund(refundData, 'user_123', 'admin_456');

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('transactionId');
      expect(result.data.type).toBe('REFUND');
    });

    test('should prevent duplicate refunds', async () => {
      // Create a refund first
      const mockTokenResponse = {
        data: { access_token: 'mock_token', expires_in: '3599' }
      };
      
      const mockRefundResponse = {
        data: {
          ConversationID: 'AG_20240115_dup_refund',
          OriginatorConversationID: 'ORG_20240115_dup_refund',
          ResponseCode: '0',
          ResponseDescription: 'Accept the service request successfully.'
        }
      };

      axios.get.mockResolvedValue(mockTokenResponse);
      axios.post.mockResolvedValue(mockRefundResponse);

      const refundData = {
        orderId: 'ORD_DUPLICATE_TEST',
        phoneNumber: '254708374149',
        amount: 500,
        reason: 'First refund'
      };

      // First refund should succeed
      await mpesaService.processRefund(refundData, 'user_123', 'admin_456');

      // Second refund for same order should fail
      const duplicateRefundData = {
        ...refundData,
        reason: 'Duplicate refund attempt'
      };

      await expect(mpesaService.processRefund(duplicateRefundData, 'user_123', 'admin_456'))
        .rejects
        .toThrow('already processed');
    });
  });

  describe('📊 Transaction Queries', () => {
    test('should get user transactions with pagination', async () => {
      const userId = 'user_pagination_test';
      const params = {
        page: 1,
        limit: 5,
        status: 'COMPLETED'
      };

      const result = await mpesaService.getUserTransactions(userId, params);

      expect(result).toHaveProperty('transactions');
      expect(result).toHaveProperty('pagination');
      expect(Array.isArray(result.transactions)).toBe(true);
      expect(result.pagination).toHaveProperty('page');
      expect(result.pagination).toHaveProperty('limit');
      expect(result.pagination).toHaveProperty('total');
      expect(result.pagination).toHaveProperty('totalPages');
    });

    test('should get transaction summary', async () => {
      const userId = 'user_summary_test';
      const summary = await mpesaService.getTransactionSummary(userId);

      expect(summary).toHaveProperty('totalTransactions');
      expect(summary).toHaveProperty('completedTransactions');
      expect(summary).toHaveProperty('pendingTransactions');
      expect(summary).toHaveProperty('failedTransactions');
      expect(summary).toHaveProperty('totalAmount');
      expect(summary).toHaveProperty('totalRefunds');
      expect(typeof summary.totalTransactions).toBe('number');
    });

    test('should get user refunds', async () => {
      const userId = 'user_refunds_test';
      const params = { page: 1, limit: 10 };

      const result = await mpesaService.getUserRefunds(userId, params);

      expect(result).toHaveProperty('refunds');
      expect(result).toHaveProperty('pagination');
      expect(Array.isArray(result.refunds)).toBe(true);
    });
  });

  describe('🔧 Utility Functions', () => {
    test('should get transaction types', () => {
      const types = mpesaService.getTransactionTypes();
      
      expect(Array.isArray(types)).toBe(true);
      expect(types).toContain('REFUND');
      expect(types).toContain('SALARY');
      expect(types).toContain('PROMOTION');
      expect(types).toContain('GENERAL');
    });

    test('should get status types', () => {
      const statuses = mpesaService.getStatusTypes();
      
      expect(Array.isArray(statuses)).toBe(true);
      expect(statuses).toContain('SUBMITTED');
      expect(statuses).toContain('COMPLETED');
      expect(statuses).toContain('FAILED');
      expect(statuses).toContain('TIMEOUT');
      expect(statuses).toContain('CANCELLED');
    });

    test('should check service health', () => {
      const health = mpesaService.checkServiceHealth();
      
      expect(health).toHaveProperty('status');
      expect(health).toHaveProperty('timestamp');
      expect(health).toHaveProperty('version');
      expect(health).toHaveProperty('environment');
      expect(health.status).toBe('healthy');
    });
  });

  describe('🚫 Error Handling', () => {
    test('should handle network errors gracefully', async () => {
      axios.get.mockRejectedValue(new Error('Network Error'));

      await expect(mpesaService.getAccessToken())
        .rejects
        .toThrow('Network Error');
    });

    test('should handle invalid API responses', async () => {
      const invalidResponse = {
        data: {
          // Missing required fields
        }
      };

      axios.get.mockResolvedValue(invalidResponse);

      await expect(mpesaService.getAccessToken())
        .rejects
        .toThrow();
    });

    test('should validate input parameters', () => {
      expect(() => mpesaService.validatePhoneNumber(null)).not.toThrow();
      expect(() => mpesaService.validateAmount(null)).not.toThrow();
      expect(() => mpesaService.calculateTransactionFee(-100)).toThrow();
    });
  });
});
