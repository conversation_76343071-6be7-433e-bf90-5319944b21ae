const { ClerkExpressRequireAuth, ClerkExpressWithAuth } = require('@clerk/clerk-sdk-node');
const { AuthenticationError, AuthorizationError } = require('../../utils/errors');
const User = require('../../db/models/User');

/**
 * Clerk authentication middleware - requires authentication
 */
exports.clerkAuth = (req, res, next) => {
  ClerkExpressRequireAuth({
    onError: (error) => {
      // Handle Clerk authentication errors properly
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        error: 'Unauthorized'
      });
    }
  })(req, res, (err) => {
    if (err) {
      // Handle any other Clerk errors
      if (err.message === 'Unauthenticated' || err.message.includes('Unauthenticated')) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required',
          error: 'Unauthorized'
        });
      }
      return next(err);
    }
    next();
  });
};

/**
 * Clerk optional authentication middleware - doesn't require authentication
 */
exports.clerkOptionalAuth = (req, res, next) => {
  ClerkExpressWithAuth({
    onError: (error) => {
      // Silently continue without authentication
      return null;
    }
  })(req, res, next);
};

/**
 * Middleware to sync Clerk user with local database
 */
exports.syncClerkUser = async (req, res, next) => {
  try {
    if (!req.auth?.userId) {
      console.log('🔍 syncClerkUser: No auth.userId found, skipping...');
      return next();
    }

    const clerkUserId = req.auth.userId;
    console.log('🔍 syncClerkUser: Processing user with Clerk ID:', clerkUserId);

    // Try to get user data from Clerk
    let clerkUser;
    try {
      clerkUser = req.auth.user;
      if (!clerkUser && req.auth.getUser) {
        console.log('🔍 syncClerkUser: Fetching user data from Clerk API...');
        clerkUser = await req.auth.getUser();
      }
    } catch (clerkError) {
      console.error('❌ syncClerkUser: Error fetching user from Clerk:', clerkError.message);
    }

    // If we still don't have a valid Clerk user object, create a minimal one
    console.log('🔍 syncClerkUser: Checking clerkUser before fallback:', clerkUser);
    if (!clerkUser) {
      console.log('⚠️ syncClerkUser: No Clerk user object available, creating minimal user data');
      clerkUser = {
        emailAddresses: [],
        firstName: '',
        lastName: '',
        email: null,
        emailAddress: null,
        primaryEmailAddress: null
      };
      console.log('🔍 syncClerkUser: Created fallback clerkUser:', clerkUser);
    } else {
      console.log('🔍 syncClerkUser: clerkUser exists, type:', typeof clerkUser);
    }

    console.log('🔍 syncClerkUser: Full Clerk user object:', clerkUser ? JSON.stringify(clerkUser, null, 2) : 'undefined');
    console.log('🔍 syncClerkUser: Clerk user data:', {
      hasEmailAddresses: !!clerkUser?.emailAddresses?.length,
      emailAddresses: clerkUser?.emailAddresses,
      primaryEmailAddress: clerkUser?.primaryEmailAddress,
      firstName: clerkUser?.firstName,
      lastName: clerkUser?.lastName
    });

    // Check if user exists in local database
    let user = await User.findOne({
      where: { clerkId: clerkUserId }
    });

    if (!user) {
      console.log('🔍 syncClerkUser: Creating new user in database...');
      // Create new user in local database
      // Try multiple ways to get email from Clerk
      let email = null;

      // Check all possible email properties
      if (clerkUser?.emailAddresses?.length > 0) {
        email = clerkUser.emailAddresses[0]?.emailAddress;
      } else if (clerkUser?.primaryEmailAddress?.emailAddress) {
        email = clerkUser.primaryEmailAddress.emailAddress;
      } else if (clerkUser?.email) {
        email = clerkUser.email;
      } else if (clerkUser?.emailAddress) {
        email = clerkUser.emailAddress;
      }

      console.log('🔍 syncClerkUser: Extracted email:', email);

      const userData = {
        clerkId: clerkUserId,
        email: email || `user_${clerkUserId}@clerk.local`,
        name: `${clerkUser?.firstName || ''} ${clerkUser?.lastName || ''}`.trim() || `User ${clerkUserId.substring(0, 8)}`,
        role: 'admin', // Set as admin for testing
        isActive: true,
        authProvider: 'clerk'
      };
      console.log('🔍 syncClerkUser: User data to create:', userData);
      try {
        user = await User.create(userData);
        console.log('✅ syncClerkUser: New user created with ID:', user.id);
      } catch (createError) {
        console.error('❌ syncClerkUser: Error creating user:', createError.message);
        console.error('❌ syncClerkUser: Create error details:', createError);
        throw createError;
      }
    } else {
      console.log('🔍 syncClerkUser: User found in database, checking for updates...');
      // Update user information if needed
      const updates = {};

      // Try multiple ways to get email from Clerk
      let currentEmail = null;

      // Check all possible email properties
      if (clerkUser?.emailAddresses?.length > 0) {
        currentEmail = clerkUser.emailAddresses[0]?.emailAddress;
      } else if (clerkUser?.primaryEmailAddress?.emailAddress) {
        currentEmail = clerkUser.primaryEmailAddress.emailAddress;
      } else if (clerkUser?.email) {
        currentEmail = clerkUser.email;
      } else if (clerkUser?.emailAddress) {
        currentEmail = clerkUser.emailAddress;
      }

      console.log('🔍 syncClerkUser: Extracted current email for update:', currentEmail);

      const currentName = `${clerkUser?.firstName || ''} ${clerkUser?.lastName || ''}`.trim();

      if (user.email !== currentEmail && currentEmail) {
        updates.email = currentEmail;
      }
      if (user.name !== currentName && currentName) {
        updates.name = currentName;
      }

      if (Object.keys(updates).length > 0) {
        console.log('🔍 syncClerkUser: Updating user with:', updates);
        await user.update(updates);
        console.log('✅ syncClerkUser: User updated successfully');
      } else {
        console.log('✅ syncClerkUser: No updates needed');
      }
    }

    console.log('✅ syncClerkUser: Final user data:', {
      id: user.id,
      clerkId: user.clerkId,
      email: user.email,
      name: user.name,
      role: user.role
    });

    // Attach user to request
    req.user = user;
    next();
  } catch (error) {
    console.error('❌ syncClerkUser: Error in middleware:', error.message);
    console.error('❌ syncClerkUser: Stack trace:', error.stack);
    next(error);
  }
};

/**
 * Role-based authorization middleware
 */
exports.requireRole = (roles = []) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        throw new AuthenticationError('User not authenticated');
      }

      // Convert single role to array
      const allowedRoles = Array.isArray(roles) ? roles : [roles];

      if (allowedRoles.length && !allowedRoles.includes(req.user.role)) {
        throw new AuthorizationError(`Access denied. Required roles: ${allowedRoles.join(', ')}`);
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Admin only middleware
 */
exports.requireAdmin = exports.requireRole(['admin']);

/**
 * Customer or Admin middleware
 */
exports.requireCustomerOrAdmin = exports.requireRole(['customer', 'admin']);

/**
 * Combined Clerk auth + user sync middleware
 */
exports.authenticateWithClerk = [
  exports.clerkAuth,
  exports.syncClerkUser
];

/**
 * Combined optional Clerk auth + user sync middleware
 */
exports.optionalAuthWithClerk = [
  exports.clerkOptionalAuth,
  exports.syncClerkUser
];

/**
 * Admin authentication middleware (Clerk + role check)
 */
exports.authenticateAdmin = [
  exports.clerkAuth,
  exports.syncClerkUser,
  exports.requireAdmin
];

/**
 * Customer authentication middleware (Clerk + role check)
 */
exports.authenticateCustomer = [
  exports.clerkAuth,
  exports.syncClerkUser,
  exports.requireCustomerOrAdmin
];
