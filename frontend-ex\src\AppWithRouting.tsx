import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Head<PERSON>, Footer } from './components/layout';
import {
  HomePage,
  ProductsPage,
  ProductPage,
  CartPage,
  CheckoutPage,
  LoginPage,
  RegisterPage,
  ProfilePage,
  SearchPage,
  NotFoundPage
} from './pages';
import {
  CartProvider,
  WishlistProvider,
  RecentlyViewedProvider
} from './contexts';

export function AppWithRouting() {
  return (
    <CartProvider>
      <WishlistProvider>
        <RecentlyViewedProvider>
          <Router>
            <div className="flex flex-col min-h-screen bg-[#0a0a0a] text-white">
              <Header />
              <main className="flex-grow">
                <Routes>
                  <Route path="/" element={<HomePage />} />
                  <Route path="/products" element={<ProductsPage />} />
                  <Route path="/products/:slug" element={<ProductPage />} />
                  <Route path="/search" element={<SearchPage />} />
                  <Route path="/cart" element={<CartPage />} />
                  <Route path="/checkout" element={<CheckoutPage />} />
                  <Route path="/login" element={<LoginPage />} />
                  <Route path="/register" element={<RegisterPage />} />
                  <Route path="/profile" element={<ProfilePage />} />
                  <Route path="*" element={<NotFoundPage />} />
                </Routes>
              </main>
              <Footer />
            </div>
          </Router>
        </RecentlyViewedProvider>
      </WishlistProvider>
    </CartProvider>
  );
}
