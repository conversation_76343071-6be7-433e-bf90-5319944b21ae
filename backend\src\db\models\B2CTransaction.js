const { DataTypes } = require('sequelize');
const sequelize = require('../../config/database');

const B2CTransaction = sequelize.define('B2CTransaction', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  conversationId: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    comment: 'Our internal conversation ID'
  },
  mpesaConversationId: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'M-Pesa conversation ID from API response'
  },
  originatorConversationId: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'M-Pesa originator conversation ID'
  },
  phoneNumber: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      is: /^254[0-9]{9}$/,
      notEmpty: true
    },
    comment: 'Customer phone number in 254XXXXXXXXX format'
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 1,
      max: 150000
    },
    comment: 'Transaction amount in KES'
  },
  transactionType: {
    type: DataTypes.ENUM('REFUND', 'SALARY', 'PROMOTION', 'GENERAL'),
    allowNull: false,
    defaultValue: 'GENERAL',
    comment: 'Type of B2C transaction'
  },
  commandId: {
    type: DataTypes.ENUM('BusinessPayment', 'SalaryPayment', 'PromotionPayment'),
    allowNull: false,
    defaultValue: 'BusinessPayment',
    comment: 'M-Pesa command ID for the transaction'
  },
  remarks: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'G20Shop Payment',
    validate: {
      len: [1, 100]
    },
    comment: 'Transaction remarks'
  },
  occasion: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'Payment Processing',
    validate: {
      len: [1, 100]
    },
    comment: 'Transaction occasion'
  },
  status: {
    type: DataTypes.ENUM('PENDING', 'SUBMITTED', 'COMPLETED', 'FAILED', 'TIMEOUT', 'CANCELLED'),
    allowNull: false,
    defaultValue: 'PENDING',
    comment: 'Transaction status'
  },
  responseCode: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Initial API response code'
  },
  responseDescription: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Initial API response description'
  },
  resultCode: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Final result code from callback'
  },
  resultDescription: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Final result description from callback'
  },
  mpesaReceiptNumber: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'M-Pesa receipt number for successful transactions'
  },
  transactionAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Actual transaction amount from M-Pesa callback'
  },
  transactionDate: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Transaction completion date from M-Pesa'
  },
  b2cCharges: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'B2C charges deducted'
  },
  receiverPartyName: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Name of the receiver from M-Pesa'
  },
  b2cUtilityBalance: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    comment: 'B2C utility account balance after transaction'
  },
  b2cWorkingBalance: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    comment: 'B2C working account balance after transaction'
  },
  orderId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'orders',
      key: 'id'
    },
    comment: 'Associated order ID for refunds'
  },
  userId: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'User ID who initiated the transaction'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Additional transaction metadata'
  },
  callbackData: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Full callback data from M-Pesa'
  },
  initiatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'When the transaction was initiated'
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When the transaction was completed'
  }
}, {
  tableName: 'b2c_transactions',
  timestamps: true,
  indexes: [
    {
      fields: ['conversationId'],
      unique: true
    },
    {
      fields: ['mpesaConversationId']
    },
    {
      fields: ['phoneNumber']
    },
    {
      fields: ['status']
    },
    {
      fields: ['transactionType']
    },
    {
      fields: ['orderId']
    },
    {
      fields: ['userId']
    },
    {
      fields: ['initiatedAt']
    },
    {
      fields: ['mpesaReceiptNumber']
    }
  ]
});

// Static methods for analytics and reporting
B2CTransaction.getTransactionStats = async function(startDate = null, endDate = null) {
  const { Op, fn, col, literal } = require('sequelize');
  
  const whereClause = {};
  if (startDate && endDate) {
    whereClause.initiatedAt = {
      [Op.between]: [startDate, endDate]
    };
  }

  const stats = await this.findAll({
    where: whereClause,
    attributes: [
      'transactionType',
      'status',
      [fn('COUNT', col('id')), 'count'],
      [fn('SUM', col('amount')), 'totalAmount'],
      [fn('AVG', col('amount')), 'averageAmount'],
      [fn('MIN', col('amount')), 'minAmount'],
      [fn('MAX', col('amount')), 'maxAmount']
    ],
    group: ['transactionType', 'status'],
    raw: true
  });

  // Calculate overall stats
  const overallStats = await this.findAll({
    where: whereClause,
    attributes: [
      [fn('COUNT', col('id')), 'totalTransactions'],
      [fn('SUM', col('amount')), 'totalAmount'],
      [fn('AVG', col('amount')), 'averageAmount'],
      [fn('COUNT', literal("CASE WHEN status = 'COMPLETED' THEN 1 END")), 'successfulTransactions'],
      [fn('COUNT', literal("CASE WHEN status = 'FAILED' THEN 1 END")), 'failedTransactions'],
      [fn('SUM', literal("CASE WHEN status = 'COMPLETED' THEN amount ELSE 0 END")), 'successfulAmount']
    ],
    raw: true
  });

  return {
    byTypeAndStatus: stats,
    overall: overallStats[0] || {},
    period: {
      startDate: startDate ? startDate.toISOString() : null,
      endDate: endDate ? endDate.toISOString() : null
    }
  };
};

// Instance methods
B2CTransaction.prototype.isCompleted = function() {
  return this.status === 'COMPLETED';
};

B2CTransaction.prototype.isFailed = function() {
  return ['FAILED', 'TIMEOUT', 'CANCELLED'].includes(this.status);
};

B2CTransaction.prototype.isPending = function() {
  return ['PENDING', 'SUBMITTED'].includes(this.status);
};

B2CTransaction.prototype.getDuration = function() {
  if (!this.completedAt) return null;
  return this.completedAt.getTime() - this.initiatedAt.getTime();
};

module.exports = B2CTransaction;
