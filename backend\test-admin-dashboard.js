const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

async function testAdminDashboard() {
  console.log('🔍 Testing Admin Dashboard Endpoint...\n');

  try {
    console.log('Testing: GET /admin/dashboard (without auth)');
    const response = await axios.get(`${API_BASE_URL}/admin/dashboard`, {
      validateStatus: () => true // Don't throw on any status
    });
    
    console.log(`Status: ${response.status}`);
    console.log(`Response:`, JSON.stringify(response.data, null, 2));
    
    if (response.status === 401) {
      console.log('\n✅ Good! The endpoint is properly protected.');
      console.log('💡 The frontend error suggests the authentication token might be invalid or expired.');
      console.log('\n🔧 Possible solutions:');
      console.log('1. Check if the user is properly logged in to Clerk');
      console.log('2. Verify the user has admin role in Clerk dashboard');
      console.log('3. Check browser console for authentication errors');
      console.log('4. Try logging out and logging back in');
    } else if (response.status === 500) {
      console.log('\n❌ Server error detected. Let me check what\'s causing it...');
      
      // Let's test the database queries directly
      console.log('\n🔍 Testing database queries...');
      
      try {
        const { Product, User, Order } = require('./src/db/models');
        
        console.log('Testing Product.count()...');
        const productCount = await Product.count({ where: { isActive: true } });
        console.log(`✅ Products: ${productCount}`);
        
        console.log('Testing User.count()...');
        const userCount = await User.count({ where: { role: 'customer' } });
        console.log(`✅ Users: ${userCount}`);
        
        console.log('Testing Order.count()...');
        const orderCount = await Order.count();
        console.log(`✅ Orders: ${orderCount}`);
        
        console.log('Testing Order.findAll()...');
        const recentOrders = await Order.findAll({
          limit: 10,
          order: [['createdAt', 'DESC']],
          include: [
            {
              model: User,
              attributes: ['id', 'name', 'email']
            }
          ]
        });
        console.log(`✅ Recent Orders: ${recentOrders.length} found`);
        
        console.log('\n✅ All database queries work fine!');
        console.log('💡 The 500 error might be due to authentication middleware issues.');
        
      } catch (dbError) {
        console.log(`❌ Database error: ${dbError.message}`);
      }
    }
    
  } catch (error) {
    console.log(`❌ Request failed: ${error.message}`);
  }
}

testAdminDashboard().catch(console.error);
