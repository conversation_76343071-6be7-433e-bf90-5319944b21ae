const express = require('express');
const router = express.Router();
const mpesaController = require('../controllers/mpesa.controller');
const { authenticateWithClerk, authenticateAdmin } = require('../middlewares/clerk-auth.middleware');
const { validateMpesaPayment, validateMpesaRefund } = require('../validators/mpesa.validator');
const { paymentLimiter } = require('../middlewares/rateLimiter.middleware');

/**
 * M-Pesa Payment Routes for G20Shop
 * Handles all M-Pesa payment operations including C2B and B2C
 */

// Import B2C routes (enhanced structure)
const b2cRoutes = require('./mpesa/b2c/index');

// Rate limiting for payment endpoints
const paymentRateLimit = paymentLimiter;

const rateLimit = require('express-rate-limit');
const callbackRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // Allow more callbacks from Safaricom
  skipSuccessfulRequests: true,
  message: {
    success: false,
    message: 'Too many callback requests, please try again later.',
    error: 'Rate limit exceeded'
  }
});

// Public endpoints (with authentication)

/**
 * @route   POST /api/mpesa/initiate-payment
 * @desc    Initiate STK Push payment for an order
 * @access  Private (Authenticated users)
 * @body    { orderId, phoneNumber, amount, accountReference?, transactionDesc? }
 */
router.post(
  '/initiate-payment',
  paymentRateLimit,
  authenticateWithClerk,
  validateMpesaPayment,
  mpesaController.initiatePayment
);

/**
 * @route   GET /api/mpesa/payment-status/:checkoutRequestId
 * @desc    Query payment status by checkout request ID
 * @access  Private (Authenticated users)
 */
router.get(
  '/payment-status/:checkoutRequestId',
  authenticateWithClerk,
  mpesaController.queryPaymentStatus
);

/**
 * @route   GET /api/mpesa/transaction-history/:orderId
 * @desc    Get transaction history for an order
 * @access  Private (Authenticated users - order owner or admin)
 */
router.get(
  '/transaction-history/:orderId',
  authenticateWithClerk,
  mpesaController.getTransactionHistory
);

// Webhook endpoints (no authentication - called by Safaricom)

/**
 * @route   POST /api/mpesa/callback/:orderId
 * @desc    Handle STK Push callback from Safaricom
 * @access  Public (Safaricom webhook)
 */
router.post(
  '/callback/:orderId',
  callbackRateLimit,
  mpesaController.stkPushCallback
);

/**
 * @route   POST /api/mpesa/confirmation
 * @desc    Handle C2B confirmation from Safaricom
 * @access  Public (Safaricom webhook)
 */
router.post(
  '/confirmation',
  callbackRateLimit,
  (req, res) => {
    // C2B confirmation endpoint
    console.log('📞 C2B Confirmation received:', req.body);

    // TODO: Process C2B confirmation
    // This is called when a customer pays via PayBill

    res.json({
      ResultCode: 0,
      ResultDesc: 'Confirmation received successfully'
    });
  }
);

/**
 * @route   POST /api/mpesa/validation
 * @desc    Handle C2B validation from Safaricom
 * @access  Public (Safaricom webhook)
 */
router.post(
  '/validation',
  callbackRateLimit,
  (req, res) => {
    // C2B validation endpoint
    console.log('📞 C2B Validation received:', req.body);

    // TODO: Implement validation logic
    // Return ResultCode 0 to accept, non-zero to reject

    res.json({
      ResultCode: 0,
      ResultDesc: 'Validation successful'
    });
  }
);

/**
 * @route   POST /api/mpesa/b2c/result
 * @desc    Handle B2C result callback from Safaricom
 * @access  Public (Safaricom webhook)
 */
router.post(
  '/b2c/result',
  callbackRateLimit,
  (req, res) => {
    // B2C result callback (for refunds)
    console.log('📞 B2C Result received:', req.body);

    // TODO: Process B2C result
    // Update refund transaction status

    res.json({
      ResultCode: 0,
      ResultDesc: 'Result received successfully'
    });
  }
);

/**
 * @route   POST /api/mpesa/b2c/timeout
 * @desc    Handle B2C timeout callback from Safaricom
 * @access  Public (Safaricom webhook)
 */
router.post(
  '/b2c/timeout',
  callbackRateLimit,
  (req, res) => {
    // B2C timeout callback
    console.log('📞 B2C Timeout received:', req.body);

    // TODO: Handle B2C timeout
    // Mark transaction as timed out

    res.json({
      ResultCode: 0,
      ResultDesc: 'Timeout received successfully'
    });
  }
);

/**
 * @route   POST /api/mpesa/transaction-status
 * @desc    Handle transaction status callback from Safaricom
 * @access  Public (Safaricom webhook)
 */
router.post(
  '/transaction-status',
  callbackRateLimit,
  (req, res) => {
    // Transaction status callback
    console.log('📞 Transaction Status received:', req.body);

    // TODO: Process transaction status

    res.json({
      ResultCode: 0,
      ResultDesc: 'Status received successfully'
    });
  }
);

/**
 * @route   POST /api/mpesa/transaction-status/timeout
 * @desc    Handle transaction status timeout callback from Safaricom
 * @access  Public (Safaricom webhook)
 */
router.post(
  '/transaction-status/timeout',
  callbackRateLimit,
  (req, res) => {
    // Transaction status timeout callback
    console.log('📞 Transaction Status Timeout received:', req.body);

    res.json({
      ResultCode: 0,
      ResultDesc: 'Timeout received successfully'
    });
  }
);

// Admin endpoints (require admin authentication)

/**
 * @route   POST /api/mpesa/admin/process-refund
 * @desc    Process refund for an order (Admin only)
 * @access  Private (Admin only)
 * @body    { orderId, amount, phoneNumber, reason? }
 */
router.post(
  '/admin/process-refund',
  authenticateAdmin,
  validateMpesaRefund,
  mpesaController.processRefund
);

/**
 * @route   GET /api/mpesa/admin/transactions
 * @desc    Get all M-Pesa transactions with filtering (Admin only)
 * @access  Private (Admin only)
 * @query   { status?, type?, startDate?, endDate?, page?, limit? }
 */
router.get(
  '/admin/transactions',
  authenticateAdmin,
  async (req, res) => {
    try {
      const MpesaTransaction = require('../../db/models/MpesaTransaction');
      const { status, type, startDate, endDate, page = 1, limit = 20 } = req.query;

      const where = {};
      if (status) where.status = status;
      if (type) where.transactionType = type;
      if (startDate && endDate) {
        where.createdAt = {
          [require('sequelize').Op.between]: [new Date(startDate), new Date(endDate)]
        };
      }

      const offset = (page - 1) * limit;

      const transactions = await MpesaTransaction.findAndCountAll({
        where,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['createdAt', 'DESC']],
        include: [
          {
            model: require('../../db/models/Order'),
            as: 'order',
            attributes: ['id', 'orderNumber', 'totalAmount']
          }
        ]
      });

      res.json({
        success: true,
        data: {
          transactions: transactions.rows,
          pagination: {
            total: transactions.count,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(transactions.count / limit)
          }
        }
      });
    } catch (error) {
      console.error('❌ Admin transactions query failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve transactions'
      });
    }
  }
);

/**
 * @route   GET /api/mpesa/admin/stats
 * @desc    Get M-Pesa transaction statistics (Admin only)
 * @access  Private (Admin only)
 * @query   { startDate?, endDate? }
 */
router.get(
  '/admin/stats',
  authenticateAdmin,
  async (req, res) => {
    try {
      const MpesaTransaction = require('../../db/models/MpesaTransaction');
      const { startDate, endDate } = req.query;

      const stats = await MpesaTransaction.getTransactionStats(
        startDate ? new Date(startDate) : null,
        endDate ? new Date(endDate) : null
      );

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('❌ Admin stats query failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve statistics'
      });
    }
  }
);

/**
 * @route   POST /api/mpesa/admin/register-urls
 * @desc    Register C2B URLs with Safaricom (Admin only)
 * @access  Private (Admin only)
 */
router.post(
  '/admin/register-urls',
  authenticateAdmin,
  async (req, res) => {
    try {
      const mpesaService = require('../../services/mpesa.service');
      const result = await mpesaService.registerC2BUrls();

      res.json({
        success: result.success,
        message: result.success ? 'URLs registered successfully' : 'Failed to register URLs',
        data: result.data,
        error: result.error
      });
    } catch (error) {
      console.error('❌ URL registration failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to register URLs'
      });
    }
  }
);

// Mount B2C routes
router.use('/b2c', b2cRoutes);

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'M-Pesa service is healthy',
    timestamp: new Date().toISOString(),
    environment: process.env.MPESA_ENVIRONMENT || 'sandbox',
    features: {
      c2b: true,
      b2c: true,
      stkPush: true,
      refunds: true
    }
  });
});

module.exports = router;
