{"name": "magic-patterns-vite-template", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:mpesa": "vitest --config tests/mpesa/vitest.config.ts"}, "dependencies": {"@clerk/clerk-react": "^4.32.5", "@types/xlsx": "^0.0.35", "axios": "^1.9.0", "lucide-react": "^0.441.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.1", "xlsx": "^0.18.5"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/node": "^20.11.18", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "autoprefixer": "latest", "eslint": "^8.50.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "jsdom": "^23.0.1", "postcss": "latest", "tailwindcss": "3.4.17", "typescript": "^5.5.4", "vite": "^5.2.0", "vitest": "^1.0.4"}}