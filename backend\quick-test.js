const http = require('http');

// Test different ports
const ports = [3000, 5000, 8000];

async function testPort(port) {
  return new Promise((resolve) => {
    const req = http.get(`http://localhost:${port}/api/products`, (res) => {
      console.log(`✅ Port ${port}: Server responding (${res.statusCode})`);
      resolve(true);
    });
    
    req.on('error', (err) => {
      console.log(`❌ Port ${port}: ${err.message}`);
      resolve(false);
    });
    
    req.setTimeout(2000, () => {
      console.log(`❌ Port ${port}: Timeout`);
      req.destroy();
      resolve(false);
    });
  });
}

async function findServer() {
  console.log('🔍 Looking for running server...\n');
  
  for (const port of ports) {
    await testPort(port);
  }
  
  console.log('\n📝 If your server is running on a different port, please let me know!');
}

findServer();
