const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

// Test API endpoints
async function testEndpoints() {
  console.log('🔍 Testing API Endpoints...\n');

  const tests = [
    {
      name: 'Health Check',
      method: 'GET',
      url: `${API_BASE_URL}/`,
      expectStatus: [200, 404] // 404 is acceptable if no root route
    },
    {
      name: 'Products List',
      method: 'GET',
      url: `${API_BASE_URL}/products`,
      expectStatus: [200]
    },
    {
      name: 'Product Categories',
      method: 'GET',
      url: `${API_BASE_URL}/products/categories`,
      expectStatus: [200]
    },
    {
      name: 'Users Profile (should fail without auth)',
      method: 'GET',
      url: `${API_BASE_URL}/users/profile`,
      expectStatus: [401, 403]
    },
    {
      name: 'Admin Dashboard (should fail without auth)',
      method: 'GET',
      url: `${API_BASE_URL}/admin/dashboard`,
      expectStatus: [401, 403]
    }
  ];

  for (const test of tests) {
    try {
      console.log(`Testing: ${test.name}`);
      const response = await axios({
        method: test.method,
        url: test.url,
        timeout: 5000,
        validateStatus: () => true // Don't throw on any status
      });

      const isExpected = test.expectStatus.includes(response.status);
      const status = isExpected ? '✅' : '❌';
      
      console.log(`${status} ${test.name}: ${response.status} ${response.statusText}`);
      
      if (response.data && typeof response.data === 'object') {
        if (response.data.message) {
          console.log(`   Message: ${response.data.message}`);
        }
        if (response.data.error) {
          console.log(`   Error: ${response.data.error}`);
        }
      }
      
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${test.name}: Server not running (Connection refused)`);
      } else {
        console.log(`❌ ${test.name}: ${error.message}`);
      }
    }
    console.log('');
  }
}

// Test database models
async function testDatabase() {
  console.log('🗄️  Testing Database Models...\n');
  
  try {
    const sequelize = require('./src/config/database');
    
    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection: OK');
    
    // Test models
    const { Product, User, Category } = require('./src/db/models');
    
    const productCount = await Product.count();
    console.log(`✅ Products table: ${productCount} records`);
    
    const userCount = await User.count();
    console.log(`✅ Users table: ${userCount} records`);
    
    const categoryCount = await Category.count();
    console.log(`✅ Categories table: ${categoryCount} records`);
    
  } catch (error) {
    console.log(`❌ Database error: ${error.message}`);
  }
}

// Main test function
async function runTests() {
  console.log('🚀 G20Shop Backend API Tests\n');
  console.log('=' .repeat(50));
  
  await testDatabase();
  console.log('\n' + '=' .repeat(50));
  await testEndpoints();
  
  console.log('=' .repeat(50));
  console.log('✨ Tests completed!');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testEndpoints, testDatabase, runTests };
