const couponService = require('../../services/coupon.service');
const { NotFoundError, ValidationError } = require('../../utils/errors');

/**
 * Create a new coupon (admin only)
 */
exports.createCoupon = async (req, res, next) => {
  try {
    const couponData = req.body;
    
    // Validate required fields
    if (!couponData.code) {
      throw new ValidationError('Coupon code is required');
    }
    
    if (!couponData.type) {
      throw new ValidationError('Coupon type is required');
    }
    
    if (!couponData.value && couponData.type !== 'free_shipping') {
      throw new ValidationError('Coupon value is required');
    }
    
    if (!couponData.startDate) {
      throw new ValidationError('Start date is required');
    }
    
    if (!couponData.endDate) {
      throw new ValidationError('End date is required');
    }
    
    const coupon = await couponService.createCoupon(couponData);
    
    res.status(201).json(coupon);
  } catch (error) {
    next(error);
  }
};

/**
 * Get all coupons (admin only)
 */
exports.getAllCoupons = async (req, res, next) => {
  try {
    const { page, limit, isActive } = req.query;
    
    const coupons = await couponService.getAllCoupons({
      page: parseInt(page) || 1,
      limit: parseInt(limit) || 10,
      isActive: isActive === 'true' ? true : isActive === 'false' ? false : undefined
    });
    
    res.status(200).json(coupons);
  } catch (error) {
    next(error);
  }
};

/**
 * Get coupon by ID (admin only)
 */
exports.getCoupon = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const coupon = await couponService.getCouponById(id);
    
    res.status(200).json(coupon);
  } catch (error) {
    next(error);
  }
};

/**
 * Update coupon (admin only)
 */
exports.updateCoupon = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    const coupon = await couponService.updateCoupon(id, updateData);
    
    res.status(200).json(coupon);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete coupon (admin only)
 */
exports.deleteCoupon = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    await couponService.deleteCoupon(id);
    
    res.status(204).send();
  } catch (error) {
    next(error);
  }
};

/**
 * Validate coupon (public)
 */
exports.validateCoupon = async (req, res, next) => {
  try {
    const { code, cartTotal, productIds } = req.body;
    const userId = req.user ? req.user.id : null;
    
    if (!code) {
      throw new ValidationError('Coupon code is required');
    }
    
    if (!cartTotal) {
      throw new ValidationError('Cart total is required');
    }
    
    const coupon = await couponService.validateCoupon(
      code, 
      parseFloat(cartTotal), 
      userId,
      productIds || []
    );
    
    res.status(200).json({
      valid: true,
      coupon: {
        code: coupon.code,
        type: coupon.type,
        value: coupon.value,
        discount: coupon.calculatedDiscount
      }
    });
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(400).json({
        valid: false,
        message: error.message
      });
    }
    next(error);
  }
};
