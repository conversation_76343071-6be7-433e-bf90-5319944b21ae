'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Coupons', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      code: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      type: {
        type: Sequelize.ENUM('percentage', 'fixed_amount', 'free_shipping'),
        allowNull: false
      },
      value: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0.00
      },
      minPurchase: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      maxDiscount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      startDate: {
        type: Sequelize.DATE,
        allowNull: false
      },
      endDate: {
        type: Sequelize.DATE,
        allowNull: false
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      usageLimit: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      usageCount: {
        type: Sequelize.INTEGER,
        defaultValue: 0
      },
      perUserLimit: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      applicableProducts: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Array of product IDs this coupon applies to'
      },
      applicableCategories: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Array of category IDs this coupon applies to'
      },
      excludedProducts: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Array of product IDs excluded from this coupon'
      },
      firstTimeOnly: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false
      }
    });

    // Add indexes
    await queryInterface.addIndex('Coupons', ['code']);
    await queryInterface.addIndex('Coupons', ['isActive']);
    await queryInterface.addIndex('Coupons', ['startDate', 'endDate']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('Coupons');
  }
};
