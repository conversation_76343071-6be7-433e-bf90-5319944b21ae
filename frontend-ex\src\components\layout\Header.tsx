import React, { useState } from 'react';
import { useAuth, useUser } from '@clerk/clerk-react';
import { MenuIcon, ShoppingCartIcon, UserIcon, SearchIcon } from 'lucide-react';
import { UserButton, SignInButton, SignUpButton } from '../auth';

export const Header = () => {
  const { isSignedIn, isLoaded } = useAuth();
  const { user } = useUser();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const userRole = user?.publicMetadata?.role as string || 'customer';

  return <header className="bg-[#0a0a0a] sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <a href="/" className="text-[#00a8ff] font-bold text-xl hover:text-[#0090e0] transition-colors duration-200">techgear</a>
            </div>
            <nav className="hidden md:block ml-10">
              <div className="flex space-x-6">
                <a href="/" className="text-gray-300 hover:text-[#00a8ff] px-3 py-2 text-sm font-medium">
                  Home
                </a>
                <a href="/products" className="text-gray-300 hover:text-[#00a8ff] px-3 py-2 text-sm font-medium">
                  Products
                </a>
                <a href="/support" className="text-gray-300 hover:text-[#00a8ff] px-3 py-2 text-sm font-medium">
                  Support
                </a>
                <a href="/about" className="text-gray-300 hover:text-[#00a8ff] px-3 py-2 text-sm font-medium">
                  About
                </a>
                <a href="/contact" className="text-gray-300 hover:text-[#00a8ff] px-3 py-2 text-sm font-medium">
                  Contact
                </a>
                {isSignedIn && userRole === 'admin' && (
                  <a href="/admin" className="text-gray-300 hover:text-[#00a8ff] px-3 py-2 text-sm font-medium">
                    Admin
                  </a>
                )}
              </div>
            </nav>
          </div>
          <div className="hidden md:flex items-center space-x-4">
            <a href="/search" className="text-gray-300 hover:text-white">
              <SearchIcon className="h-5 w-5" />
            </a>

            {/* Authentication Section */}
            {!isLoaded ? (
              <div className="animate-pulse bg-gray-700 h-8 w-20 rounded"></div>
            ) : isSignedIn ? (
              <>
                <a href="/cart" className="text-gray-300 hover:text-white">
                  <ShoppingCartIcon className="h-5 w-5" />
                </a>
                <UserButton />
              </>
            ) : (
              <div className="flex items-center space-x-2">
                <SignInButton />
                <SignUpButton />
              </div>
            )}
          </div>
          <div className="md:hidden">
            <button
              className="text-gray-300 hover:text-white"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              <MenuIcon className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-900">
              <a href="/" className="text-gray-300 hover:text-[#00a8ff] block px-3 py-2 text-base font-medium">
                Home
              </a>
              <a href="/products" className="text-gray-300 hover:text-[#00a8ff] block px-3 py-2 text-base font-medium">
                Products
              </a>
              <a href="/support" className="text-gray-300 hover:text-[#00a8ff] block px-3 py-2 text-base font-medium">
                Support
              </a>
              <a href="/about" className="text-gray-300 hover:text-[#00a8ff] block px-3 py-2 text-base font-medium">
                About
              </a>
              <a href="/contact" className="text-gray-300 hover:text-[#00a8ff] block px-3 py-2 text-base font-medium">
                Contact
              </a>
              {isSignedIn && userRole === 'admin' && (
                <a href="/admin" className="text-gray-300 hover:text-[#00a8ff] block px-3 py-2 text-base font-medium">
                  Admin
                </a>
              )}

              {/* Mobile Authentication */}
              <div className="border-t border-gray-700 pt-4">
                {!isLoaded ? (
                  <div className="animate-pulse bg-gray-700 h-8 w-20 rounded mx-3"></div>
                ) : isSignedIn ? (
                  <div className="flex items-center justify-between px-3">
                    <span className="text-gray-300">
                      {user?.firstName || user?.emailAddresses[0]?.emailAddress}
                    </span>
                    <UserButton />
                  </div>
                ) : (
                  <div className="flex flex-col space-y-2 px-3">
                    <SignInButton />
                    <SignUpButton />
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </header>;
};