const { DataTypes } = require('sequelize');
const sequelize = require('../../config/database');

/**
 * M-Pesa Transaction Model
 * Stores all M-Pesa transaction records for audit and tracking
 */
const MpesaTransaction = sequelize.define('MpesaTransaction', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  orderId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'Orders',
      key: 'id'
    },
    comment: 'Reference to the order this transaction belongs to'
  },
  merchantRequestId: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Merchant request ID from M-Pesa STK Push'
  },
  checkoutRequestId: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true,
    comment: 'Checkout request ID from M-Pesa STK Push'
  },
  mpesaReceiptNumber: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true,
    comment: 'M-Pesa receipt number for successful transactions'
  },
  transactionDate: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Date and time when the transaction was completed on M-Pesa'
  },
  phoneNumber: {
    type: DataTypes.STRING(15),
    allowNull: false,
    validate: {
      is: /^254[17]\d{8}$/,
      notEmpty: true
    },
    comment: 'Customer phone number in format 254XXXXXXXXX'
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 1,
      max: 70000
    },
    comment: 'Transaction amount in KES'
  },
  transactionType: {
    type: DataTypes.ENUM(
      'STK_PUSH',
      'C2B',
      'B2C_REFUND',
      'B2B',
      'REVERSAL'
    ),
    allowNull: false,
    defaultValue: 'STK_PUSH',
    comment: 'Type of M-Pesa transaction'
  },
  status: {
    type: DataTypes.ENUM(
      'PENDING',
      'SUCCESS',
      'FAILED',
      'CANCELLED',
      'TIMEOUT'
    ),
    allowNull: false,
    defaultValue: 'PENDING',
    comment: 'Current status of the transaction'
  },
  resultCode: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Result code from M-Pesa callback (0 = success)'
  },
  resultDesc: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Result description from M-Pesa callback'
  },
  callbackData: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Full callback data from M-Pesa for audit purposes'
  },
  conversationId: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Conversation ID for B2C and other async transactions'
  },
  originatorConversationId: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Originator conversation ID for tracking'
  },
  accountReference: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Account reference used in the transaction'
  },
  transactionDesc: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Transaction description'
  },
  initiatorName: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Name of the transaction initiator (for B2C, B2B)'
  },
  remarks: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Additional remarks or notes about the transaction'
  },
  retryCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Number of retry attempts for failed transactions'
  },
  lastRetryAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Timestamp of last retry attempt'
  },
  processedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Timestamp when transaction was fully processed'
  },
  isReconciled: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this transaction has been reconciled with M-Pesa statements'
  },
  reconciledAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Timestamp when transaction was reconciled'
  },
  environment: {
    type: DataTypes.ENUM('sandbox', 'production'),
    allowNull: false,
    defaultValue: 'sandbox',
    comment: 'Environment where transaction was processed'
  }
}, {
  tableName: 'mpesa_transactions',
  timestamps: true,
  indexes: [
    {
      fields: ['orderId']
    },
    {
      fields: ['checkoutRequestId'],
      unique: true,
      where: {
        checkoutRequestId: {
          [sequelize.Sequelize.Op.ne]: null
        }
      }
    },
    {
      fields: ['mpesaReceiptNumber'],
      unique: true,
      where: {
        mpesaReceiptNumber: {
          [sequelize.Sequelize.Op.ne]: null
        }
      }
    },
    {
      fields: ['phoneNumber']
    },
    {
      fields: ['status']
    },
    {
      fields: ['transactionType']
    },
    {
      fields: ['transactionDate']
    },
    {
      fields: ['createdAt']
    },
    {
      fields: ['environment']
    }
  ],
  hooks: {
    beforeCreate: (transaction) => {
      // Set environment from config
      transaction.environment = process.env.MPESA_ENVIRONMENT || 'sandbox';
      
      // Format phone number
      if (transaction.phoneNumber && transaction.phoneNumber.startsWith('0')) {
        transaction.phoneNumber = `254${transaction.phoneNumber.substring(1)}`;
      }
    },
    beforeUpdate: (transaction) => {
      // Update processedAt when status changes to SUCCESS or FAILED
      if (transaction.changed('status') && 
          ['SUCCESS', 'FAILED', 'CANCELLED'].includes(transaction.status) &&
          !transaction.processedAt) {
        transaction.processedAt = new Date();
      }
    }
  }
});

// Instance methods
MpesaTransaction.prototype.isSuccessful = function() {
  return this.status === 'SUCCESS' && this.resultCode === 0;
};

MpesaTransaction.prototype.isFailed = function() {
  return this.status === 'FAILED' || (this.resultCode && this.resultCode !== 0);
};

MpesaTransaction.prototype.isPending = function() {
  return this.status === 'PENDING';
};

MpesaTransaction.prototype.canRetry = function() {
  return this.isFailed() && this.retryCount < 3;
};

MpesaTransaction.prototype.getFormattedAmount = function() {
  return `KES ${parseFloat(this.amount).toLocaleString('en-KE', { minimumFractionDigits: 2 })}`;
};

MpesaTransaction.prototype.getFormattedPhone = function() {
  if (this.phoneNumber.startsWith('254')) {
    return `+${this.phoneNumber}`;
  }
  return this.phoneNumber;
};

MpesaTransaction.prototype.getDurationMinutes = function() {
  if (!this.processedAt) return null;
  const diffMs = new Date(this.processedAt) - new Date(this.createdAt);
  return Math.round(diffMs / (1000 * 60));
};

// Static methods
MpesaTransaction.findByCheckoutRequestId = function(checkoutRequestId) {
  return this.findOne({
    where: { checkoutRequestId }
  });
};

MpesaTransaction.findByMpesaReceipt = function(mpesaReceiptNumber) {
  return this.findOne({
    where: { mpesaReceiptNumber }
  });
};

MpesaTransaction.findByOrderId = function(orderId) {
  return this.findAll({
    where: { orderId },
    order: [['createdAt', 'DESC']]
  });
};

MpesaTransaction.getSuccessfulTransactions = function(startDate, endDate) {
  const where = {
    status: 'SUCCESS',
    resultCode: 0
  };
  
  if (startDate && endDate) {
    where.transactionDate = {
      [sequelize.Sequelize.Op.between]: [startDate, endDate]
    };
  }
  
  return this.findAll({
    where,
    order: [['transactionDate', 'DESC']]
  });
};

MpesaTransaction.getPendingTransactions = function() {
  return this.findAll({
    where: {
      status: 'PENDING',
      createdAt: {
        // Find transactions older than 5 minutes
        [sequelize.Sequelize.Op.lt]: new Date(Date.now() - 5 * 60 * 1000)
      }
    },
    order: [['createdAt', 'ASC']]
  });
};

MpesaTransaction.getTransactionStats = async function(startDate, endDate) {
  const where = {};
  
  if (startDate && endDate) {
    where.createdAt = {
      [sequelize.Sequelize.Op.between]: [startDate, endDate]
    };
  }
  
  const stats = await this.findAll({
    where,
    attributes: [
      'status',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      [sequelize.fn('SUM', sequelize.col('amount')), 'totalAmount']
    ],
    group: ['status'],
    raw: true
  });
  
  return stats.reduce((acc, stat) => {
    acc[stat.status] = {
      count: parseInt(stat.count),
      totalAmount: parseFloat(stat.totalAmount || 0)
    };
    return acc;
  }, {});
};

module.exports = MpesaTransaction;
