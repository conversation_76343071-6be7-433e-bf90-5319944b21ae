# G20 Shop Frontend Components

This document provides information about the components used in the G20 Shop frontend application.

## Component Structure

Each component follows a consistent structure:

```tsx
import React from 'react';
import './ComponentName.css'; // If using CSS modules

interface ComponentNameProps {
  // Props definition
}

export const ComponentName: React.FC<ComponentNameProps> = ({ prop1, prop2 }) => {
  // Component logic
  
  return (
    <div className="component-name">
      {/* Component JSX */}
    </div>
  );
};
```

## Layout Components

### Header

The `Header` component is the main navigation header for the application.

**Props:** None

**Usage:**

```tsx
import { Header } from './components/layout/Header';

function App() {
  return (
    <div className="app">
      <Header />
      {/* Other components */}
    </div>
  );
}
```

### Footer

The `Footer` component is the footer section of the application.

**Props:** None

**Usage:**

```tsx
import { Footer } from './components/layout/Footer';

function App() {
  return (
    <div className="app">
      {/* Other components */}
      <Footer />
    </div>
  );
}
```

## Product Components

### ProductCard

The `ProductCard` component displays a product card with basic information.

**Props:**

- `product`: Product object with the following properties:
  - `id`: Product ID
  - `name`: Product name
  - `price`: Product price
  - `image`: Product image URL
  - `slug`: Product slug for routing

**Usage:**

```tsx
import { ProductCard } from './components/products/ProductCard';

function ProductsPage() {
  const product = {
    id: 1,
    name: 'Wireless Mouse',
    price: 29.99,
    image: 'https://example.com/images/mouse1.jpg',
    slug: 'wireless-mouse'
  };

  return (
    <div className="products-page">
      <ProductCard product={product} />
    </div>
  );
}
```

### ProductDetail

The `ProductDetail` component displays detailed information about a product.

**Props:**

- `product`: Product object with detailed information

**Usage:**

```tsx
import { ProductDetail } from './components/products/ProductDetail';

function ProductPage() {
  const product = {
    id: 1,
    name: 'Wireless Mouse',
    price: 29.99,
    description: 'High-performance wireless mouse',
    images: [
      { id: 1, url: 'https://example.com/images/mouse1.jpg' }
    ],
    categories: [
      { id: 1, name: 'Accessories' }
    ]
  };

  return (
    <div className="product-page">
      <ProductDetail product={product} />
    </div>
  );
}
```

## Form Components

### SearchBar

The `SearchBar` component provides a search input for searching products.

**Props:**

- `onSearch`: Function to call when search is submitted

**Usage:**

```tsx
import { SearchBar } from './components/common/SearchBar';

function Header() {
  const handleSearch = (query) => {
    // Handle search
  };

  return (
    <header className="header">
      <SearchBar onSearch={handleSearch} />
    </header>
  );
}
```

## Feature Components

### QuickView

The `QuickView` component displays a quick view modal for a product.

**Props:**

- `product`: Product object
- `isOpen`: Boolean indicating if the modal is open
- `onClose`: Function to call when the modal is closed

**Usage:**

```tsx
import { QuickView } from './components/products/QuickView';

function ProductsPage() {
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isQuickViewOpen, setIsQuickViewOpen] = useState(false);

  const handleQuickView = (product) => {
    setSelectedProduct(product);
    setIsQuickViewOpen(true);
  };

  const handleCloseQuickView = () => {
    setIsQuickViewOpen(false);
  };

  return (
    <div className="products-page">
      {/* Product cards */}
      {selectedProduct && (
        <QuickView
          product={selectedProduct}
          isOpen={isQuickViewOpen}
          onClose={handleCloseQuickView}
        />
      )}
    </div>
  );
}
```

### RecentlyViewedProducts

The `RecentlyViewedProducts` component displays recently viewed products.

**Props:** None (uses context internally)

**Usage:**

```tsx
import { RecentlyViewedProducts } from './components/products/RecentlyViewedProducts';

function ProductPage() {
  return (
    <div className="product-page">
      {/* Product detail */}
      <RecentlyViewedProducts />
    </div>
  );
}
```
