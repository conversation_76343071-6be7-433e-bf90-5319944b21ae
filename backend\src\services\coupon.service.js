const { Coupon, CouponUsage, Order, Product, Category } = require('../db/models');
const { NotFoundError, ValidationError } = require('../utils/errors');
const { Op } = require('sequelize');

/**
 * Create a new coupon
 */
exports.createCoupon = async (couponData) => {
  // Check if coupon code already exists
  const existingCoupon = await Coupon.findOne({
    where: { code: couponData.code }
  });
  
  if (existingCoupon) {
    throw new ValidationError('Coupon code already exists');
  }
  
  // Validate coupon data
  if (couponData.type === 'percentage' && (couponData.value <= 0 || couponData.value > 100)) {
    throw new ValidationError('Percentage discount must be between 1 and 100');
  }
  
  if (couponData.type === 'fixed_amount' && couponData.value <= 0) {
    throw new ValidationError('Fixed amount discount must be greater than 0');
  }
  
  if (new Date(couponData.startDate) >= new Date(couponData.endDate)) {
    throw new ValidationError('End date must be after start date');
  }
  
  // Create coupon
  const coupon = await Coupon.create(couponData);
  
  return coupon;
};

/**
 * Get all coupons
 */
exports.getAllCoupons = async ({ page = 1, limit = 10, isActive }) => {
  const offset = (page - 1) * limit;
  const where = {};
  
  if (isActive !== undefined) {
    where.isActive = isActive;
  }
  
  const { count, rows } = await Coupon.findAndCountAll({
    where,
    order: [['createdAt', 'DESC']],
    limit,
    offset
  });
  
  return {
    coupons: rows,
    totalItems: count,
    totalPages: Math.ceil(count / limit),
    currentPage: page
  };
};

/**
 * Get coupon by ID
 */
exports.getCouponById = async (id) => {
  const coupon = await Coupon.findByPk(id, {
    include: [
      {
        model: CouponUsage,
        as: 'usages',
        limit: 10,
        order: [['createdAt', 'DESC']]
      }
    ]
  });
  
  if (!coupon) {
    throw new NotFoundError('Coupon not found');
  }
  
  return coupon;
};

/**
 * Update coupon
 */
exports.updateCoupon = async (id, updateData) => {
  const coupon = await Coupon.findByPk(id);
  
  if (!coupon) {
    throw new NotFoundError('Coupon not found');
  }
  
  // Validate coupon code if changing
  if (updateData.code && updateData.code !== coupon.code) {
    const existingCoupon = await Coupon.findOne({
      where: { code: updateData.code }
    });
    
    if (existingCoupon) {
      throw new ValidationError('Coupon code already exists');
    }
  }
  
  // Validate percentage value
  if (updateData.type === 'percentage' && updateData.value !== undefined) {
    if (updateData.value <= 0 || updateData.value > 100) {
      throw new ValidationError('Percentage discount must be between 1 and 100');
    }
  }
  
  // Validate fixed amount value
  if (updateData.type === 'fixed_amount' && updateData.value !== undefined) {
    if (updateData.value <= 0) {
      throw new ValidationError('Fixed amount discount must be greater than 0');
    }
  }
  
  // Validate dates
  if (updateData.startDate && updateData.endDate) {
    if (new Date(updateData.startDate) >= new Date(updateData.endDate)) {
      throw new ValidationError('End date must be after start date');
    }
  } else if (updateData.startDate && !updateData.endDate) {
    if (new Date(updateData.startDate) >= new Date(coupon.endDate)) {
      throw new ValidationError('Start date must be before current end date');
    }
  } else if (!updateData.startDate && updateData.endDate) {
    if (new Date(coupon.startDate) >= new Date(updateData.endDate)) {
      throw new ValidationError('Current start date must be before end date');
    }
  }
  
  // Update coupon
  await coupon.update(updateData);
  
  return this.getCouponById(id);
};

/**
 * Delete coupon
 */
exports.deleteCoupon = async (id) => {
  const coupon = await Coupon.findByPk(id);
  
  if (!coupon) {
    throw new NotFoundError('Coupon not found');
  }
  
  await coupon.destroy();
  
  return true;
};

/**
 * Validate coupon for use
 */
exports.validateCoupon = async (code, cartTotal, userId, productIds = []) => {
  // Find coupon by code
  const coupon = await Coupon.findOne({
    where: { 
      code,
      isActive: true,
      startDate: { [Op.lte]: new Date() },
      endDate: { [Op.gte]: new Date() }
    }
  });
  
  if (!coupon) {
    throw new ValidationError('Invalid or expired coupon code');
  }
  
  // Check minimum purchase requirement
  if (coupon.minPurchase && cartTotal < coupon.minPurchase) {
    throw new ValidationError(`Minimum purchase amount of $${coupon.minPurchase} required`);
  }
  
  // Check usage limit
  if (coupon.usageLimit && coupon.usageCount >= coupon.usageLimit) {
    throw new ValidationError('Coupon usage limit has been reached');
  }
  
  // Check per-user limit if user is logged in
  if (userId && coupon.perUserLimit) {
    const userUsageCount = await CouponUsage.count({
      where: { 
        couponId: coupon.id,
        userId
      }
    });
    
    if (userUsageCount >= coupon.perUserLimit) {
      throw new ValidationError('You have reached the usage limit for this coupon');
    }
  }
  
  // Check first-time only restriction
  if (userId && coupon.firstTimeOnly) {
    const hasOrders = await Order.count({
      where: { userId }
    });
    
    if (hasOrders > 0) {
      throw new ValidationError('This coupon is for first-time customers only');
    }
  }
  
  // Check product restrictions
  if (coupon.applicableProducts && coupon.applicableProducts.length > 0) {
    const hasValidProduct = productIds.some(id => 
      coupon.applicableProducts.includes(id)
    );
    
    if (!hasValidProduct) {
      throw new ValidationError('This coupon is not applicable to the products in your cart');
    }
  }
  
  // Check excluded products
  if (coupon.excludedProducts && coupon.excludedProducts.length > 0) {
    const hasExcludedProduct = productIds.some(id => 
      coupon.excludedProducts.includes(id)
    );
    
    if (hasExcludedProduct) {
      throw new ValidationError('This coupon cannot be used with some products in your cart');
    }
  }
  
  // Calculate discount amount
  let calculatedDiscount = 0;
  
  if (coupon.type === 'percentage') {
    calculatedDiscount = (cartTotal * coupon.value) / 100;
  } else if (coupon.type === 'fixed_amount') {
    calculatedDiscount = coupon.value;
  } else if (coupon.type === 'free_shipping') {
    // Free shipping is handled separately
    calculatedDiscount = 0;
  }
  
  // Apply maximum discount limit if set
  if (coupon.maxDiscount && calculatedDiscount > coupon.maxDiscount) {
    calculatedDiscount = coupon.maxDiscount;
  }
  
  return {
    ...coupon.toJSON(),
    calculatedDiscount
  };
};
