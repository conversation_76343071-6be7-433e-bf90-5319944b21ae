const { Product, Category, ProductImage } = require('../src/db/models');

async function checkProducts() {
  try {
    console.log('🔍 Checking existing products in database...\n');
    
    // Get all products
    const products = await Product.findAll({
      include: [
        {
          model: ProductImage,
          as: 'images',
          where: { isPrimary: true },
          required: false,
          limit: 1
        }
      ],
      limit: 10
    });
    
    if (products.length === 0) {
      console.log('❌ No products found in database');
      console.log('\n💡 You can add test products by running:');
      console.log('   node src/db/seeders/add-test-products.js');
    } else {
      console.log(`✅ Found ${products.length} products:\n`);
      
      products.forEach((product, index) => {
        console.log(`${index + 1}. Product ID: ${product.id}`);
        console.log(`   Name: ${product.name}`);
        console.log(`   SKU: ${product.sku}`);
        console.log(`   Price: $${product.regularPrice}`);
        console.log(`   Stock: ${product.stockQuantity}`);
        console.log(`   Active: ${product.isActive}`);
        console.log('');
      });
      
      console.log('💡 You can test the cart with these product IDs in the frontend.');
    }
    
    // Also check categories
    const categories = await Category.findAll({ limit: 5 });
    console.log(`\n📂 Found ${categories.length} categories:`);
    categories.forEach(cat => {
      console.log(`   - ${cat.name} (slug: ${cat.slug})`);
    });
    
  } catch (error) {
    console.error('❌ Error checking products:', error.message);
  } finally {
    process.exit(0);
  }
}

// Run if called directly
if (require.main === module) {
  checkProducts();
}

module.exports = checkProducts;
