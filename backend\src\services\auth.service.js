const jwt = require('jsonwebtoken');
const { User } = require('../db/models');
const { AuthenticationError, NotFoundError, ValidationError } = require('../utils/errors');
const config = require('../config');

/**
 * Register a new user
 */
exports.register = async (userData) => {
  // Check if email already exists
  const existingUser = await User.findOne({ where: { email: userData.email } });
  
  if (existingUser) {
    throw new ValidationError('Email already in use');
  }
  
  // Create new user
  const user = await User.create({
    ...userData,
    role: 'customer' // Default role for new registrations
  });
  
  return user;
};

/**
 * Login user
 */
exports.login = async (email, password) => {
  // Find user by email
  const user = await User.findOne({ where: { email } });
  
  if (!user) {
    throw new AuthenticationError('Invalid email or password');
  }
  
  // Check if user is active
  if (!user.isActive) {
    throw new AuthenticationError('Account is disabled');
  }
  
  // Verify password
  const isPasswordValid = await user.checkPassword(password);
  
  if (!isPasswordValid) {
    throw new AuthenticationError('Invalid email or password');
  }
  
  // Update last login timestamp
  await user.update({ lastLogin: new Date() });
  
  // Generate tokens
  const token = generateAccessToken(user);
  const refreshToken = generateRefreshToken(user);
  
  return { user, token, refreshToken };
};

/**
 * Refresh access token
 */
exports.refreshToken = async (refreshToken) => {
  try {
    // Verify refresh token
    const decoded = jwt.verify(refreshToken, config.jwt.refreshSecret);
    
    // Find user
    const user = await User.findByPk(decoded.id);
    
    if (!user || !user.isActive) {
      throw new AuthenticationError('Invalid refresh token');
    }
    
    // Generate new tokens
    const token = generateAccessToken(user);
    const newRefreshToken = generateRefreshToken(user);
    
    return { token, newRefreshToken };
  } catch (error) {
    throw new AuthenticationError('Invalid refresh token');
  }
};

/**
 * Get user by ID
 */
exports.getUserById = async (id) => {
  const user = await User.findByPk(id);
  
  if (!user) {
    throw new NotFoundError('User not found');
  }
  
  return user;
};

/**
 * Update user
 */
exports.updateUser = async (id, updateData) => {
  const user = await User.findByPk(id);
  
  if (!user) {
    throw new NotFoundError('User not found');
  }
  
  await user.update(updateData);
  
  return user;
};

/**
 * Change password
 */
exports.changePassword = async (id, currentPassword, newPassword) => {
  const user = await User.findByPk(id);
  
  if (!user) {
    throw new NotFoundError('User not found');
  }
  
  // Verify current password
  const isPasswordValid = await user.checkPassword(currentPassword);
  
  if (!isPasswordValid) {
    throw new ValidationError('Current password is incorrect');
  }
  
  // Update password
  await user.update({ password: newPassword });
  
  return true;
};

/**
 * Generate access token
 */
const generateAccessToken = (user) => {
  return jwt.sign(
    {
      id: user.id,
      email: user.email,
      role: user.role
    },
    config.jwt.secret,
    { expiresIn: config.jwt.expiresIn }
  );
};

/**
 * Generate refresh token
 */
const generateRefreshToken = (user) => {
  return jwt.sign(
    { id: user.id },
    config.jwt.refreshSecret,
    { expiresIn: config.jwt.refreshExpiresIn }
  );
};
