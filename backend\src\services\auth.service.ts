import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { User, IUserDocument } from '../models/User';
import { IUser, UserRole, JwtPayload } from '../types';
import config from '../config';
import { logger } from '../utils/logger';
import {
  AuthenticationError,
  ValidationError,
  NotFoundError,
  ConflictError,
  ErrorCode,
} from '../utils/errors';

/**
 * User registration data interface
 */
interface RegisterUserData {
  name: string;
  email: string;
  password: string;
  phone?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
  };
}

/**
 * Token response interface
 */
interface TokenResponse {
  token: string;
  refreshToken: string;
  expiresIn: string;
}

/**
 * Authentication service for user management and authentication
 */
class AuthService {
  /**
   * Register a new user
   * 
   * @param userData - User registration data
   * @returns Newly created user
   */
  async register(userData: RegisterUserData): Promise<IUserDocument> {
    try {
      // Check if email already exists
      const existingUser = await User.findOne({ email: userData.email });
      
      if (existingUser) {
        logger.warn(`Registration attempt with existing email: ${userData.email}`);
        throw new ConflictError(
          'Email already in use',
          ErrorCode.EMAIL_IN_USE
        );
      }
      
      // Create new user with default role
      const user = await User.create({
        ...userData,
        role: UserRole.CUSTOMER, // Default role
        isActive: true,
      });
      
      logger.info(`New user registered: ${user.email}`);
      
      return user;
    } catch (error) {
      // Re-throw custom errors
      if (error instanceof ConflictError) {
        throw error;
      }
      
      logger.error(`User registration error: ${error.message}`);
      throw new ValidationError(
        'Failed to register user',
        ErrorCode.INVALID_DATA
      );
    }
  }
  
  /**
   * Login user and generate tokens
   * 
   * @param email - User email
   * @param password - User password
   * @returns User and tokens
   */
  async login(email: string, password: string): Promise<{
    user: IUserDocument;
    token: string;
    refreshToken: string;
  }> {
    try {
      // Find user by email
      const user = await User.findOne({ email });
      
      // Check if user exists
      if (!user) {
        logger.warn(`Login attempt with non-existent email: ${email}`);
        throw new AuthenticationError(
          'Invalid email or password',
          ErrorCode.INVALID_CREDENTIALS
        );
      }
      
      // Check if user is active
      if (!user.isActive) {
        logger.warn(`Login attempt for inactive user: ${email}`);
        throw new AuthenticationError(
          'Account is disabled',
          ErrorCode.USER_INACTIVE
        );
      }
      
      // Verify password
      const isPasswordValid = await user.checkPassword(password);
      
      if (!isPasswordValid) {
        logger.warn(`Login attempt with invalid password for user: ${email}`);
        throw new AuthenticationError(
          'Invalid email or password',
          ErrorCode.INVALID_CREDENTIALS
        );
      }
      
      // Update last login timestamp
      user.lastLogin = new Date();
      await user.save();
      
      // Generate tokens
      const token = this.generateAccessToken(user);
      const refreshToken = this.generateRefreshToken(user);
      
      logger.info(`User logged in: ${user.email}`);
      
      return { user, token, refreshToken };
    } catch (error) {
      // Re-throw custom errors
      if (error instanceof AuthenticationError) {
        throw error;
      }
      
      logger.error(`Login error: ${error.message}`);
      throw new AuthenticationError(
        'Authentication failed',
        ErrorCode.INVALID_CREDENTIALS
      );
    }
  }
  
  /**
   * Refresh access token using refresh token
   * 
   * @param refreshToken - Refresh token
   * @returns New access and refresh tokens
   */
  async refreshToken(refreshToken: string): Promise<TokenResponse> {
    try {
      // Verify refresh token
      const decoded = jwt.verify(
        refreshToken,
        config.jwt.refreshSecret
      ) as JwtPayload;
      
      // Find user
      const user = await User.findById(decoded.id);
      
      if (!user || !user.isActive) {
        logger.warn(`Token refresh attempt for invalid user ID: ${decoded.id}`);
        throw new AuthenticationError(
          'Invalid refresh token',
          ErrorCode.TOKEN_INVALID
        );
      }
      
      // Generate new tokens
      const token = this.generateAccessToken(user);
      const newRefreshToken = this.generateRefreshToken(user);
      
      logger.info(`Token refreshed for user: ${user.email}`);
      
      return {
        token,
        refreshToken: newRefreshToken,
        expiresIn: config.jwt.expiresIn,
      };
    } catch (error) {
      if (error instanceof AuthenticationError) {
        throw error;
      }
      
      logger.error(`Token refresh error: ${error.message}`);
      throw new AuthenticationError(
        'Invalid refresh token',
        ErrorCode.TOKEN_INVALID
      );
    }
  }
  
  /**
   * Get user by ID
   * 
   * @param id - User ID
   * @returns User document
   */
  async getUserById(id: string): Promise<IUserDocument> {
    try {
      const user = await User.findById(id);
      
      if (!user) {
        logger.warn(`User not found with ID: ${id}`);
        throw new NotFoundError(
          'User not found',
          ErrorCode.USER_NOT_FOUND
        );
      }
      
      return user;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      
      logger.error(`Error fetching user by ID: ${error.message}`);
      throw new NotFoundError(
        'User not found',
        ErrorCode.USER_NOT_FOUND
      );
    }
  }
  
  /**
   * Update user profile
   * 
   * @param id - User ID
   * @param updateData - User profile data to update
   * @returns Updated user document
   */
  async updateUser(id: string, updateData: Partial<IUser>): Promise<IUserDocument> {
    try {
      // Ensure sensitive fields cannot be updated
      const safeUpdateData = this.getSafeUpdateData(updateData);
      
      const user = await User.findByIdAndUpdate(
        id,
        { $set: safeUpdateData },
        { new: true, runValidators: true }
      );
      
      if (!user) {
        logger.warn(`Update attempt for non-existent user ID: ${id}`);
        throw new NotFoundError(
          'User not found',
          ErrorCode.USER_NOT_FOUND
        );
      }
      
      logger.info(`User updated: ${user.email}`);
      
      return user;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      
      logger.error(`User update error: ${error.message}`);
      throw new ValidationError(
        'Failed to update user',
        ErrorCode.INVALID_DATA
      );
    }
  }
  
  /**
   * Change user password
   * 
   * @param id - User ID
   * @param currentPassword - Current password
   * @param newPassword - New password
   * @returns Success status
   */
  async changePassword(
    id: string,
    currentPassword: string,
    newPassword: string
  ): Promise<boolean> {
    try {
      // Find user
      const user = await User.findById(id);
      
      if (!user) {
        logger.warn(`Password change attempt for non-existent user ID: ${id}`);
        throw new NotFoundError(
          'User not found',
          ErrorCode.USER_NOT_FOUND
        );
      }
      
      // Verify current password
      const isPasswordValid = await user.checkPassword(currentPassword);
      
      if (!isPasswordValid) {
        logger.warn(`Password change attempt with invalid current password for user: ${user.email}`);
        throw new ValidationError(
          'Current password is incorrect',
          ErrorCode.INVALID_PASSWORD
        );
      }
      
      // Update password
      user.password = newPassword;
      await user.save();
      
      logger.info(`Password changed for user: ${user.email}`);
      
      return true;
    } catch (error) {
      if (error instanceof NotFoundError || error instanceof ValidationError) {
        throw error;
      }
      
      logger.error(`Password change error: ${error.message}`);
      throw new ValidationError(
        'Failed to change password',
        ErrorCode.INVALID_DATA
      );
    }
  }
  
  /**
   * Request password reset
   * 
   * @param email - User email
   * @returns Success status
   */
  async requestPasswordReset(email: string): Promise<boolean> {
    try {
      // Find user
      const user = await User.findOne({ email });
      
      // Always return success even if email doesn't exist (security)
      if (!user) {
        logger.info(`Password reset requested for non-existent email: ${email}`);
        return true;
      }
      
      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const hashedToken = crypto
        .createHash('sha256')
        .update(resetToken)
        .digest('hex');
      
      // Set reset token and expiry
      user.resetPasswordToken = hashedToken;
      user.resetPasswordExpires = new Date(Date.now() + 3600000); // 1 hour
      await user.save();
      
      logger.info(`Password reset requested for user: ${email}`);
      
      // In a real application, you would send an email with the reset token
      // For this example, we're just returning true
      
      return true;
    } catch (error) {
      logger.error(`Password reset request error: ${error.message}`);
      // Always return true for security (don't reveal if email exists)
      return true;
    }
  }
  
  /**
   * Reset password using token
   * 
   * @param token - Reset token
   * @param newPassword - New password
   * @returns Success status
   */
  async resetPassword(token: string, newPassword: string): Promise<boolean> {
    try {
      // Hash the token to compare with stored hash
      const hashedToken = crypto
        .createHash('sha256')
        .update(token)
        .digest('hex');
      
      // Find user with valid token
      const user = await User.findOne({
        resetPasswordToken: hashedToken,
        resetPasswordExpires: { $gt: Date.now() },
      });
      
      if (!user) {
        logger.warn('Password reset attempt with invalid or expired token');
        throw new ValidationError(
          'Invalid or expired password reset token',
          ErrorCode.TOKEN_INVALID
        );
      }
      
      // Update password and clear reset fields
      user.password = newPassword;
      user.resetPasswordToken = undefined;
      user.resetPasswordExpires = undefined;
      await user.save();
      
      logger.info(`Password reset successful for user: ${user.email}`);
      
      return true;
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      
      logger.error(`Password reset error: ${error.message}`);
      throw new ValidationError(
        'Failed to reset password',
        ErrorCode.INVALID_DATA
      );
    }
  }
  
  /**
   * Generate access token
   * 
   * @param user - User document
   * @returns JWT access token
   */
  private generateAccessToken(user: IUserDocument): string {
    return jwt.sign(
      {
        id: user.id,
        email: user.email,
        role: user.role,
      },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );
  }
  
  /**
   * Generate refresh token
   * 
   * @param user - User document
   * @returns JWT refresh token
   */
  private generateRefreshToken(user: IUserDocument): string {
    return jwt.sign(
      { id: user.id },
      config.jwt.refreshSecret,
      { expiresIn: config.jwt.refreshExpiresIn }
    );
  }
  
  /**
   * Filter update data to allow only safe fields
   * 
   * @param updateData - User update data
   * @returns Filtered update data
   */
  private getSafeUpdateData(updateData: Partial<IUser>): Partial<IUser> {
    // Define fields that are safe to update
    const allowedFields = ['name', 'phone', 'address'];
    
    // Create a new object with only allowed fields
    const safeData: Partial<IUser> = {};
    
    for (const field of allowedFields) {
      if (field in updateData) {
        safeData[field] = updateData[field];
      }
    }
    
    return safeData;
  }
}

export const authService = new AuthService();
export default authService;

