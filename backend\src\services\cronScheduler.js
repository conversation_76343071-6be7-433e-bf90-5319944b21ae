// Note: This is a basic implementation. In production, you would install node-cron
// For now, we'll simulate cron functionality with setTimeout

const { ImportJob, ImportJobLog } = require('../db/models');
const importService = require('./importService');

class CronScheduler {
  constructor() {
    this.jobs = new Map();
    this.isRunning = false;
  }

  // Parse cron expression to get next run time (simplified)
  parseNextRunTime(cronExpression) {
    // This is a simplified parser. In production, use a proper cron library
    const now = new Date();
    
    // For demo purposes, we'll support basic patterns:
    // "*/5 * * * *" - every 5 minutes
    // "0 * * * *" - every hour
    // "0 0 * * *" - daily at midnight
    // "0 0 * * 0" - weekly on Sunday
    
    const parts = cronExpression.split(' ');
    if (parts.length !== 5) {
      throw new Error('Invalid cron expression. Expected 5 parts: minute hour day month weekday');
    }

    const [minute, hour, day, month, weekday] = parts;
    
    // Simple calculation for next run (this is very basic)
    const nextRun = new Date(now);
    
    if (cronExpression === '*/5 * * * *') {
      // Every 5 minutes
      nextRun.setMinutes(Math.ceil(now.getMinutes() / 5) * 5);
      nextRun.setSeconds(0);
      if (nextRun <= now) {
        nextRun.setMinutes(nextRun.getMinutes() + 5);
      }
    } else if (cronExpression === '0 * * * *') {
      // Every hour
      nextRun.setHours(now.getHours() + 1);
      nextRun.setMinutes(0);
      nextRun.setSeconds(0);
    } else if (cronExpression === '0 0 * * *') {
      // Daily at midnight
      nextRun.setDate(now.getDate() + 1);
      nextRun.setHours(0);
      nextRun.setMinutes(0);
      nextRun.setSeconds(0);
    } else {
      // Default: 1 hour from now
      nextRun.setHours(now.getHours() + 1);
    }

    return nextRun;
  }

  // Schedule a job
  async scheduleJob(importJob) {
    try {
      const nextRunTime = this.parseNextRunTime(importJob.schedule);
      
      // Update the job's next run time
      await importJob.update({ nextRunAt: nextRunTime });

      // Calculate delay until next run
      const delay = nextRunTime.getTime() - Date.now();
      
      if (delay > 0) {
        const timeoutId = setTimeout(async () => {
          await this.executeJob(importJob.id);
          // Reschedule for next run
          await this.scheduleJob(importJob);
        }, delay);

        this.jobs.set(importJob.id, {
          timeoutId,
          nextRunTime,
          importJob
        });

        console.log(`📅 Scheduled import job "${importJob.name}" to run at ${nextRunTime.toISOString()}`);
      }
    } catch (error) {
      console.error(`❌ Failed to schedule job ${importJob.id}:`, error.message);
    }
  }

  // Execute a scheduled job
  async executeJob(jobId) {
    try {
      console.log(`🚀 Executing scheduled import job: ${jobId}`);
      
      const importJob = await ImportJob.findByPk(jobId);
      if (!importJob || !importJob.isActive) {
        console.log(`⏭️ Skipping inactive job: ${jobId}`);
        return;
      }

      // Create a log entry
      const logEntry = await ImportJobLog.create({
        importJobId: jobId,
        status: 'running',
        startedAt: new Date(),
        triggeredBy: 'schedule'
      });

      try {
        // Execute the import based on source type
        const result = await importService.executeImportJob(importJob);
        
        // Update log with success
        await logEntry.update({
          status: 'success',
          completedAt: new Date(),
          duration: Date.now() - logEntry.startedAt.getTime(),
          recordsProcessed: result.total || 0,
          recordsImported: result.imported || 0,
          recordsFailed: result.failed || 0,
          result: result
        });

        // Update job statistics
        await importJob.update({
          lastRunAt: new Date(),
          lastRunStatus: 'success',
          lastRunResult: result,
          totalRuns: importJob.totalRuns + 1,
          successfulRuns: importJob.successfulRuns + 1
        });

        console.log(`✅ Import job ${jobId} completed successfully`);

      } catch (error) {
        // Update log with failure
        await logEntry.update({
          status: 'failed',
          completedAt: new Date(),
          duration: Date.now() - logEntry.startedAt.getTime(),
          errors: [{ message: error.message, stack: error.stack }]
        });

        // Update job statistics
        await importJob.update({
          lastRunAt: new Date(),
          lastRunStatus: 'failed',
          lastRunResult: { error: error.message },
          totalRuns: importJob.totalRuns + 1,
          failedRuns: importJob.failedRuns + 1
        });

        console.error(`❌ Import job ${jobId} failed:`, error.message);
      }

    } catch (error) {
      console.error(`💥 Critical error executing job ${jobId}:`, error.message);
    }
  }

  // Start the scheduler
  async start() {
    if (this.isRunning) {
      console.log('📅 Cron scheduler is already running');
      return;
    }

    console.log('🚀 Starting cron scheduler...');
    this.isRunning = true;

    try {
      // Load all active import jobs
      const activeJobs = await ImportJob.findAll({
        where: { isActive: true }
      });

      console.log(`📋 Found ${activeJobs.length} active import jobs`);

      // Schedule each job
      for (const job of activeJobs) {
        await this.scheduleJob(job);
      }

      console.log('✅ Cron scheduler started successfully');
    } catch (error) {
      console.error('❌ Failed to start cron scheduler:', error.message);
      this.isRunning = false;
    }
  }

  // Stop the scheduler
  stop() {
    console.log('🛑 Stopping cron scheduler...');
    
    // Clear all scheduled jobs
    for (const [jobId, jobData] of this.jobs) {
      clearTimeout(jobData.timeoutId);
      console.log(`⏹️ Cancelled scheduled job: ${jobId}`);
    }
    
    this.jobs.clear();
    this.isRunning = false;
    
    console.log('✅ Cron scheduler stopped');
  }

  // Manually trigger a job
  async triggerJob(jobId, userId = null) {
    try {
      console.log(`🔧 Manually triggering import job: ${jobId}`);
      
      const importJob = await ImportJob.findByPk(jobId);
      if (!importJob) {
        throw new Error('Import job not found');
      }

      // Create a log entry
      const logEntry = await ImportJobLog.create({
        importJobId: jobId,
        status: 'running',
        startedAt: new Date(),
        triggeredBy: 'manual',
        triggeredByUser: userId
      });

      // Execute the import
      const result = await importService.executeImportJob(importJob);
      
      // Update log with success
      await logEntry.update({
        status: 'success',
        completedAt: new Date(),
        duration: Date.now() - logEntry.startedAt.getTime(),
        recordsProcessed: result.total || 0,
        recordsImported: result.imported || 0,
        recordsFailed: result.failed || 0,
        result: result
      });

      console.log(`✅ Manual import job ${jobId} completed successfully`);
      return result;

    } catch (error) {
      console.error(`❌ Manual import job ${jobId} failed:`, error.message);
      throw error;
    }
  }

  // Get scheduler status
  getStatus() {
    return {
      isRunning: this.isRunning,
      scheduledJobs: this.jobs.size,
      jobs: Array.from(this.jobs.entries()).map(([id, data]) => ({
        id,
        name: data.importJob.name,
        nextRunTime: data.nextRunTime,
        schedule: data.importJob.schedule
      }))
    };
  }
}

// Create singleton instance
const cronScheduler = new CronScheduler();

module.exports = cronScheduler;
