const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

// Test admin users API endpoints
async function testAdminUsersAPI() {
  console.log('🧪 Testing Admin Users API Endpoints...\n');

  // You'll need to replace this with a valid admin token
  const adminToken = 'your_admin_token_here';
  
  const api = axios.create({
    baseURL: API_BASE_URL,
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    }
  });

  try {
    // Test 1: Get all users
    console.log('1. Testing GET /admin/users');
    const usersResponse = await api.get('/admin/users?page=1&limit=5');
    console.log('✅ Users fetched:', usersResponse.data.data.users.length);
    console.log('📊 Pagination:', usersResponse.data.data.pagination);

    if (usersResponse.data.data.users.length > 0) {
      const testUser = usersResponse.data.data.users[0];
      console.log(`\n📝 Using test user: ${testUser.name} (${testUser.id})`);

      // Test 2: Update user role
      console.log('\n2. Testing PATCH /admin/users/:id/role');
      const roleResponse = await api.patch(`/admin/users/${testUser.id}/role`, {
        role: 'customer'
      });
      console.log('✅ Role updated:', roleResponse.data.message);

      // Test 3: Update user status
      console.log('\n3. Testing PATCH /admin/users/:id/status');
      const statusResponse = await api.patch(`/admin/users/${testUser.id}/status`, {
        isActive: true
      });
      console.log('✅ Status updated:', statusResponse.data.message);

      // Test 4: Update user details
      console.log('\n4. Testing PATCH /admin/users/:id');
      const updateResponse = await api.patch(`/admin/users/${testUser.id}`, {
        name: testUser.name,
        email: testUser.email,
        phone: testUser.phone || '************'
      });
      console.log('✅ User updated:', updateResponse.data.message);
    }

    // Test 5: Search users
    console.log('\n5. Testing user search');
    const searchResponse = await api.get('/admin/users?search=test&role=customer');
    console.log('✅ Search results:', searchResponse.data.data.users.length);

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', {
      status: error.response?.status,
      message: error.response?.data?.message || error.message,
      endpoint: error.config?.url
    });
  }
}

// Run tests
testAdminUsersAPI();
