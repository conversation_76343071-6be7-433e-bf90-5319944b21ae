/**
 * M-Pesa Models Backend Tests
 * Test suite for M-Pesa database models and relationships
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-01-15
 */

const { sequelize } = require('../../src/config/database');
const { MpesaB2CTransaction } = require('../../src/db/models');

describe('M-Pesa Models Backend Tests', () => {
  beforeAll(async () => {
    await sequelize.sync({ force: true });
  });

  afterAll(async () => {
    await sequelize.close();
  });

  describe('🗃️ MpesaB2CTransaction Model', () => {
    test('should create a transaction with required fields', async () => {
      const transactionData = {
        conversationId: 'AG_20240115_123456789',
        originatorConversationId: 'ORG_20240115_123456789',
        amount: '1000.00',
        type: 'GENERAL',
        typeDescription: 'General Payment',
        status: 'SUBMITTED',
        statusDescription: 'Request submitted successfully',
        remarks: 'Test transaction',
        phoneNumber: '254708374149',
        userId: 'user_123',
        initiatedBy: 'admin_456'
      };

      const transaction = await MpesaB2CTransaction.create(transactionData);

      expect(transaction.id).toBeDefined();
      expect(transaction.conversationId).toBe(transactionData.conversationId);
      expect(transaction.amount).toBe(transactionData.amount);
      expect(transaction.type).toBe(transactionData.type);
      expect(transaction.status).toBe(transactionData.status);
      expect(transaction.phoneNumber).toBe(transactionData.phoneNumber);
      expect(transaction.createdAt).toBeDefined();
      expect(transaction.updatedAt).toBeDefined();
    });

    test('should validate required fields', async () => {
      const invalidData = {
        amount: '1000.00'
        // Missing required fields
      };

      await expect(MpesaB2CTransaction.create(invalidData))
        .rejects
        .toThrow();
    });

    test('should validate phone number format', async () => {
      const invalidPhoneData = {
        conversationId: 'AG_20240115_123456790',
        originatorConversationId: 'ORG_20240115_123456790',
        amount: '1000.00',
        type: 'GENERAL',
        typeDescription: 'General Payment',
        status: 'SUBMITTED',
        statusDescription: 'Request submitted successfully',
        remarks: 'Test transaction',
        phoneNumber: 'invalid_phone',
        userId: 'user_123',
        initiatedBy: 'admin_456'
      };

      await expect(MpesaB2CTransaction.create(invalidPhoneData))
        .rejects
        .toThrow();
    });

    test('should validate transaction type enum', async () => {
      const invalidTypeData = {
        conversationId: 'AG_20240115_123456791',
        originatorConversationId: 'ORG_20240115_123456791',
        amount: '1000.00',
        type: 'INVALID_TYPE',
        typeDescription: 'Invalid Type',
        status: 'SUBMITTED',
        statusDescription: 'Request submitted successfully',
        remarks: 'Test transaction',
        phoneNumber: '254708374149',
        userId: 'user_123',
        initiatedBy: 'admin_456'
      };

      await expect(MpesaB2CTransaction.create(invalidTypeData))
        .rejects
        .toThrow();
    });

    test('should validate status enum', async () => {
      const invalidStatusData = {
        conversationId: 'AG_20240115_123456792',
        originatorConversationId: 'ORG_20240115_123456792',
        amount: '1000.00',
        type: 'GENERAL',
        typeDescription: 'General Payment',
        status: 'INVALID_STATUS',
        statusDescription: 'Invalid status',
        remarks: 'Test transaction',
        phoneNumber: '254708374149',
        userId: 'user_123',
        initiatedBy: 'admin_456'
      };

      await expect(MpesaB2CTransaction.create(invalidStatusData))
        .rejects
        .toThrow();
    });

    test('should update transaction status', async () => {
      const transaction = await MpesaB2CTransaction.create({
        conversationId: 'AG_20240115_123456793',
        originatorConversationId: 'ORG_20240115_123456793',
        amount: '1000.00',
        type: 'GENERAL',
        typeDescription: 'General Payment',
        status: 'SUBMITTED',
        statusDescription: 'Request submitted successfully',
        remarks: 'Test transaction',
        phoneNumber: '254708374149',
        userId: 'user_123',
        initiatedBy: 'admin_456'
      });

      await transaction.update({
        status: 'COMPLETED',
        statusDescription: 'Transaction completed successfully',
        receiptNumber: 'MPE123456789',
        completedAt: new Date()
      });

      expect(transaction.status).toBe('COMPLETED');
      expect(transaction.receiptNumber).toBe('MPE123456789');
      expect(transaction.completedAt).toBeDefined();
    });

    test('should handle optional fields correctly', async () => {
      const transactionWithOptionals = await MpesaB2CTransaction.create({
        conversationId: 'AG_20240115_123456794',
        originatorConversationId: 'ORG_20240115_123456794',
        amount: '1000.00',
        type: 'REFUND',
        typeDescription: 'Refund Payment',
        status: 'SUBMITTED',
        statusDescription: 'Request submitted successfully',
        remarks: 'Test refund',
        phoneNumber: '254708374149',
        userId: 'user_123',
        initiatedBy: 'admin_456',
        orderId: 'ORD_123456',
        occasion: 'Customer refund',
        metadata: { source: 'web', version: '1.0' }
      });

      expect(transactionWithOptionals.orderId).toBe('ORD_123456');
      expect(transactionWithOptionals.occasion).toBe('Customer refund');
      expect(transactionWithOptionals.metadata).toEqual({ source: 'web', version: '1.0' });
    });

    test('should find transactions by user', async () => {
      const userId = 'user_search_test';
      
      // Create multiple transactions for the user
      await MpesaB2CTransaction.bulkCreate([
        {
          conversationId: 'AG_20240115_search1',
          originatorConversationId: 'ORG_20240115_search1',
          amount: '500.00',
          type: 'GENERAL',
          typeDescription: 'General Payment',
          status: 'COMPLETED',
          statusDescription: 'Transaction completed',
          remarks: 'Search test 1',
          phoneNumber: '254708374149',
          userId: userId,
          initiatedBy: 'admin_456'
        },
        {
          conversationId: 'AG_20240115_search2',
          originatorConversationId: 'ORG_20240115_search2',
          amount: '750.00',
          type: 'REFUND',
          typeDescription: 'Refund Payment',
          status: 'PENDING',
          statusDescription: 'Transaction pending',
          remarks: 'Search test 2',
          phoneNumber: '254708374149',
          userId: userId,
          initiatedBy: 'admin_456'
        }
      ]);

      const userTransactions = await MpesaB2CTransaction.findAll({
        where: { userId: userId },
        order: [['createdAt', 'DESC']]
      });

      expect(userTransactions).toHaveLength(2);
      expect(userTransactions[0].userId).toBe(userId);
      expect(userTransactions[1].userId).toBe(userId);
    });

    test('should find transactions by status', async () => {
      const completedTransactions = await MpesaB2CTransaction.findAll({
        where: { status: 'COMPLETED' }
      });

      completedTransactions.forEach(transaction => {
        expect(transaction.status).toBe('COMPLETED');
      });
    });

    test('should find transactions by type', async () => {
      const refundTransactions = await MpesaB2CTransaction.findAll({
        where: { type: 'REFUND' }
      });

      refundTransactions.forEach(transaction => {
        expect(transaction.type).toBe('REFUND');
      });
    });

    test('should find transactions by phone number', async () => {
      const phoneNumber = '254708374149';
      const phoneTransactions = await MpesaB2CTransaction.findAll({
        where: { phoneNumber: phoneNumber }
      });

      phoneTransactions.forEach(transaction => {
        expect(transaction.phoneNumber).toBe(phoneNumber);
      });
    });

    test('should calculate transaction duration', async () => {
      const transaction = await MpesaB2CTransaction.create({
        conversationId: 'AG_20240115_duration_test',
        originatorConversationId: 'ORG_20240115_duration_test',
        amount: '1000.00',
        type: 'GENERAL',
        typeDescription: 'Duration Test',
        status: 'SUBMITTED',
        statusDescription: 'Request submitted',
        remarks: 'Duration test',
        phoneNumber: '254708374149',
        userId: 'user_123',
        initiatedBy: 'admin_456'
      });

      // Simulate completion after some time
      const completedAt = new Date(transaction.createdAt.getTime() + 30000); // 30 seconds later
      
      await transaction.update({
        status: 'COMPLETED',
        statusDescription: 'Transaction completed',
        completedAt: completedAt,
        duration: Math.floor((completedAt - transaction.createdAt) / 1000)
      });

      expect(transaction.duration).toBe(30);
      expect(transaction.completedAt).toEqual(completedAt);
    });

    test('should handle large amounts correctly', async () => {
      const largeAmountTransaction = await MpesaB2CTransaction.create({
        conversationId: 'AG_20240115_large_amount',
        originatorConversationId: 'ORG_20240115_large_amount',
        amount: '250000.00',
        type: 'GENERAL',
        typeDescription: 'Large Amount Test',
        status: 'SUBMITTED',
        statusDescription: 'Request submitted',
        remarks: 'Large amount test',
        phoneNumber: '254708374149',
        userId: 'user_123',
        initiatedBy: 'admin_456'
      });

      expect(largeAmountTransaction.amount).toBe('250000.00');
    });

    test('should handle metadata JSON field', async () => {
      const metadataTransaction = await MpesaB2CTransaction.create({
        conversationId: 'AG_20240115_metadata_test',
        originatorConversationId: 'ORG_20240115_metadata_test',
        amount: '1000.00',
        type: 'GENERAL',
        typeDescription: 'Metadata Test',
        status: 'SUBMITTED',
        statusDescription: 'Request submitted',
        remarks: 'Metadata test',
        phoneNumber: '254708374149',
        userId: 'user_123',
        initiatedBy: 'admin_456',
        metadata: {
          source: 'mobile_app',
          version: '2.1.0',
          deviceId: 'device_123',
          location: 'Nairobi',
          customData: {
            orderId: 'ORD_789',
            customerType: 'premium'
          }
        }
      });

      expect(metadataTransaction.metadata.source).toBe('mobile_app');
      expect(metadataTransaction.metadata.customData.orderId).toBe('ORD_789');
    });

    test('should enforce unique conversation IDs', async () => {
      const conversationId = 'AG_20240115_unique_test';
      
      await MpesaB2CTransaction.create({
        conversationId: conversationId,
        originatorConversationId: 'ORG_20240115_unique_test1',
        amount: '1000.00',
        type: 'GENERAL',
        typeDescription: 'Unique Test 1',
        status: 'SUBMITTED',
        statusDescription: 'Request submitted',
        remarks: 'Unique test 1',
        phoneNumber: '254708374149',
        userId: 'user_123',
        initiatedBy: 'admin_456'
      });

      // Try to create another transaction with the same conversation ID
      await expect(MpesaB2CTransaction.create({
        conversationId: conversationId,
        originatorConversationId: 'ORG_20240115_unique_test2',
        amount: '1000.00',
        type: 'GENERAL',
        typeDescription: 'Unique Test 2',
        status: 'SUBMITTED',
        statusDescription: 'Request submitted',
        remarks: 'Unique test 2',
        phoneNumber: '254708374149',
        userId: 'user_123',
        initiatedBy: 'admin_456'
      })).rejects.toThrow();
    });
  });

  describe('🔍 Model Queries and Aggregations', () => {
    test('should count transactions by status', async () => {
      const statusCounts = await MpesaB2CTransaction.findAll({
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['status'],
        raw: true
      });

      expect(Array.isArray(statusCounts)).toBe(true);
      statusCounts.forEach(statusCount => {
        expect(statusCount).toHaveProperty('status');
        expect(statusCount).toHaveProperty('count');
        expect(typeof statusCount.count).toBe('string'); // COUNT returns string in raw queries
      });
    });

    test('should sum amounts by type', async () => {
      const typeSums = await MpesaB2CTransaction.findAll({
        attributes: [
          'type',
          [sequelize.fn('SUM', sequelize.cast(sequelize.col('amount'), 'DECIMAL')), 'totalAmount']
        ],
        group: ['type'],
        raw: true
      });

      expect(Array.isArray(typeSums)).toBe(true);
      typeSums.forEach(typeSum => {
        expect(typeSum).toHaveProperty('type');
        expect(typeSum).toHaveProperty('totalAmount');
      });
    });

    test('should find recent transactions', async () => {
      const recentTransactions = await MpesaB2CTransaction.findAll({
        where: {
          createdAt: {
            [sequelize.Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        order: [['createdAt', 'DESC']],
        limit: 10
      });

      expect(Array.isArray(recentTransactions)).toBe(true);
      expect(recentTransactions.length).toBeLessThanOrEqual(10);
    });
  });
});
