# M-Pesa B2C Environment Setup

## Required Environment Variables

Add these variables to your `.env` file:

```bash
# M-Pesa B2C Configuration
MPESA_ENVIRONMENT=sandbox
MPESA_CONSUMER_KEY=your_consumer_key
MPESA_CONSUMER_SECRET=your_consumer_secret
MPESA_BUSINESS_SHORT_CODE=your_business_shortcode
MPESA_INITIATOR_NAME=your_initiator_name
MPESA_SECURITY_CREDENTIAL=your_security_credential

# B2C Callback URLs
MPESA_B2C_RESULT_URL=https://yourdomain.com/api/mpesa/b2c/callback/result
MPESA_B2C_QUEUE_TIMEOUT_URL=https://yourdomain.com/api/mpesa/b2c/callback/timeout

# Optional: Custom limits
MPESA_B2C_DAILY_LIMIT=500000
MPESA_B2C_SINGLE_TRANSACTION_LIMIT=150000
```

## Sandbox vs Production

### Sandbox Configuration
```bash
MPESA_ENVIRONMENT=sandbox
MPESA_CONSUMER_KEY=your_sandbox_consumer_key
MPESA_CONSUMER_SECRET=your_sandbox_consumer_secret
MPESA_BUSINESS_SHORT_CODE=600000  # Example sandbox shortcode
MPESA_INITIATOR_NAME=testapi
MPESA_SECURITY_CREDENTIAL=Safaricom000!
```

### Production Configuration
```bash
MPESA_ENVIRONMENT=production
MPESA_CONSUMER_KEY=your_production_consumer_key
MPESA_CONSUMER_SECRET=your_production_consumer_secret
MPESA_BUSINESS_SHORT_CODE=your_actual_shortcode
MPESA_INITIATOR_NAME=your_actual_initiator
MPESA_SECURITY_CREDENTIAL=your_encrypted_credential
```

## Getting M-Pesa Credentials

1. **Register on Safaricom Developer Portal**
   - Go to https://developer.safaricom.co.ke/
   - Create an account and verify your email

2. **Create an App**
   - Create a new app in the developer portal
   - Select "Lipa na M-Pesa Online" and "M-Pesa B2C" APIs
   - Get your Consumer Key and Consumer Secret

3. **Get Business Short Code**
   - For sandbox: Use test shortcodes provided by Safaricom
   - For production: Use your actual M-Pesa business number

4. **Set Up Initiator**
   - Create an initiator user in your M-Pesa business account
   - Get the initiator name and password
   - Generate security credential using M-Pesa certificate

## Security Credential Generation

For production, you need to encrypt your initiator password using M-Pesa's public certificate:

```javascript
const crypto = require('crypto');
const fs = require('fs');

// Load M-Pesa public certificate
const certificate = fs.readFileSync('path/to/mpesa_certificate.cer');

// Encrypt initiator password
const securityCredential = crypto.publicEncrypt({
  key: certificate,
  padding: crypto.constants.RSA_PKCS1_PADDING
}, Buffer.from('your_initiator_password')).toString('base64');
```

## Callback URL Setup

Your callback URLs must be:
- Publicly accessible (not localhost)
- HTTPS enabled (for production)
- Able to handle POST requests

Example callback URLs:
```
Result URL: https://yourdomain.com/api/mpesa/b2c/callback/result/{transactionId}
Timeout URL: https://yourdomain.com/api/mpesa/b2c/callback/timeout/{transactionId}
```

## Testing

### Test Phone Numbers (Sandbox)
- ************
- 254711XXXXXX
- 254733XXXXXX

### Test Amounts
- Any amount between 1 and 150,000 KES
- Use whole numbers (no decimals)

## Common Issues

1. **Invalid Security Credential**
   - Ensure you're using the correct credential for your environment
   - For sandbox, use the provided test credential
   - For production, generate using the actual certificate

2. **Callback URL Not Reachable**
   - Ensure URLs are publicly accessible
   - Check firewall settings
   - Verify HTTPS configuration

3. **Insufficient Funds**
   - Ensure your M-Pesa business account has sufficient balance
   - Check account limits and restrictions

4. **Invalid Phone Number**
   - Use Kenyan phone numbers in 254XXXXXXXXX format
   - Ensure the number is registered for M-Pesa

## API Endpoints

### Admin Endpoints (Require admin authentication)
- `POST /api/mpesa/b2c/payment` - General B2C payment
- `POST /api/mpesa/b2c/refund` - Process refund
- `POST /api/mpesa/b2c/salary` - Salary payment
- `POST /api/mpesa/b2c/promotion` - Promotional payment
- `GET /api/mpesa/b2c/transactions` - Transaction history
- `GET /api/mpesa/b2c/stats` - Transaction statistics
- `POST /api/mpesa/express/test ` - testing the IN push SDK 

### User Endpoints (Require user authentication)
- `GET /api/mpesa/b2c/my-transactions` - User's transactions
- `GET /api/mpesa/b2c/my-transaction/:id` - Specific transaction

### Webhook Endpoints (No authentication)
- `POST /api/mpesa/b2c/callback/result/:transactionId` - Result callback
- `POST /api/mpesa/b2c/callback/timeout/:transactionId` - Timeout callback

### Utility Endpoints
- `GET /api/mpesa/b2c/health` - Health check
- `GET /api/mpesa/b2c/transaction-types` - Available transaction types

## Example Usage

### Process Refund
```javascript
const response = await fetch('/api/mpesa/b2c/refund', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your_admin_token'
  },
  body: JSON.stringify({
    orderId: 'order-uuid',
    phoneNumber: '************',
    amount: 1000,
    reason: 'Customer requested refund'
  })
});
```

### Check Transaction Status
```javascript
const response = await fetch('/api/mpesa/b2c/transaction/transaction-id', {
  headers: {
    'Authorization': 'Bearer your_admin_token'
  }
});
```

## Monitoring and Logging

The B2C service includes comprehensive logging:
- All transactions are logged with sanitized data
- Callback responses are logged for debugging
- Error tracking for failed transactions
- Performance metrics for transaction processing

Check your application logs for:
- `✅ B2C payment initiated successfully`
- `❌ B2C payment failed`
- `📞 B2C Result callback received`
- `⏰ B2C transaction timed out`
