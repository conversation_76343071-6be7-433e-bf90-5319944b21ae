# G20 Shop API Endpoints

This document provides detailed information about the G20 Shop API endpoints.

## Base URL

All API endpoints are relative to the base URL:

```
http://localhost:3000/api
```

## Authentication

### Register a new user

```
POST /auth/register
```

**Request Body:**

```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**

```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "id": 1,
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "createdAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Login

```
POST /auth/login
```

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>"
    }
  }
}
```

### Refresh Token

```
POST /auth/refresh-token
```

**Request Body:**

```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**

```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

## Products

### Get all products

```
GET /products
```

**Query Parameters:**

- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of items per page (default: 10)
- `category` (optional): Filter by category ID
- `search` (optional): Search term
- `minPrice` (optional): Minimum price
- `maxPrice` (optional): Maximum price
- `sort` (optional): Sort field (default: 'createdAt')
- `order` (optional): Sort order ('asc' or 'desc', default: 'desc')

**Response:**

```json
{
  "success": true,
  "message": "Products retrieved successfully",
  "data": {
    "products": [
      {
        "id": 1,
        "name": "Wireless Mouse",
        "slug": "wireless-mouse",
        "description": "High-performance wireless mouse",
        "price": 29.99,
        "images": [
          {
            "id": 1,
            "url": "https://example.com/images/mouse1.jpg"
          }
        ],
        "categories": [
          {
            "id": 1,
            "name": "Accessories"
          }
        ]
      }
    ],
    "totalItems": 100,
    "totalPages": 10,
    "currentPage": 1
  }
}
```

### Get product by ID or slug

```
GET /products/:identifier
```

**Response:**

```json
{
  "success": true,
  "message": "Product retrieved successfully",
  "data": {
    "id": 1,
    "name": "Wireless Mouse",
    "slug": "wireless-mouse",
    "description": "High-performance wireless mouse",
    "price": 29.99,
    "images": [
      {
        "id": 1,
        "url": "https://example.com/images/mouse1.jpg"
      }
    ],
    "categories": [
      {
        "id": 1,
        "name": "Accessories"
      }
    ]
  }
}
```

## Cart

### Get cart

```
GET /cart
```

**Response:**

```json
{
  "success": true,
  "message": "Cart retrieved successfully",
  "data": {
    "id": 1,
    "items": [
      {
        "id": 1,
        "productId": 1,
        "quantity": 2,
        "product": {
          "id": 1,
          "name": "Wireless Mouse",
          "price": 29.99,
          "image": "https://example.com/images/mouse1.jpg"
        }
      }
    ],
    "totalItems": 2,
    "totalPrice": 59.98
  }
}
```

### Add item to cart

```
POST /cart/items
```

**Request Body:**

```json
{
  "productId": 1,
  "quantity": 2
}
```

**Response:**

```json
{
  "success": true,
  "message": "Item added to cart successfully",
  "data": {
    "id": 1,
    "productId": 1,
    "quantity": 2
  }
}
```

## Error Responses

All API endpoints return a consistent error response format:

```json
{
  "success": false,
  "message": "Error message",
  "error": {
    "code": "ERROR_CODE",
    "details": {}
  }
}
```

Common error codes:

- `VALIDATION_ERROR`: Request validation failed
- `AUTHENTICATION_ERROR`: Authentication failed
- `AUTHORIZATION_ERROR`: User not authorized to perform the action
- `NOT_FOUND`: Resource not found
- `INTERNAL_SERVER_ERROR`: Server error
