# Server Configuration
PORT=5001
NODE_ENV=test

# Database Configuration
DB_DIALECT=sqlite
DB_STORAGE=./database_test.sqlite

# JWT Configuration
JWT_SECRET=g20shop-jwt-secret-key-test
JWT_EXPIRES_IN=1d
JWT_REFRESH_SECRET=g20shop-jwt-refresh-secret-key-test
JWT_REFRESH_EXPIRES_IN=7d

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Bcrypt Configuration
BCRYPT_SALT_ROUNDS=10

# Upload Configuration
UPLOAD_PATH=uploads/test/
UPLOAD_MAX_SIZE=5000000

# Stripe Configuration (test mode)
STRIPE_SECRET_KEY=sk_test_mock_key_for_testing
