# Testing the Backend

This document provides instructions on how to test the backend functionality of the TechGear e-commerce platform.

## Prerequisites

Before running the tests, make sure you have:

1. Node.js installed (v14 or higher)
2. PostgreSQL database running
3. Backend dependencies installed (`npm install`)
4. Environment variables configured in `.env` file
5. Database migrations applied (`npx sequelize-cli db:migrate`)

## Available Test Scripts

The backend includes several test scripts:

1. **test-models.js**: Tests the database models and their relationships
2. **test-api.js**: Tests the API endpoints
3. **run-tests.js**: Runs both test scripts in sequence

## Running the Tests

### Option 1: Run All Tests

To run all tests in sequence:

```bash
node run-tests.js
```

This will:
1. Run the model tests first
2. Then run the API tests
3. Save all test results in the `test-results` directory

### Option 2: Run Individual Tests

To run only the model tests:

```bash
node test-models.js
```

To run only the API tests:

```bash
node test-api.js
```

## Test Results

All test results are saved in the `test-results` directory as JSON files. Each test generates:

- A success file with the test name (e.g., `create-user.json`)
- An error file if the test fails (e.g., `create-user-error.json`)

## What's Being Tested

### Model Tests

The model tests verify:

1. Database connection
2. User creation and retrieval
3. Category creation
4. Product creation with images and categories
5. Cart and cart item functionality
6. Wishlist functionality
7. Order creation and management
8. Review creation

### API Tests

The API tests verify:

1. User registration and authentication
2. Product listing and retrieval
3. Category listing
4. Cart operations (add, update, remove items)
5. Wishlist operations
6. Order creation and retrieval

## Troubleshooting

If you encounter issues running the tests:

1. **Database Connection Issues**: Verify your database credentials in the `.env` file
2. **Missing Tables**: Make sure you've run the migrations (`npx sequelize-cli db:migrate`)
3. **API Server Not Running**: For API tests, ensure the backend server is running on the configured port
4. **Permission Issues**: Ensure your database user has sufficient permissions

## Manual Testing

For manual testing of the API endpoints, you can use tools like:

1. **Postman**: Import the Postman collection from `docs/postman-collection.json`
2. **cURL**: Example commands are available in `docs/curl-examples.md`
3. **Swagger UI**: Available at `/api-docs` when the server is running with `NODE_ENV=development`

## Adding New Tests

To add new tests:

1. For model tests: Add new test functions to `test-models.js`
2. For API tests: Add new test functions to `test-api.js`
3. Make sure to use the `logResult` and `logError` helper functions to save test results
