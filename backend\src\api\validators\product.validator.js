const { body, param } = require('express-validator');
const { validate } = require('../middlewares/validation.middleware');

exports.validateProductCreate = [
  body('name')
    .notEmpty().withMessage('Product name is required')
    .isString().withMessage('Product name must be a string')
    .isLength({ min: 2, max: 100 }).withMessage('Product name must be between 2 and 100 characters'),
  
  body('brand')
    .notEmpty().withMessage('Brand is required')
    .isString().withMessage('Brand must be a string'),
  
  body('model')
    .notEmpty().withMessage('Model is required')
    .isString().withMessage('Model must be a string'),
  
  body('description')
    .optional()
    .isString().withMessage('Description must be a string'),
  
  body('sku')
    .notEmpty().withMessage('SKU is required')
    .isString().withMessage('SKU must be a string')
    .isLength({ min: 3, max: 50 }).withMessage('SKU must be between 3 and 50 characters'),
  
  body('regularPrice')
    .notEmpty().withMessage('Regular price is required')
    .isFloat({ min: 0 }).withMessage('Regular price must be a positive number'),
  
  body('salePrice')
    .optional()
    .isFloat({ min: 0 }).withMessage('Sale price must be a positive number'),
  
  body('cost')
    .notEmpty().withMessage('Cost is required')
    .isFloat({ min: 0 }).withMessage('Cost must be a positive number'),
  
  body('stockQuantity')
    .optional()
    .isInt({ min: 0 }).withMessage('Stock quantity must be a non-negative integer'),
  
  body('minOrderQuantity')
    .optional()
    .isInt({ min: 1 }).withMessage('Minimum order quantity must be at least 1'),
  
  body('maxOrderQuantity')
    .optional()
    .isInt({ min: 1 }).withMessage('Maximum order quantity must be at least 1'),
  
  body('categories')
    .optional()
    .isArray().withMessage('Categories must be an array'),
  
  body('categories.*')
    .optional()
    .isUUID().withMessage('Category ID must be a valid UUID'),
  
  body('images')
    .optional()
    .isArray().withMessage('Images must be an array'),
  
  body('images.*.url')
    .optional()
    .isURL().withMessage('Image URL must be a valid URL'),
  
  body('images.*.altText')
    .optional()
    .isString().withMessage('Image alt text must be a string'),
  
  validate
];

exports.validateProductUpdate = [
  param('id')
    .isUUID().withMessage('Product ID must be a valid UUID'),
  
  body('name')
    .optional()
    .isString().withMessage('Product name must be a string')
    .isLength({ min: 2, max: 100 }).withMessage('Product name must be between 2 and 100 characters'),
  
  body('brand')
    .optional()
    .isString().withMessage('Brand must be a string'),
  
  body('model')
    .optional()
    .isString().withMessage('Model must be a string'),
  
  body('description')
    .optional()
    .isString().withMessage('Description must be a string'),
  
  body('sku')
    .optional()
    .isString().withMessage('SKU must be a string')
    .isLength({ min: 3, max: 50 }).withMessage('SKU must be between 3 and 50 characters'),
  
  body('regularPrice')
    .optional()
    .isFloat({ min: 0 }).withMessage('Regular price must be a positive number'),
  
  body('salePrice')
    .optional()
    .isFloat({ min: 0 }).withMessage('Sale price must be a positive number'),
  
  body('cost')
    .optional()
    .isFloat({ min: 0 }).withMessage('Cost must be a positive number'),
  
  body('stockQuantity')
    .optional()
    .isInt({ min: 0 }).withMessage('Stock quantity must be a non-negative integer'),
  
  body('minOrderQuantity')
    .optional()
    .isInt({ min: 1 }).withMessage('Minimum order quantity must be at least 1'),
  
  body('maxOrderQuantity')
    .optional()
    .isInt({ min: 1 }).withMessage('Maximum order quantity must be at least 1'),
  
  body('categories')
    .optional()
    .isArray().withMessage('Categories must be an array'),
  
  body('categories.*')
    .optional()
    .isUUID().withMessage('Category ID must be a valid UUID'),
  
  validate
];
