const app = require('./app');
const sequelize = require('./config/database');
const logger = require('./utils/logger');
const cronScheduler = require('./services/cronScheduler');

const PORT = process.env.PORT || 3000;

// Test database connection
async function assertDatabaseConnection() {
  try {
    await sequelize.authenticate();
    logger.info('Database connection established successfully.');
  } catch (error) {
    logger.error('Unable to connect to the database:', error);
    process.exit(1);
  }
}

// Start server
async function startServer() {
  await assertDatabaseConnection();

  // Start the cron scheduler
  try {
    await cronScheduler.start();
    logger.info('Cron scheduler started successfully');
  } catch (error) {
    logger.error('Failed to start cron scheduler:', error);
  }

  app.listen(PORT, () => {
    logger.info(`Server running on port ${PORT}`);
  });
}

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  cronScheduler.stop();
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  cronScheduler.stop();
  process.exit(0);
});

startServer();
