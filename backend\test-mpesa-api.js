/**
 * M-Pesa API Test
 * Test the M-Pesa endpoints to verify they're working
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testEndpoint(method, url, data = null) {
  try {
    console.log(`\n🔍 Testing ${method.toUpperCase()} ${url}`);
    
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    console.log(`✅ Success: ${response.status}`);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    if (error.response) {
      console.log(`❌ Error: ${error.response.status}`);
      console.log('Error Response:', JSON.stringify(error.response.data, null, 2));
    } else if (error.code === 'ECONNREFUSED') {
      console.log('❌ Connection refused - is the server running?');
    } else {
      console.log('❌ Network Error:', error.message);
    }
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting M-Pesa API Tests');
  console.log('============================');

  // Test 1: Basic health check
  console.log('\n📋 Testing basic endpoints...');
  await testEndpoint('GET', '/health');
  await testEndpoint('GET', '/mpesa/health');

  // Test 2: B2C utility endpoints
  console.log('\n🔧 Testing B2C utility endpoints...');
  await testEndpoint('GET', '/mpesa/b2c/utility/health');
  await testEndpoint('GET', '/mpesa/b2c/utility/transaction-types');
  await testEndpoint('GET', '/mpesa/b2c/utility/status-types');

  // Test 3: Validation endpoints
  console.log('\n✅ Testing validation endpoints...');
  await testEndpoint('POST', '/mpesa/b2c/utility/validate-phone', {
    phoneNumber: '**********'
  });

  await testEndpoint('POST', '/mpesa/b2c/utility/validate-amount', {
    amount: 1000,
    transactionType: 'REFUND'
  });

  // Test 4: Fee calculator
  console.log('\n💰 Testing fee calculator...');
  await testEndpoint('GET', '/mpesa/b2c/utility/fee-calculator/1000');

  // Test 5: Configuration endpoints
  console.log('\n⚙️ Testing configuration endpoints...');
  await testEndpoint('GET', '/mpesa/b2c/utility/config-summary');
  await testEndpoint('GET', '/mpesa/b2c/utility/business-hours');

  console.log('\n🏁 Tests completed!');
  console.log('============================');
}

// Run the tests
runTests().catch(error => {
  console.error('❌ Test suite failed:', error.message);
  process.exit(1);
});
