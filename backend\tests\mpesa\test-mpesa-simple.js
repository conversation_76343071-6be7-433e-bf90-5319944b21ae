/**
 * Simple M-Pesa API Test
 * Test the M-Pesa endpoints to verify they're working
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testEndpoint(method, url, data = null) {
  try {
    console.log(`\n🔍 Testing ${method.toUpperCase()} ${url}`);
    
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    console.log(`✅ Success: ${response.status}`);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    if (error.response) {
      console.log(`❌ Error: ${error.response.status}`);
      console.log('Error Response:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('❌ Network Error:', error.message);
    }
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting M-Pesa API Tests');
  console.log('============================');

  // Test 1: M-Pesa Health
  await testEndpoint('GET', '/mpesa/health');

  // Test 2: B2C Health
  await testEndpoint('GET', '/mpesa/b2c/utility/health');

  // Test 3: Phone validation
  await testEndpoint('POST', '/mpesa/b2c/utility/validate-phone', {
    phoneNumber: '**********'
  });

  // Test 4: Amount validation
  await testEndpoint('POST', '/mpesa/b2c/utility/validate-amount', {
    amount: 1000,
    transactionType: 'REFUND'
  });

  // Test 5: Fee calculator
  await testEndpoint('GET', '/mpesa/b2c/utility/fee-calculator/1000');

  console.log('\n🏁 Tests completed!');
}

// Run the tests
runTests().catch(console.error);
