# Database Schema for TechGear Shop

## Core Tables and Relationships

```
+----------------+       +----------------+       +----------------+
|     Users      |       |   Customers    |       |   Products     |
+----------------+       +----------------+       +----------------+
| id             |       | id             |       | id             |
| name           |       | name           |       | name           |
| email          |       | email          |       | description    |
| password       |       | phone          |       | sku            |
| role           |       | address        |       | price          |
| isActive       |       | createdAt      |       | cost           |
| createdAt      |       | updatedAt      |       | imageUrl       |
| updatedAt      |       +-------+--------+       | isActive       |
+-------+--------+               |                | categoryId     |
        |                        |                | createdAt      |
        |                        |                | updatedAt      |
        |                        |                +-------+--------+
        |                        |                        |
        |                        |                        |
+-------v--------+       +-------v--------+       +-------v--------+
| RepairTickets  |       |    Orders      |       |  Categories    |
+----------------+       +----------------+       +----------------+
| id             |       | id             |       | id             |
| ticketNumber   |<------+ customerId     |       | name           |
| deviceType     |       | orderNumber    |       | description    |
| brand          |       | totalAmount    |       | createdAt      |
| model          |       | status         |       | updatedAt      |
| serialNumber   |       | paymentMethod  |       +----------------+
| issueDescription       | createdById    |
| status         |       | createdAt      |
| estimatedCompletionDate| updatedAt      |
| actualCompletionDate   +-------+--------+
| cost           |               |
| notes          |               |
| customerId     |               |
| technicianId   |       +-------v--------+
| createdById    |       |   OrderItems   |
| createdAt      |       +----------------+
| updatedAt      |       | id             |
+-------+--------+       | orderId        |
        |                | productId      |
        |                | quantity       |
        |                | price          |
+-------v--------+       | createdAt      |
| RepairStatuses |       | updatedAt      |
+----------------+       +----------------+
| id             |
| repairTicketId |
| status         |
| notes          |
| userId         |
| createdAt      |
| updatedAt      |
+----------------+


+----------------+       +----------------+
|   Inventory    |       | InventoryTrans |
+----------------+       +----------------+
| id             |       | id             |
| productId      |<------+ inventoryId    |
| quantity       |       | type           |
| minQuantity    |       | quantity       |
| maxQuantity    |       | reference      |
| location       |       | notes          |
| createdAt      |       | userId         |
| updatedAt      |       | createdAt      |
+----------------+       | updatedAt      |
                         +----------------+
```

## Table Descriptions

### Users
- Stores staff member information
- Different roles (admin, sales, technician)
- Used for authentication and authorization

### Customers
- Stores customer information
- Linked to orders and repair tickets

### Products
- Stores product information
- Linked to categories and inventory

### Categories
- Product categories
- Hierarchical structure possible

### Inventory
- Tracks current stock levels
- Linked to products

### InventoryTransactions
- Records all inventory changes
- Types: purchase, sale, adjustment, return
- Maintains audit trail

### Orders
- Customer purchase records
- Contains order metadata

### OrderItems
- Individual items in an order
- Links to products with quantity and price

### RepairTickets
- Tracks repair and maintenance services
- Contains device information and status

### RepairStatuses
- Historical record of status changes
- Maintains complete repair history

## Key Relationships

1. **Customer to Orders/RepairTickets**
   - One customer can have many orders
   - One customer can have many repair tickets

2. **Product to OrderItems/Inventory**
   - One product can be in many order items
   - One product has one inventory record

3. **User to RepairTickets**
   - Users (technicians) are assigned to repair tickets
   - Users (staff) create repair tickets

4. **RepairTicket to RepairStatuses**
   - One repair ticket has many status updates
   - Creates a timeline of the repair process

5. **Inventory to InventoryTransactions**
   - One inventory record has many transactions
   - Transactions record all changes to inventory

## Indexes and Performance Considerations

### Primary Indexes
- All tables have a primary key `id` (UUID or auto-increment)

### Secondary Indexes
- `Users`: email (unique)
- `Customers`: email (unique)
- `Products`: sku (unique)
- `RepairTickets`: ticketNumber (unique)
- `Orders`: orderNumber (unique)

### Foreign Key Indexes
- All foreign key columns should be indexed
- Examples: customerId, productId, userId, etc.

### Search Indexes
- Consider adding indexes for frequently searched fields:
  - RepairTickets: status, customerId
  - Products: categoryId
  - Inventory: quantity (for low stock alerts)

## Data Types and Constraints

### Common Data Types
- IDs: UUID or BIGINT
- Names/Descriptions: VARCHAR/TEXT
- Prices/Costs: DECIMAL(10,2)
- Dates: TIMESTAMP
- Enums: For status fields with predefined values

### Constraints
- NOT NULL on required fields
- UNIQUE on business identifiers
- CHECK constraints on numeric fields (e.g., quantity >= 0)
- Foreign key constraints with appropriate ON DELETE/UPDATE actions

## Notes on Implementation

1. **Soft Deletes**
   - Consider using soft deletes (isActive flag) for important records
   - Helps maintain data integrity and history

2. **Audit Trails**
   - All tables include createdAt/updatedAt timestamps
   - Consider adding createdBy/updatedBy for critical tables

3. **Transaction Support**
   - Use database transactions for operations that modify multiple tables
   - Example: Creating an order should update inventory in a transaction

4. **Pagination Support**
   - Design queries with pagination in mind for large tables
   - Use LIMIT/OFFSET or cursor-based pagination
