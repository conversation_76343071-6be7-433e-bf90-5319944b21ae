# Safaricom Daraja API Integration Plan for G20Shop

## Overview
This document outlines the integration plan for Safaricom's Daraja API into the G20Shop e-commerce platform for laptop and PC accessories.

## Business Requirements
- Accept M-Pesa payments for laptop and PC accessories
- Handle payment confirmations and callbacks
- Support refunds and transaction status checking
- Integrate with existing order management system
- Maintain transaction records in PostgreSQL database

## API Endpoints to Implement

### 1. Authentication API
**Purpose**: Generate access tokens for secure API communication
- **Endpoint**: `GET /oauth/v1/generate?grant_type=client_credentials`
- **Environment**: 
  - Sandbox: `https://sandbox.safaricom.co.ke`
  - Production: `https://api.safaricom.co.ke`
- **Token Validity**: 3600 seconds (1 hour)
- **Implementation**: Token caching and auto-renewal

### 2. STK Push (Lipa Na M-Pesa Online) - PRIMARY
**Purpose**: Initiate payment requests during checkout
- **Endpoint**: `POST /mpesa/stkpush/v1/processrequest`
- **Use Case**: Customer checkout process
- **Integration Point**: Order creation and payment processing

### 3. STK Push Query
**Purpose**: Check status of STK Push requests
- **Endpoint**: `POST /mpesa/stkpushquery/v1/query`
- **Use Case**: Verify payment completion

### 4. C2B (Customer to Business)
**Purpose**: Receive payment notifications
- **Endpoints**:
  - `POST /mpesa/c2b/v1/registerurl` - Register callback URLs
  - `POST /mpesa/c2b/v1/simulate` - Testing payments
- **Integration Point**: Payment confirmation webhooks

### 5. Transaction Status
**Purpose**: Query transaction status for order management
- **Endpoint**: `POST /mpesa/transactionstatus/v1/query`
- **Use Case**: Order status updates and customer support

### 6. B2C (Business to Customer) - SECONDARY
**Purpose**: Process refunds and cashbacks
- **Endpoint**: `POST /mpesa/b2c/v1/paymentrequest`
- **Use Case**: Refund processing for returns

## Technical Implementation Structure

### Backend Components to Create

1. **M-Pesa Service Layer** (`/src/services/mpesa.service.js`)
   - Authentication token management
   - STK Push implementation
   - Callback handling
   - Transaction status queries

2. **M-Pesa Controllers** (`/src/api/controllers/mpesa.controller.js`)
   - Payment initiation endpoints
   - Callback endpoints
   - Status check endpoints

3. **M-Pesa Routes** (`/src/api/routes/mpesa.routes.js`)
   - Public payment endpoints
   - Protected admin endpoints
   - Webhook endpoints

4. **M-Pesa Models** (`/src/db/models/MpesaTransaction.js`)
   - Transaction logging
   - Payment status tracking
   - Audit trail

5. **M-Pesa Middleware** (`/src/middlewares/mpesa.middleware.js`)
   - Request validation
   - Callback verification
   - Rate limiting

### Database Schema

```sql
-- M-Pesa Transactions Table
CREATE TABLE mpesa_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID REFERENCES orders(id),
    merchant_request_id VARCHAR(255),
    checkout_request_id VARCHAR(255),
    mpesa_receipt_number VARCHAR(255),
    transaction_date TIMESTAMP,
    phone_number VARCHAR(15),
    amount DECIMAL(10,2),
    transaction_type VARCHAR(50), -- STK_PUSH, C2B, B2C
    status VARCHAR(50), -- PENDING, SUCCESS, FAILED, CANCELLED
    result_code INTEGER,
    result_desc TEXT,
    callback_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- M-Pesa Configuration Table
CREATE TABLE mpesa_config (
    id SERIAL PRIMARY KEY,
    environment VARCHAR(20), -- sandbox, production
    consumer_key VARCHAR(255),
    consumer_secret VARCHAR(255),
    business_short_code VARCHAR(10),
    lipa_na_mpesa_online_passkey TEXT,
    callback_url VARCHAR(255),
    confirmation_url VARCHAR(255),
    validation_url VARCHAR(255),
    is_active BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Environment Configuration

### Required Environment Variables
```bash
# M-Pesa Configuration
MPESA_ENVIRONMENT=sandbox # or production
MPESA_CONSUMER_KEY=your_consumer_key
MPESA_CONSUMER_SECRET=your_consumer_secret
MPESA_BUSINESS_SHORT_CODE=174379 # sandbox shortcode
MPESA_LIPA_NA_MPESA_ONLINE_PASSKEY=your_passkey
MPESA_CALLBACK_URL=https://yourdomain.com/api/mpesa/callback
MPESA_CONFIRMATION_URL=https://yourdomain.com/api/mpesa/confirmation
MPESA_VALIDATION_URL=https://yourdomain.com/api/mpesa/validation

# Sandbox URLs
MPESA_SANDBOX_BASE_URL=https://sandbox.safaricom.co.ke
# Production URLs
MPESA_PRODUCTION_BASE_URL=https://api.safaricom.co.ke
```

## Integration Flow

### 1. Customer Checkout Process
```
1. Customer adds items to cart
2. Customer proceeds to checkout
3. Customer selects M-Pesa payment option
4. System initiates STK Push request
5. Customer receives payment prompt on phone
6. Customer enters M-Pesa PIN
7. System receives callback confirmation
8. Order status updated to paid
9. Customer receives confirmation
```

### 2. Payment Callback Handling
```
1. Safaricom sends callback to registered URL
2. System validates callback authenticity
3. System updates transaction status
4. System updates order status
5. System sends customer notification
6. System triggers order fulfillment
```

## Security Considerations

### 1. Authentication
- Secure storage of consumer keys and secrets
- Token caching and automatic renewal
- Request signing and validation

### 2. Callback Security
- IP whitelisting for Safaricom callbacks
- Callback URL validation
- Request signature verification

### 3. Data Protection
- Encryption of sensitive payment data
- PCI DSS compliance considerations
- Audit logging for all transactions

## Testing Strategy

### 1. Sandbox Testing
- Use Safaricom sandbox environment
- Test all payment scenarios
- Validate callback handling
- Test error conditions

### 2. Integration Testing
- End-to-end payment flows
- Order management integration
- Customer notification testing
- Refund process testing

## Go-Live Process

### 1. Prerequisites
- Safaricom developer account
- Business registration with Safaricom
- PayBill number acquisition
- SSL certificate for callback URLs

### 2. Production Deployment
- Environment variable updates
- DNS configuration for callbacks
- Load balancer configuration
- Monitoring setup

### 3. Post-Launch Monitoring
- Transaction success rates
- Callback response times
- Error rate monitoring
- Customer support integration

## Compliance and Regulations

### 1. Financial Regulations
- Central Bank of Kenya compliance
- Anti-money laundering (AML) requirements
- Know Your Customer (KYC) considerations

### 2. Data Protection
- Kenya Data Protection Act compliance
- GDPR considerations for international customers
- Data retention policies

## Support and Maintenance

### 1. Monitoring
- Real-time transaction monitoring
- Alert systems for failed payments
- Performance metrics tracking

### 2. Customer Support
- Transaction lookup tools
- Refund processing workflows
- Dispute resolution procedures

## Timeline and Milestones

### Phase 1: Foundation (Week 1-2)
- Environment setup
- Basic authentication implementation
- Database schema creation

### Phase 2: Core Integration (Week 3-4)
- STK Push implementation
- Callback handling
- Order integration

### Phase 3: Testing (Week 5-6)
- Sandbox testing
- Integration testing
- Security testing

### Phase 4: Production (Week 7-8)
- Production setup
- Go-live process
- Monitoring implementation

This integration will enable your laptop and PC accessories e-commerce platform to accept M-Pesa payments seamlessly, providing customers with the most popular payment method in Kenya.
