import { Request } from 'express';

/**
 * Roles enum for user types
 */
export enum UserRole {
  CUSTOMER = 'customer',
  ADMIN = 'admin',
  TECHNICIAN = 'technician',
  SALES = 'sales',
}

/**
 * User interface that extends Mongoose document
 */
export interface IUser {
  id?: string;
  name: string;
  email: string;
  password: string;
  role: UserRole;
  isActive: boolean;
  lastLogin?: Date;
  phone?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
  };
  resetPasswordToken?: string;
  resetPasswordExpires?: Date;
  checkPassword(password: string): Promise<boolean>;
}

/**
 * Request with authenticated user
 */
export interface AuthRequest extends Request {
  user?: IUser;
}

/**
 * JWT payload interface
 */
export interface JwtPayload {
  id: string;
  email: string;
  role: string;
}

/**
 * Response format for authentication
 */
export interface AuthResponse {
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  token: string;
  refreshToken: string;
}

