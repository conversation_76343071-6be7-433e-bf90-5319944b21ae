/**
 * M-Pesa B2C Service
 * Handles Business to Customer transactions including refunds, salary payments, etc.
 *
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 2024-01-15
 */

const axios = require('axios');
const B2CConfig = require('./config/B2CConfig');
const B2CUtils = require('./utils/B2CUtils');
const logger = require('../../../utils/logger');

class B2CService {
  constructor() {
    this.config = new B2CConfig();
    this.accessToken = null;
    this.tokenExpiry = null;
  }

  /**
   * Get access token for M-Pesa API
   */
  async getAccessToken() {
    try {
      if (this.accessToken && this.tokenExpiry && new Date() < this.tokenExpiry) {
        return this.accessToken;
      }

      const auth = Buffer.from(`${this.config.consumerKey}:${this.config.consumerSecret}`).toString('base64');

      const response = await axios.get(`${this.config.baseUrl}/oauth/v1/generate?grant_type=client_credentials`, {
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json'
        },
        timeout: this.config.apiTimeout
      });

      this.accessToken = response.data.access_token;
      this.tokenExpiry = new Date(Date.now() + (response.data.expires_in * 1000));

      logger.info('🔑 M-Pesa access token obtained successfully');
      return this.accessToken;
    } catch (error) {
      logger.error('❌ Failed to get M-Pesa access token:', error.message);
      throw new Error('Failed to authenticate with M-Pesa API');
    }
  }

  /**
   * Process B2C payment (refund, salary, etc.)
   */
  async processPayment(paymentData) {
    try {
      const accessToken = await this.getAccessToken();

      const requestBody = {
        InitiatorName: this.config.initiatorName,
        SecurityCredential: this.config.securityCredential,
        CommandID: this.config.getCommandId(paymentData.transactionType),
        Amount: Math.round(paymentData.amount),
        PartyA: this.config.businessShortCode,
        PartyB: B2CUtils.formatPhoneNumber(paymentData.phoneNumber),
        Remarks: paymentData.remarks || 'Payment from G20Shop',
        QueueTimeOutURL: this.config.queueTimeoutUrl,
        ResultURL: this.config.resultUrl,
        Occasion: paymentData.occasion || ''
      };

      logger.info('🚀 Initiating B2C payment:', {
        type: paymentData.transactionType,
        amount: paymentData.amount,
        phone: B2CUtils.maskPhoneNumber(paymentData.phoneNumber)
      });

      const response = await axios.post(
        `${this.config.baseUrl}/mpesa/b2c/v1/paymentrequest`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          timeout: this.config.apiTimeout
        }
      );

      return {
        success: true,
        data: response.data,
        conversationId: response.data.ConversationID,
        originatorConversationId: response.data.OriginatorConversationID
      };
    } catch (error) {
      logger.error('❌ B2C payment failed:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data || error.message
      };
    }
  }

  /**
   * Process refund
   */
  async processRefund(refundData) {
    return this.processPayment({
      ...refundData,
      transactionType: 'REFUND',
      remarks: refundData.reason || 'Refund from G20Shop'
    });
  }

  /**
   * Process salary payment
   */
  async processSalaryPayment(salaryData) {
    return this.processPayment({
      ...salaryData,
      transactionType: 'SALARY',
      remarks: `Salary payment for ${salaryData.employeeId || 'employee'}`
    });
  }

  /**
   * Process promotional payment
   */
  async processPromotionalPayment(promoData) {
    return this.processPayment({
      ...promoData,
      transactionType: 'PROMOTION',
      remarks: `Promotional payment - ${promoData.promotionCode || 'promo'}`
    });
  }

  /**
   * Get service health status
   */
  static getHealthStatus() {
    return {
      service: 'M-Pesa B2C Service',
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.MPESA_ENVIRONMENT || 'sandbox',
      features: {
        refunds: true,
        salaryPayments: true,
        promotionalPayments: true,
        generalPayments: true
      }
    };
  }

  /**
   * Validate payment request
   */
  validatePaymentRequest(paymentData) {
    const errors = [];

    if (!paymentData.phoneNumber) {
      errors.push('Phone number is required');
    } else if (!B2CUtils.isValidKenyanPhone(paymentData.phoneNumber)) {
      errors.push('Invalid Kenyan phone number format');
    }

    if (!paymentData.amount || paymentData.amount <= 0) {
      errors.push('Valid amount is required');
    }

    const amountValidation = this.config.validateAmount(paymentData.amount, paymentData.transactionType);
    if (!amountValidation.isValid) {
      errors.push(...amountValidation.errors);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Process B2C payment (main method called by controller)
   */
  static async processB2CPayment(paymentData) {
    try {
      const service = new B2CService();
      const validation = service.validatePaymentRequest(paymentData);

      if (!validation.isValid) {
        return {
          success: false,
          message: 'Validation failed',
          errors: validation.errors
        };
      }

      const result = await service.processPayment(paymentData);

      if (result.success) {
        // Save transaction to database
        const { B2CTransaction } = require('../../../db/models');
        await B2CTransaction.create({
          userId: paymentData.userId,
          orderId: paymentData.orderId,
          conversationId: result.conversationId,
          originatorConversationId: result.originatorConversationId,
          amount: paymentData.amount,
          phoneNumber: B2CUtils.formatPhoneNumber(paymentData.phoneNumber),
          transactionType: paymentData.transactionType || 'GENERAL',
          status: 'SUBMITTED',
          remarks: paymentData.remarks,
          occasion: paymentData.occasion,
          initiatedAt: new Date(),
          metadata: paymentData.metadata
        });

        return {
          success: true,
          message: 'Payment initiated successfully',
          data: result.data
        };
      }

      return result;
    } catch (error) {
      logger.error('❌ B2C payment processing error:', error);
      return {
        success: false,
        message: 'Failed to process payment',
        error: error.message
      };
    }
  }

  /**
   * Process refund (static method for controller)
   */
  static async processRefund(refundData) {
    try {
      const service = new B2CService();
      return await service.processRefund(refundData);
    } catch (error) {
      logger.error('❌ Refund processing error:', error);
      return {
        success: false,
        message: 'Failed to process refund',
        error: error.message
      };
    }
  }

  /**
   * Process salary payment (static method for controller)
   */
  static async processSalaryPayment(salaryData) {
    try {
      const service = new B2CService();
      return await service.processSalaryPayment(salaryData);
    } catch (error) {
      logger.error('❌ Salary payment processing error:', error);
      return {
        success: false,
        message: 'Failed to process salary payment',
        error: error.message
      };
    }
  }

  /**
   * Process promotional payment (static method for controller)
   */
  static async processPromotionalPayment(promoData) {
    try {
      const service = new B2CService();
      return await service.processPromotionalPayment(promoData);
    } catch (error) {
      logger.error('❌ Promotional payment processing error:', error);
      return {
        success: false,
        message: 'Failed to process promotional payment',
        error: error.message
      };
    }
  }

  /**
   * Get transaction status
   */
  static async getTransactionStatus(transactionId) {
    try {
      const { B2CTransaction } = require('../../../db/models');
      const transaction = await B2CTransaction.findByPk(transactionId);

      if (!transaction) {
        return {
          success: false,
          message: 'Transaction not found'
        };
      }

      return {
        success: true,
        data: B2CUtils.getTransactionSummary(transaction)
      };
    } catch (error) {
      logger.error('❌ Get transaction status error:', error);
      return {
        success: false,
        message: 'Failed to get transaction status',
        error: error.message
      };
    }
  }

  /**
   * Get transaction history
   */
  static async getTransactionHistory(filters) {
    try {
      const { B2CTransaction } = require('../../../db/models');
      const { Op } = require('sequelize');

      const where = {};
      const { status, transactionType, phoneNumber, startDate, endDate, page = 1, limit = 20 } = filters;

      if (status) where.status = status;
      if (transactionType) where.transactionType = transactionType;
      if (phoneNumber) where.phoneNumber = { [Op.like]: `%${phoneNumber}%` };
      if (startDate && endDate) {
        where.initiatedAt = {
          [Op.between]: [new Date(startDate), new Date(endDate)]
        };
      }

      const offset = (page - 1) * limit;

      const transactions = await B2CTransaction.findAndCountAll({
        where,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['initiatedAt', 'DESC']]
      });

      return {
        success: true,
        data: {
          transactions: transactions.rows.map(t => B2CUtils.getTransactionSummary(t)),
          pagination: {
            total: transactions.count,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(transactions.count / limit)
          }
        }
      };
    } catch (error) {
      logger.error('❌ Get transaction history error:', error);
      return {
        success: false,
        message: 'Failed to get transaction history',
        error: error.message
      };
    }
  }
}

module.exports = B2CService;
