const express = require('express');
const router = express.Router();
const reviewsController = require('../controllers/reviews.controller');
const { authenticate, authorize } = require('../middlewares/auth.middleware');
const { validateReview, validateReviewUpdate } = require('../validators/review.validator');
const upload = require('../middlewares/upload.middleware');

// Public routes
router.get(
  '/product/:productId',
  reviewsController.getProductReviews
);

router.get(
  '/:id',
  reviewsController.getReview
);

// Protected routes (authenticated users)
router.post(
  '/',
  authenticate,
  upload.array('images', 5),
  validateReview,
  reviewsController.createReview
);

router.put(
  '/:id',
  authenticate,
  upload.array('images', 5),
  validateReviewUpdate,
  reviewsController.updateReview
);

router.delete(
  '/:id',
  authenticate,
  reviewsController.deleteReview
);

router.get(
  '/user/me',
  authenticate,
  reviewsController.getUserReviews
);

router.post(
  '/:id/helpful',
  authenticate,
  reviewsController.markReviewHelpfulness
);

// Admin routes
router.patch(
  '/:id/moderate',
  authenticate,
  authorize(['admin']),
  reviewsController.moderateReview
);

module.exports = router;
