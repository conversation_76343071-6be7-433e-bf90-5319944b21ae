/**
 * Test script to debug M-Pesa service directly
 * Run this with: node test-mpesa-service.js
 */

require('dotenv').config();
const axios = require('axios');

// M-Pesa configuration from environment
const config = {
  environment: process.env.MPESA_ENVIRONMENT || 'sandbox',
  consumerKey: process.env.MPESA_CONSUMER_KEY,
  consumerSecret: process.env.MPESA_CONSUMER_SECRET,
  businessShortCode: process.env.MPESA_BUSINESS_SHORT_CODE,
  passkey: process.env.MPESA_LIPA_NA_MPESA_ONLINE_PASSKEY,
  callbackUrl: process.env.MPESA_CALLBACK_URL
};

// Set base URL based on environment
const baseUrl = config.environment === 'production' 
  ? 'https://api.safaricom.co.ke'
  : 'https://sandbox.safaricom.co.ke';

console.log('🔧 M-Pesa Configuration:');
console.log('Environment:', config.environment);
console.log('Consumer Key:', config.consumerKey ? `${config.consumerKey.substring(0, 10)}...` : 'NOT SET');
console.log('Consumer Secret:', config.consumerSecret ? `${config.consumerSecret.substring(0, 10)}...` : 'NOT SET');
console.log('Business Short Code:', config.businessShortCode);
console.log('Passkey:', config.passkey ? `${config.passkey.substring(0, 10)}...` : 'NOT SET');
console.log('Callback URL:', config.callbackUrl);
console.log('Base URL:', baseUrl);
console.log('');

// Test 1: Generate Access Token
async function testAccessToken() {
  try {
    console.log('🔑 Testing access token generation...');
    
    if (!config.consumerKey || !config.consumerSecret) {
      throw new Error('Consumer key or secret not configured');
    }
    
    const auth = Buffer.from(`${config.consumerKey}:${config.consumerSecret}`).toString('base64');
    
    const response = await axios.get(
      `${baseUrl}/oauth/v1/generate?grant_type=client_credentials`,
      {
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      }
    );
    
    console.log('✅ Access token generated successfully!');
    console.log('Token:', response.data.access_token ? `${response.data.access_token.substring(0, 20)}...` : 'NO TOKEN');
    console.log('Expires in:', response.data.expires_in, 'seconds');
    
    return response.data.access_token;
    
  } catch (error) {
    console.log('❌ Access token generation failed!');
    
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error data:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log('Network error - no response received');
      console.log('Error:', error.message);
    } else {
      console.log('Error:', error.message);
    }
    
    return null;
  }
}

// Test 2: Generate Password for STK Push
function generatePassword() {
  const timestamp = new Date().toISOString().replace(/[^0-9]/g, '').slice(0, -3);
  const password = Buffer.from(`${config.businessShortCode}${config.passkey}${timestamp}`).toString('base64');
  
  return { password, timestamp };
}

// Test 3: Test STK Push (simplified)
async function testSTKPush(accessToken) {
  try {
    console.log('💳 Testing STK Push...');
    
    if (!accessToken) {
      throw new Error('No access token available');
    }
    
    const { password, timestamp } = generatePassword();
    const testPhone = '************';
    const testAmount = 1;
    
    const requestBody = {
      BusinessShortCode: config.businessShortCode,
      Password: password,
      Timestamp: timestamp,
      TransactionType: 'CustomerPayBillOnline',
      Amount: testAmount,
      PartyA: testPhone,
      PartyB: config.businessShortCode,
      PhoneNumber: testPhone,
      CallBackURL: config.callbackUrl,
      AccountReference: 'TEST123',
      TransactionDesc: 'Test payment'
    };
    
    console.log('Request body:', JSON.stringify(requestBody, null, 2));
    
    const response = await axios.post(
      `${baseUrl}/mpesa/stkpush/v1/processrequest`,
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    );
    
    console.log('✅ STK Push test successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('❌ STK Push test failed!');
    
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error data:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log('Network error - no response received');
      console.log('Error:', error.message);
    } else {
      console.log('Error:', error.message);
    }
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting M-Pesa service tests...\n');
  
  // Test 1: Access token
  const accessToken = await testAccessToken();
  
  console.log(''); // Empty line
  
  // Test 2: STK Push (only if access token works)
  if (accessToken) {
    await testSTKPush(accessToken);
  } else {
    console.log('⏭️ Skipping STK Push test - no access token');
  }
}

// Run the tests
runTests().catch(console.error);
