// Product Types
export interface Product {
  id: number;
  name: string;
  slug: string;
  description: string;
  price: number;
  images: ProductImage[];
  categories: Category[];
  inStock: boolean;
  rating?: number;
  reviewCount?: number;
}

export interface ProductImage {
  id: number;
  url: string;
  alt?: string;
}

export interface Category {
  id: number;
  name: string;
  slug: string;
}

// Cart Types
export interface CartItem {
  id: number;
  productId: number;
  quantity: number;
  product: Product;
}

export interface Cart {
  id: number;
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
}

// Wishlist Types
export interface WishlistItem {
  id: number;
  productId: number;
  product: Product;
}

export interface Wishlist {
  id: number;
  items: WishlistItem[];
}

// User Types
export interface User {
  id: number;
  name: string;
  email: string;
  role: string;
}

// Auth Types
export interface AuthResponse {
  token: string;
  refreshToken: string;
  user: User;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

export interface PaginatedResponse<T> {
  items: T[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
}

// Form Types
export interface LoginFormData {
  email: string;
  password: string;
}

export interface RegisterFormData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}
