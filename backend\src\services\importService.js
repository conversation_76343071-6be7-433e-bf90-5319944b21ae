const { Product } = require('../db/models');
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const slugify = require('slugify');

class ImportService {
  constructor() {
    this.supportedSources = ['file_upload', 'http_url', 'ftp', 'google_sheets', 'api_webhook'];
  }

  // Main method to execute an import job
  async executeImportJob(importJob) {
    console.log(`🔄 Executing import job: ${importJob.name} (${importJob.sourceType})`);
    
    switch (importJob.sourceType) {
      case 'file_upload':
        return await this.importFromFile(importJob);
      case 'http_url':
        return await this.importFromUrl(importJob);
      case 'ftp':
        return await this.importFromFtp(importJob);
      case 'google_sheets':
        return await this.importFromGoogleSheets(importJob);
      case 'api_webhook':
        return await this.importFromApiWebhook(importJob);
      default:
        throw new Error(`Unsupported source type: ${importJob.sourceType}`);
    }
  }

  // Import from uploaded file
  async importFromFile(importJob) {
    const config = importJob.sourceConfig || {};
    const filePath = config.filePath;

    if (!filePath) {
      throw new Error('File path not specified in source configuration');
    }

    try {
      // Check if file exists
      await fs.access(filePath);
      
      // Read and parse file
      const fileContent = await fs.readFile(filePath, 'utf8');
      const data = this.parseCSVContent(fileContent);
      
      // Process the data
      const result = await this.processImportData(data, importJob);
      
      return {
        source: 'file_upload',
        filePath,
        ...result
      };
    } catch (error) {
      throw new Error(`Failed to import from file: ${error.message}`);
    }
  }

  // Import from HTTP URL
  async importFromUrl(importJob) {
    const config = importJob.sourceConfig || {};
    const url = config.url;

    if (!url) {
      throw new Error('URL not specified in source configuration');
    }

    try {
      console.log(`📥 Downloading from URL: ${url}`);
      
      const response = await axios.get(url, {
        timeout: 30000, // 30 second timeout
        headers: config.headers || {}
      });

      let data;
      if (typeof response.data === 'string') {
        // Assume CSV format
        data = this.parseCSVContent(response.data);
      } else {
        // Assume JSON format
        data = Array.isArray(response.data) ? response.data : response.data.products || [];
      }

      const result = await this.processImportData(data, importJob);
      
      return {
        source: 'http_url',
        url,
        ...result
      };
    } catch (error) {
      throw new Error(`Failed to import from URL: ${error.message}`);
    }
  }

  // Import from FTP (placeholder)
  async importFromFtp(importJob) {
    // This would require an FTP client library like 'ftp' or 'basic-ftp'
    throw new Error('FTP import is not yet implemented. Please use HTTP URL or file upload.');
  }

  // Import from Google Sheets (placeholder)
  async importFromGoogleSheets(importJob) {
    const config = importJob.sourceConfig || {};
    const sheetId = config.sheetId;
    const apiKey = config.apiKey;

    if (!sheetId || !apiKey) {
      throw new Error('Google Sheets ID and API key required');
    }

    try {
      // Use Google Sheets API to fetch data
      const url = `https://sheets.googleapis.com/v4/spreadsheets/${sheetId}/values/Sheet1?key=${apiKey}`;
      
      const response = await axios.get(url);
      const rows = response.data.values || [];
      
      if (rows.length < 2) {
        throw new Error('Sheet must contain at least a header row and one data row');
      }

      // Convert to object format
      const headers = rows[0];
      const data = rows.slice(1).map(row => {
        const obj = {};
        headers.forEach((header, index) => {
          obj[header.toLowerCase().replace(/\s+/g, '_')] = row[index] || '';
        });
        return obj;
      });

      const result = await this.processImportData(data, importJob);
      
      return {
        source: 'google_sheets',
        sheetId,
        ...result
      };
    } catch (error) {
      throw new Error(`Failed to import from Google Sheets: ${error.message}`);
    }
  }

  // Import from API webhook (placeholder)
  async importFromApiWebhook(importJob) {
    const config = importJob.sourceConfig || {};
    const webhookUrl = config.webhookUrl;

    if (!webhookUrl) {
      throw new Error('Webhook URL not specified');
    }

    try {
      const response = await axios.post(webhookUrl, {
        action: 'fetch_products',
        jobId: importJob.id
      }, {
        timeout: 30000,
        headers: config.headers || {}
      });

      const data = Array.isArray(response.data) ? response.data : response.data.products || [];
      const result = await this.processImportData(data, importJob);
      
      return {
        source: 'api_webhook',
        webhookUrl,
        ...result
      };
    } catch (error) {
      throw new Error(`Failed to import from API webhook: ${error.message}`);
    }
  }

  // Parse CSV content
  parseCSVContent(content) {
    const lines = content.split('\n').filter(line => line.trim());
    
    if (lines.length < 2) {
      throw new Error('CSV must contain at least a header row and one data row');
    }

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    
    return lines.slice(1).map((line, index) => {
      const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
      const row = {};
      
      headers.forEach((header, i) => {
        row[header.toLowerCase().replace(/\s+/g, '_')] = values[i] || '';
      });
      
      row._rowNumber = index + 2;
      return row;
    });
  }

  // Process import data (same logic as bulk import)
  async processImportData(data, importJob) {
    const results = {
      total: data.length,
      imported: 0,
      failed: 0,
      errors: []
    };

    for (let i = 0; i < data.length; i++) {
      const productData = data[i];
      const rowNumber = productData._rowNumber || i + 2;

      try {
        // Validate required fields
        if (!productData.name || !productData.brand || !productData.sku || !productData.regular_price) {
          results.errors.push({
            row: rowNumber,
            error: 'Missing required fields: name, brand, sku, or regular_price'
          });
          results.failed++;
          continue;
        }

        // Check if SKU already exists
        const existingSku = await Product.findOne({ where: { sku: productData.sku } });
        if (existingSku) {
          results.errors.push({
            row: rowNumber,
            error: `Product with SKU '${productData.sku}' already exists`
          });
          results.failed++;
          continue;
        }

        // Generate slug from name
        const slug = slugify(productData.name, { lower: true, strict: true });

        // Create product
        await Product.create({
          name: productData.name,
          brand: productData.brand,
          model: productData.model || '',
          description: productData.description || '',
          sku: productData.sku,
          regularPrice: parseFloat(productData.regular_price || productData.price || '0'),
          salePrice: productData.sale_price ? parseFloat(productData.sale_price) : undefined,
          cost: productData.cost ? parseFloat(productData.cost) : undefined,
          stockQuantity: parseInt(productData.stock_quantity || productData.stock || '0'),
          minOrderQuantity: parseInt(productData.min_order_quantity || '1'),
          maxOrderQuantity: productData.max_order_quantity ? parseInt(productData.max_order_quantity) : undefined,
          weight: productData.weight ? parseFloat(productData.weight) : undefined,
          dimensions: productData.dimensions || '',
          featured: productData.featured === 'true' || productData.featured === '1',
          slug,
          metaTitle: productData.meta_title || productData.name,
          metaDescription: productData.meta_description,
          metaKeywords: productData.meta_keywords,
          isActive: true
        });

        results.imported++;

      } catch (error) {
        console.error(`Error importing product at row ${rowNumber}:`, error);
        results.errors.push({
          row: rowNumber,
          error: error.message || 'Unknown error occurred'
        });
        results.failed++;
      }
    }

    console.log(`📊 Import completed: ${results.imported} imported, ${results.failed} failed`);
    return results;
  }

  // Validate import job configuration
  validateJobConfig(sourceType, sourceConfig) {
    switch (sourceType) {
      case 'file_upload':
        if (!sourceConfig.filePath) {
          throw new Error('File path is required for file upload source');
        }
        break;
      case 'http_url':
        if (!sourceConfig.url) {
          throw new Error('URL is required for HTTP URL source');
        }
        break;
      case 'google_sheets':
        if (!sourceConfig.sheetId || !sourceConfig.apiKey) {
          throw new Error('Sheet ID and API key are required for Google Sheets source');
        }
        break;
      case 'api_webhook':
        if (!sourceConfig.webhookUrl) {
          throw new Error('Webhook URL is required for API webhook source');
        }
        break;
      default:
        throw new Error(`Unsupported source type: ${sourceType}`);
    }
  }
}

module.exports = new ImportService();
