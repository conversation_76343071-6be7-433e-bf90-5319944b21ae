import React from 'react';
import { SignUpButton as ClerkSignUpButton, useAuth } from '@clerk/clerk-react';

export const SignUpButton: React.FC = () => {
  const { isSignedIn } = useAuth();

  if (isSignedIn) {
    return null;
  }

  return (
    <ClerkSignUpButton mode="modal">
      <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
        Sign Up
      </button>
    </ClerkSignUpButton>
  );
};
