const { DataTypes } = require('sequelize');
const sequelize = require('../../config/database');

const ImportJob = sequelize.define('ImportJob', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 255]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    sourceType: {
      type: DataTypes.ENUM('file_upload', 'ftp', 'http_url', 'google_sheets', 'api_webhook'),
      allowNull: false,
      defaultValue: 'file_upload'
    },
    sourceConfig: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Configuration for the import source (URLs, credentials, etc.)'
    },
    schedule: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Cron expression for scheduling (e.g., "0 2 * * *" for daily at 2 AM)'
    },
    timezone: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'UTC'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    lastRunAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    nextRunAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    lastRunStatus: {
      type: DataTypes.ENUM('pending', 'running', 'success', 'failed', 'cancelled'),
      allowNull: true
    },
    lastRunResult: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Results from the last import run (imported count, errors, etc.)'
    },
    totalRuns: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    successfulRuns: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    failedRuns: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    createdBy: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'User ID who created this job'
    },
    updatedBy: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'User ID who last updated this job'
    }
  }, {
    tableName: 'import_jobs',
    timestamps: true,
    indexes: [
      {
        fields: ['isActive']
      },
      {
        fields: ['nextRunAt']
      },
      {
        fields: ['createdBy']
      }
    ]
  });

module.exports = ImportJob;
