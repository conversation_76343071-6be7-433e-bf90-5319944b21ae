# TechGear Shop Backend Structure

## Project Structure

```
techgear-backend/
├── src/                          # Source code
│   ├── config/                   # Configuration files
│   │   ├── database.js           # Database configuration
│   │   ├── auth.js               # Authentication configuration
│   │   ├── email.js              # Email service configuration
│   │   └── index.js              # Export all configurations
│   │
│   ├── api/                      # API routes and controllers
│   │   ├── routes/               # Route definitions
│   │   │   ├── auth.routes.js    # Authentication routes
│   │   │   ├── products.routes.js # Product routes
│   │   │   ├── repairs.routes.js # Repair service routes
│   │   │   ├── inventory.routes.js # Inventory management routes
│   │   │   ├── customers.routes.js # Customer management routes
│   │   │   └── index.js          # Export all routes
│   │   │
│   │   ├── controllers/          # Route controllers
│   │   │   ├── auth.controller.js # Authentication controller
│   │   │   ├── products.controller.js # Products controller
│   │   │   ├── repairs.controller.js # Repair services controller
│   │   │   ├── inventory.controller.js # Inventory controller
│   │   │   └── customers.controller.js # Customer controller
│   │   │
│   │   ├── middlewares/          # Custom middlewares
│   │   │   ├── auth.middleware.js # Authentication middleware
│   │   │   ├── error.middleware.js # Error handling middleware
│   │   │   ├── validation.middleware.js # Input validation middleware
│   │   │   └── logging.middleware.js # Request logging middleware
│   │   │
│   │   └── validators/           # Request validators
│   │       ├── product.validator.js # Product validation rules
│   │       ├── repair.validator.js # Repair service validation rules
│   │       └── user.validator.js # User validation rules
│   │
│   ├── db/                       # Database related files
│   │   ├── models/               # Database models
│   │   │   ├── User.js           # User model
│   │   │   ├── Product.js        # Product model
│   │   │   ├── Category.js       # Product category model
│   │   │   ├── Inventory.js      # Inventory model
│   │   │   ├── Customer.js       # Customer model
│   │   │   ├── RepairTicket.js   # Repair ticket model
│   │   │   ├── RepairStatus.js   # Repair status model
│   │   │   ├── Order.js          # Order model
│   │   │   └── OrderItem.js      # Order item model
│   │   │
│   │   ├── migrations/           # Database migrations
│   │   │   ├── 20230101000000-create-users.js
│   │   │   ├── 20230101000001-create-products.js
│   │   │   └── ...
│   │   │
│   │   ├── seeders/              # Database seeders
│   │   │   ├── 20230101000000-demo-users.js
│   │   │   ├── 20230101000001-product-categories.js
│   │   │   └── ...
│   │   │
│   │   └── repositories/         # Data access layer
│   │       ├── user.repository.js
│   │       ├── product.repository.js
│   │       ├── repair.repository.js
│   │       └── ...
│   │
│   ├── services/                 # Business logic
│   │   ├── auth.service.js       # Authentication service
│   │   ├── product.service.js    # Product service
│   │   ├── inventory.service.js  # Inventory management service
│   │   ├── repair.service.js     # Repair service
│   │   ├── email.service.js      # Email notification service
│   │   ├── report.service.js     # Reporting service
│   │   └── ...
│   │
│   ├── utils/                    # Utility functions
│   │   ├── logger.js             # Logging utility
│   │   ├── errors.js             # Custom error classes
│   │   ├── helpers.js            # Helper functions
│   │   └── constants.js          # Constants and enums
│   │
│   ├── jobs/                     # Background jobs
│   │   ├── reminder.job.js       # Reminder notifications job
│   │   ├── report.job.js         # Automated reporting job
│   │   └── cleanup.job.js        # Data cleanup job
│   │
│   ├── app.js                    # Express app setup
│   └── server.js                 # Server entry point
│
├── tests/                        # Test files
│   ├── unit/                     # Unit tests
│   │   ├── services/             # Service tests
│   │   └── controllers/          # Controller tests
│   │
│   ├── integration/              # Integration tests
│   │   ├── api/                  # API endpoint tests
│   │   └── db/                   # Database tests
│   │
│   └── fixtures/                 # Test fixtures
│       ├── users.js              # User test data
│       ├── products.js           # Product test data
│       └── ...
│
├── scripts/                      # Utility scripts
│   ├── db-setup.js               # Database setup script
│   ├── generate-api-docs.js      # API documentation generator
│   └── ...
│
├── docs/                         # Documentation
│   ├── api/                      # API documentation
│   └── database/                 # Database schema documentation
│
├── .env.example                  # Example environment variables
├── .eslintrc.js                  # ESLint configuration
├── .prettierrc                   # Prettier configuration
├── .gitignore                    # Git ignore file
├── package.json                  # NPM package file
├── README.md                     # Project README
└── docker-compose.yml            # Docker Compose configuration
```

## Key Components

### Database Models

1. **User Model**
   - Tracks staff members with different roles (admin, sales, technician)
   - Manages authentication and permissions

2. **Product Model**
   - Stores product information (name, description, price, etc.)
   - Links to categories and inventory

3. **Inventory Model**
   - Tracks stock levels for each product
   - Records purchase history, current quantity, and reorder thresholds

4. **Customer Model**
   - Stores customer information
   - Links to orders and repair tickets

5. **RepairTicket Model**
   - Tracks repair and maintenance services
   - Includes device details, issue description, status, and timeline

### API Endpoints

1. **Authentication**
   - `/api/auth/login`
   - `/api/auth/register`
   - `/api/auth/refresh-token`

2. **Products**
   - `/api/products` - CRUD operations
   - `/api/products/categories`

3. **Inventory**
   - `/api/inventory` - Stock management
   - `/api/inventory/transactions` - Purchase and sales records

4. **Repairs**
   - `/api/repairs` - CRUD for repair tickets
   - `/api/repairs/:id/status` - Update repair status
   - `/api/repairs/:id/notes` - Add technician notes

5. **Customers**
   - `/api/customers` - CRUD operations
   - `/api/customers/:id/repairs` - Customer repair history
   - `/api/customers/:id/purchases` - Customer purchase history

6. **Reports**
   - `/api/reports/sales` - Sales reports
   - `/api/reports/repairs` - Repair service reports
   - `/api/reports/inventory` - Inventory reports

### Services

1. **Auth Service**
   - Handles user authentication and authorization

2. **Product Service**
   - Manages product information and categories

3. **Inventory Service**
   - Handles stock management and transactions

4. **Repair Service**
   - Manages repair tickets and status updates

5. **Report Service**
   - Generates business reports and analytics

6. **Email Service**
   - Sends notifications to customers and staff
