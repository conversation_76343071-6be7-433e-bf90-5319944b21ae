/**
 * M-Pesa B2C User Routes
 * User-accessible routes for B2C operations
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 2024-01-15
 */

const express = require('express');
const router = express.Router();
const { authenticateWithClerk } = require('../../../middlewares/clerk-auth.middleware');
const B2CUtils = require('../../../../services/mpesa/b2c/utils/B2CUtils');

// Rate limiting for user B2C endpoints
const rateLimit = require('express-rate-limit');

const userB2CRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10, // Limit user requests
  skipSuccessfulRequests: true,
  message: {
    success: false,
    message: 'Too many B2C requests, please try again later.',
    error: 'Rate limit exceeded'
  }
});

// Apply user authentication to all routes
router.use(authenticateWithClerk);
router.use(userB2CRateLimit);

/**
 * @route   GET /api/mpesa/b2c/user/my-transactions
 * @desc    Get user's B2C transactions (refunds, etc.)
 * @access  Private (Authenticated users)
 * @query   { status?, transactionType?, page?, limit? }
 */
router.get('/my-transactions', async (req, res) => {
  try {
    const { B2CTransaction } = require('../../../../db/models');
    const userId = req.user.id;
    const { status, transactionType, page = 1, limit = 20 } = req.query;

    const where = { userId };
    if (status) where.status = status;
    if (transactionType) where.transactionType = transactionType;

    const offset = (page - 1) * limit;

    const transactions = await B2CTransaction.findAndCountAll({
      where,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['initiatedAt', 'DESC']],
      attributes: [
        'id', 'conversationId', 'amount', 'transactionType', 'status',
        'remarks', 'mpesaReceiptNumber', 'initiatedAt', 'completedAt',
        'resultDescription'
      ]
    });

    // Format transactions for user display
    const formattedTransactions = transactions.rows.map(transaction => ({
      id: transaction.id,
      conversationId: transaction.conversationId,
      amount: B2CUtils.formatCurrency(transaction.amount),
      type: transaction.transactionType,
      typeDescription: B2CUtils.getTransactionTypeDescription(transaction.transactionType),
      status: transaction.status,
      statusDescription: B2CUtils.getStatusDescription(transaction.status),
      remarks: transaction.remarks,
      receiptNumber: transaction.mpesaReceiptNumber,
      initiatedAt: transaction.initiatedAt,
      completedAt: transaction.completedAt,
      resultDescription: transaction.resultDescription,
      duration: transaction.completedAt ? 
        Math.round((new Date(transaction.completedAt) - new Date(transaction.initiatedAt)) / 1000) : null
    }));

    res.json({
      success: true,
      data: {
        transactions: formattedTransactions,
        pagination: {
          total: transactions.count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(transactions.count / limit)
        }
      }
    });
  } catch (error) {
    console.error('❌ User B2C transactions query failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve your transactions'
    });
  }
});

/**
 * @route   GET /api/mpesa/b2c/user/my-transaction/:id
 * @desc    Get specific user's B2C transaction
 * @access  Private (Authenticated users - own transactions only)
 */
router.get('/my-transaction/:id', async (req, res) => {
  try {
    const { B2CTransaction } = require('../../../../db/models');
    const userId = req.user.id;
    const { id } = req.params;

    const transaction = await B2CTransaction.findOne({
      where: { id, userId },
      attributes: [
        'id', 'conversationId', 'amount', 'transactionType', 'status',
        'remarks', 'occasion', 'mpesaReceiptNumber', 'resultDescription',
        'initiatedAt', 'completedAt', 'metadata'
      ]
    });

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }

    // Format transaction for user display
    const formattedTransaction = {
      id: transaction.id,
      conversationId: transaction.conversationId,
      amount: B2CUtils.formatCurrency(transaction.amount),
      type: transaction.transactionType,
      typeDescription: B2CUtils.getTransactionTypeDescription(transaction.transactionType),
      status: transaction.status,
      statusDescription: B2CUtils.getStatusDescription(transaction.status),
      remarks: transaction.remarks,
      occasion: transaction.occasion,
      receiptNumber: transaction.mpesaReceiptNumber,
      resultDescription: transaction.resultDescription,
      initiatedAt: transaction.initiatedAt,
      completedAt: transaction.completedAt,
      duration: transaction.completedAt ? 
        Math.round((new Date(transaction.completedAt) - new Date(transaction.initiatedAt)) / 1000) : null,
      metadata: transaction.metadata
    };

    res.json({
      success: true,
      data: formattedTransaction
    });
  } catch (error) {
    console.error('❌ User B2C transaction query failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve transaction'
    });
  }
});

/**
 * @route   GET /api/mpesa/b2c/user/my-refunds
 * @desc    Get user's refund transactions
 * @access  Private (Authenticated users)
 * @query   { status?, page?, limit? }
 */
router.get('/my-refunds', async (req, res) => {
  try {
    const { B2CTransaction } = require('../../../../db/models');
    const userId = req.user.id;
    const { status, page = 1, limit = 20 } = req.query;

    const where = { 
      userId,
      transactionType: 'REFUND'
    };
    if (status) where.status = status;

    const offset = (page - 1) * limit;

    const refunds = await B2CTransaction.findAndCountAll({
      where,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['initiatedAt', 'DESC']],
      attributes: [
        'id', 'conversationId', 'amount', 'status', 'remarks',
        'mpesaReceiptNumber', 'initiatedAt', 'completedAt', 'orderId'
      ]
    });

    // Format refunds for user display
    const formattedRefunds = refunds.rows.map(refund => ({
      id: refund.id,
      conversationId: refund.conversationId,
      amount: B2CUtils.formatCurrency(refund.amount),
      status: refund.status,
      statusDescription: B2CUtils.getStatusDescription(refund.status),
      remarks: refund.remarks,
      receiptNumber: refund.mpesaReceiptNumber,
      orderId: refund.orderId,
      initiatedAt: refund.initiatedAt,
      completedAt: refund.completedAt,
      isCompleted: refund.status === 'COMPLETED',
      isPending: ['PENDING', 'SUBMITTED'].includes(refund.status)
    }));

    res.json({
      success: true,
      data: {
        refunds: formattedRefunds,
        pagination: {
          total: refunds.count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(refunds.count / limit)
        },
        summary: {
          totalRefunds: refunds.count,
          completedRefunds: formattedRefunds.filter(r => r.isCompleted).length,
          pendingRefunds: formattedRefunds.filter(r => r.isPending).length
        }
      }
    });
  } catch (error) {
    console.error('❌ User refunds query failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve your refunds'
    });
  }
});

/**
 * @route   GET /api/mpesa/b2c/user/transaction-summary
 * @desc    Get user's B2C transaction summary
 * @access  Private (Authenticated users)
 */
router.get('/transaction-summary', async (req, res) => {
  try {
    const { B2CTransaction } = require('../../../../db/models');
    const { Op, fn, col } = require('sequelize');
    const userId = req.user.id;

    // Get summary statistics
    const summary = await B2CTransaction.findAll({
      where: { userId },
      attributes: [
        'transactionType',
        'status',
        [fn('COUNT', col('id')), 'count'],
        [fn('SUM', col('amount')), 'totalAmount']
      ],
      group: ['transactionType', 'status'],
      raw: true
    });

    // Get recent transactions
    const recentTransactions = await B2CTransaction.findAll({
      where: { userId },
      limit: 5,
      order: [['initiatedAt', 'DESC']],
      attributes: [
        'id', 'conversationId', 'amount', 'transactionType', 'status',
        'initiatedAt', 'mpesaReceiptNumber'
      ]
    });

    // Format summary data
    const formattedSummary = {
      byType: {},
      byStatus: {},
      totals: {
        allTransactions: 0,
        totalAmount: 0,
        completedTransactions: 0,
        pendingTransactions: 0,
        failedTransactions: 0
      }
    };

    summary.forEach(item => {
      const type = item.transactionType;
      const status = item.status;
      const count = parseInt(item.count);
      const amount = parseFloat(item.totalAmount) || 0;

      // By type
      if (!formattedSummary.byType[type]) {
        formattedSummary.byType[type] = { count: 0, amount: 0 };
      }
      formattedSummary.byType[type].count += count;
      formattedSummary.byType[type].amount += amount;

      // By status
      if (!formattedSummary.byStatus[status]) {
        formattedSummary.byStatus[status] = { count: 0, amount: 0 };
      }
      formattedSummary.byStatus[status].count += count;
      formattedSummary.byStatus[status].amount += amount;

      // Totals
      formattedSummary.totals.allTransactions += count;
      formattedSummary.totals.totalAmount += amount;

      if (status === 'COMPLETED') {
        formattedSummary.totals.completedTransactions += count;
      } else if (['PENDING', 'SUBMITTED'].includes(status)) {
        formattedSummary.totals.pendingTransactions += count;
      } else if (['FAILED', 'TIMEOUT', 'CANCELLED'].includes(status)) {
        formattedSummary.totals.failedTransactions += count;
      }
    });

    // Format currency amounts
    Object.keys(formattedSummary.byType).forEach(type => {
      formattedSummary.byType[type].formattedAmount = B2CUtils.formatCurrency(formattedSummary.byType[type].amount);
    });

    Object.keys(formattedSummary.byStatus).forEach(status => {
      formattedSummary.byStatus[status].formattedAmount = B2CUtils.formatCurrency(formattedSummary.byStatus[status].amount);
    });

    formattedSummary.totals.formattedTotalAmount = B2CUtils.formatCurrency(formattedSummary.totals.totalAmount);

    res.json({
      success: true,
      data: {
        summary: formattedSummary,
        recentTransactions: recentTransactions.map(t => B2CUtils.getTransactionSummary(t))
      }
    });
  } catch (error) {
    console.error('❌ User transaction summary query failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve transaction summary'
    });
  }
});

module.exports = router;
