/**
 * Test script to check available products
 * Run this with: node test-products.js
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

async function testGetProducts() {
  try {
    console.log('📦 Testing get products...');
    
    const response = await axios.get(`${API_BASE_URL}/products`);
    
    console.log('✅ Products retrieved successfully!');
    console.log('📊 Total products:', response.data.products?.length || 0);
    
    if (response.data.products && response.data.products.length > 0) {
      console.log('\n🔍 Available products:');
      response.data.products.slice(0, 5).forEach((product, index) => {
        console.log(`  ${index + 1}. ID: ${product.id}`);
        console.log(`     Name: ${product.name}`);
        console.log(`     Price: ${product.regularPrice || product.price}`);
        console.log(`     Stock: ${product.stockQuantity}`);
        console.log(`     Active: ${product.isActive}`);
        console.log('');
      });
      
      // Return the first active product with stock
      const availableProduct = response.data.products.find(p => 
        p.isActive && p.stockQuantity > 0
      );
      
      if (availableProduct) {
        console.log('✅ Found available product for testing:');
        console.log(`   ID: ${availableProduct.id}`);
        console.log(`   Name: ${availableProduct.name}`);
        return availableProduct.id;
      } else {
        console.log('❌ No available products found (need active products with stock)');
        return null;
      }
    } else {
      console.log('❌ No products found in database');
      return null;
    }
    
  } catch (error) {
    console.log('❌ Get products failed!');
    
    if (error.response) {
      console.log('📊 Status:', error.response.status);
      console.log('📋 Error data:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log('🌐 Network error - no response received');
    } else {
      console.log('⚠️ Error:', error.message);
    }
    return null;
  }
}

// Test server connectivity first
async function testServerConnection() {
  try {
    console.log('🔗 Testing server connection...');
    const response = await axios.get(`${API_BASE_URL.replace('/api', '')}/health`);
    console.log('✅ Server is responding:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Server connection failed:', error.message);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting product availability tests...\n');
  
  // Test server connection
  const serverOk = await testServerConnection();
  if (!serverOk) {
    console.log('❌ Cannot proceed - server is not responding');
    return;
  }
  
  console.log(''); // Empty line for readability
  
  // Test get products
  const productId = await testGetProducts();
  
  if (productId) {
    console.log(`\n🎯 Use this product ID in your checkout test: "${productId}"`);
  }
}

// Run the tests
runTests().catch(console.error);
