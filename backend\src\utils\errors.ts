/**
 * Custom API error codes
 */
export enum ErrorCode {
  // Authentication errors
  INVALID_CREDENTIALS = 'auth/invalid-credentials',
  TOKEN_EXPIRED = 'auth/token-expired',
  TOKEN_INVALID = 'auth/token-invalid',
  TOKEN_MISSING = 'auth/token-missing',
  USER_NOT_FOUND = 'auth/user-not-found',
  USER_INACTIVE = 'auth/user-inactive',
  
  // Authorization errors
  INSUFFICIENT_PERMISSIONS = 'auth/insufficient-permissions',
  ACCESS_DENIED = 'auth/access-denied',
  
  // Validation errors
  INVALID_DATA = 'validation/invalid-data',
  MISSING_FIELDS = 'validation/missing-fields',
  EMAIL_IN_USE = 'validation/email-in-use',
  INVALID_EMAIL = 'validation/invalid-email',
  INVALID_PASSWORD = 'validation/invalid-password',
  PASSWORD_MISMATCH = 'validation/password-mismatch',
  
  // Resource errors
  RESOURCE_NOT_FOUND = 'resource/not-found',
  RESOURCE_ALREADY_EXISTS = 'resource/already-exists',
  RESOURCE_CONFLICT = 'resource/conflict',
  
  // Server errors
  INTERNAL_SERVER_ERROR = 'server/internal-error',
  DATABASE_ERROR = 'server/database-error',
  EXTERNAL_API_ERROR = 'server/external-api-error'
}

/**
 * Base class for all application errors
 */
export class AppError extends Error {
  statusCode: number;
  code: ErrorCode;
  isOperational: boolean;
  
  constructor(message: string, statusCode: number, code: ErrorCode) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true; // Used to distinguish operational errors from programmer errors
    
    // Ensure the proper prototype chain (only necessary for TypeScript)
    Object.setPrototypeOf(this, new.target.prototype);
    
    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Authentication errors (401 Unauthorized)
 * Use when user is not authenticated or credentials are invalid
 */
export class AuthenticationError extends AppError {
  constructor(
    message: string = 'Authentication failed',
    code: ErrorCode = ErrorCode.INVALID_CREDENTIALS
  ) {
    super(message, 401, code);
    this.name = 'AuthenticationError';
  }
}

/**
 * Authorization errors (403 Forbidden)
 * Use when user is authenticated but doesn't have sufficient permissions
 */
export class AuthorizationError extends AppError {
  constructor(
    message: string = 'You do not have permission to access this resource',
    code: ErrorCode = ErrorCode.INSUFFICIENT_PERMISSIONS
  ) {
    super(message, 403, code);
    this.name = 'AuthorizationError';
  }
}

/**
 * Validation errors (400 Bad Request)
 * Use when request data fails validation
 */
export class ValidationError extends AppError {
  errors?: Record<string, string>; // Field-specific validation errors
  
  constructor(
    message: string = 'Validation failed',
    code: ErrorCode = ErrorCode.INVALID_DATA,
    errors?: Record<string, string>
  ) {
    super(message, 400, code);
    this.name = 'ValidationError';
    this.errors = errors;
  }
}

/**
 * Not Found errors (404 Not Found)
 * Use when requested resource doesn't exist
 */
export class NotFoundError extends AppError {
  constructor(
    message: string = 'Resource not found',
    code: ErrorCode = ErrorCode.RESOURCE_NOT_FOUND
  ) {
    super(message, 404, code);
    this.name = 'NotFoundError';
  }
}

/**
 * Conflict errors (409 Conflict)
 * Use when request conflicts with the current state of the server
 */
export class ConflictError extends AppError {
  constructor(
    message: string = 'Resource already exists',
    code: ErrorCode = ErrorCode.RESOURCE_ALREADY_EXISTS
  ) {
    super(message, 409, code);
    this.name = 'ConflictError';
  }
}

/**
 * Internal Server Error (500 Internal Server Error)
 * Use for unexpected errors that aren't handled more specifically
 */
export class InternalServerError extends AppError {
  constructor(
    message: string = 'Internal server error',
    code: ErrorCode = ErrorCode.INTERNAL_SERVER_ERROR
  ) {
    super(message, 500, code);
    this.name = 'InternalServerError';
    this.isOperational = false; // Mark as a programmer error, not operational
  }
}

/**
 * Database Error (500 Internal Server Error)
 * Use for database-related errors
 */
export class DatabaseError extends AppError {
  constructor(
    message: string = 'Database error occurred',
    code: ErrorCode = ErrorCode.DATABASE_ERROR
  ) {
    super(message, 500, code);
    this.name = 'DatabaseError';
  }
}

/**
 * Creates a specific error based on the HTTP status code
 */
export const createErrorFromStatus = (
  statusCode: number,
  message?: string
): AppError => {
  switch (statusCode) {
    case 400:
      return new ValidationError(message);
    case 401:
      return new AuthenticationError(message);
    case 403:
      return new AuthorizationError(message);
    case 404:
      return new NotFoundError(message);
    case 409:
      return new ConflictError(message);
    default:
      return new InternalServerError(message);
  }
};

