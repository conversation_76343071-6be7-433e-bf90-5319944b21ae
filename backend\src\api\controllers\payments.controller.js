const paymentService = require('../../services/payment.service');
const { NotFoundError, ValidationError } = require('../../utils/errors');

/**
 * Create payment intent
 */
exports.createPaymentIntent = async (req, res, next) => {
  try {
    const { orderId } = req.body;
    const userId = req.user ? req.user.id : null;
    
    if (!orderId) {
      throw new ValidationError('Order ID is required');
    }
    
    const paymentIntent = await paymentService.createPaymentIntent(orderId, userId);
    
    res.status(200).json(paymentIntent);
  } catch (error) {
    next(error);
  }
};

/**
 * Process Stripe webhook
 */
exports.processWebhook = async (req, res, next) => {
  try {
    const signature = req.headers['stripe-signature'];
    
    if (!signature) {
      throw new ValidationError('Stripe signature is required');
    }
    
    // Process the webhook event
    const result = await paymentService.processPaymentWebhook(req.body);
    
    res.status(200).json({ received: true, ...result });
  } catch (error) {
    next(error);
  }
};

/**
 * Process refund (admin only)
 */
exports.processRefund = async (req, res, next) => {
  try {
    const { orderId } = req.params;
    const { amount, reason } = req.body;
    const adminId = req.user.id;
    
    if (!amount) {
      throw new ValidationError('Refund amount is required');
    }
    
    const refund = await paymentService.processRefund(
      orderId, 
      parseFloat(amount), 
      reason || 'requested_by_customer',
      adminId
    );
    
    res.status(200).json(refund);
  } catch (error) {
    next(error);
  }
};
