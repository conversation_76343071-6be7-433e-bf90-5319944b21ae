# G20 Shop Documentation

This directory contains documentation for the G20 Shop e-commerce platform.

## Directory Structure

```
docs/
├── api/                      # API documentation
│   ├── endpoints.md          # API endpoint documentation
│   ├── models.md             # Database model documentation
│   └── ...
├── frontend/                 # Frontend documentation
│   ├── components.md         # Component documentation
│   ├── state-management.md   # State management documentation
│   └── ...
└── README.md                 # This file
```

## API Documentation

The `api` directory contains documentation for the backend API, including:

- API endpoints
- Request and response formats
- Authentication and authorization
- Error handling
- Database models

## Frontend Documentation

The `frontend` directory contains documentation for the frontend application, including:

- Component structure
- State management
- Routing
- Styling guidelines
- Testing

## Contributing to Documentation

When contributing to the documentation, please follow these guidelines:

1. Use Markdown for all documentation files
2. Use clear and concise language
3. Include code examples where appropriate
4. Keep the documentation up-to-date with code changes
5. Use proper headings and formatting for readability
