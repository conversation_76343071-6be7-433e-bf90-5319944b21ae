const { sequelize, User, Product, Order } = require('./src/db/models');

async function testDatabaseConnection() {
  console.log('🔍 Testing Database Connection and Models...\n');

  try {
    // Test database connection
    console.log('1. Testing database connection...');
    await sequelize.authenticate();
    console.log('✅ Database connection successful!\n');

    // Test User model
    console.log('2. Testing User model...');
    const userCount = await User.count();
    console.log(`✅ User count: ${userCount}\n`);

    // Test Product model
    console.log('3. Testing Product model...');
    const productCount = await Product.count();
    console.log(`✅ Product count: ${productCount}\n`);

    // Test Order model
    console.log('4. Testing Order model...');
    try {
      const orderCount = await Order.count();
      console.log(`✅ Order count: ${orderCount}\n`);
    } catch (orderError) {
      console.log(`❌ Order model error: ${orderError.message}`);
      console.log('💡 This might be why the admin dashboard is failing.\n');
    }

    // Test Order with User association
    console.log('5. Testing Order-User association...');
    try {
      const recentOrders = await Order.findAll({
        limit: 5,
        order: [['createdAt', 'DESC']],
        include: [
          {
            model: User,
            attributes: ['id', 'name', 'email'],
            required: false
          }
        ]
      });
      console.log(`✅ Recent orders with users: ${recentOrders.length} found\n`);
    } catch (associationError) {
      console.log(`❌ Order-User association error: ${associationError.message}`);
      console.log('💡 This is likely the cause of the 500 error.\n');
    }

    console.log('🎉 Database test completed!');

  } catch (error) {
    console.log(`❌ Database connection failed: ${error.message}`);
  } finally {
    await sequelize.close();
  }
}

testDatabaseConnection().catch(console.error);
