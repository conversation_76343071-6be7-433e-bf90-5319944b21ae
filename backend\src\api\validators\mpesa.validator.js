const { body, param, query, validationResult } = require('express-validator');
const { ValidationError } = require('../../utils/errors');

/**
 * M-Pesa Validation Middleware
 * Validates M-Pesa payment requests and parameters
 */

/**
 * Validate M-Pesa payment initiation request
 */
const validateMpesaPayment = [
  body('orderId')
    .notEmpty()
    .withMessage('Order ID is required')
    .isUUID()
    .withMessage('Order ID must be a valid UUID'),
    
  body('phoneNumber')
    .notEmpty()
    .withMessage('Phone number is required')
    .matches(/^(254|0)[17]\d{8}$/)
    .withMessage('Phone number must be in format ********** or ************'),
    
  body('amount')
    .notEmpty()
    .withMessage('Amount is required')
    .isFloat({ min: 1, max: 70000 })
    .withMessage('Amount must be between KES 1 and KES 70,000')
    .custom((value) => {
      // Ensure amount has at most 2 decimal places
      if (!/^\d+(\.\d{1,2})?$/.test(value.toString())) {
        throw new Error('Amount must have at most 2 decimal places');
      }
      return true;
    }),
    
  body('accountReference')
    .optional()
    .isLength({ min: 1, max: 20 })
    .withMessage('Account reference must be between 1 and 20 characters')
    .matches(/^[a-zA-Z0-9\-_]+$/)
    .withMessage('Account reference can only contain letters, numbers, hyphens, and underscores'),
    
  body('transactionDesc')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Transaction description must be between 1 and 100 characters'),

  // Validation result handler
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors.array().map(error => error.msg);
      throw new ValidationError(errorMessages.join(', '));
    }
    next();
  }
];

/**
 * Validate M-Pesa refund request
 */
const validateMpesaRefund = [
  body('orderId')
    .notEmpty()
    .withMessage('Order ID is required')
    .isUUID()
    .withMessage('Order ID must be a valid UUID'),
    
  body('amount')
    .notEmpty()
    .withMessage('Amount is required')
    .isFloat({ min: 1, max: 70000 })
    .withMessage('Amount must be between KES 1 and KES 70,000'),
    
  body('phoneNumber')
    .notEmpty()
    .withMessage('Phone number is required')
    .matches(/^(254|0)[17]\d{8}$/)
    .withMessage('Phone number must be in format ********** or ************'),
    
  body('reason')
    .optional()
    .isLength({ min: 1, max: 200 })
    .withMessage('Reason must be between 1 and 200 characters'),

  // Validation result handler
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors.array().map(error => error.msg);
      throw new ValidationError(errorMessages.join(', '));
    }
    next();
  }
];

/**
 * Validate checkout request ID parameter
 */
const validateCheckoutRequestId = [
  param('checkoutRequestId')
    .notEmpty()
    .withMessage('Checkout request ID is required')
    .isLength({ min: 10, max: 50 })
    .withMessage('Invalid checkout request ID format'),

  // Validation result handler
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors.array().map(error => error.msg);
      throw new ValidationError(errorMessages.join(', '));
    }
    next();
  }
];

/**
 * Validate order ID parameter
 */
const validateOrderId = [
  param('orderId')
    .notEmpty()
    .withMessage('Order ID is required')
    .isUUID()
    .withMessage('Order ID must be a valid UUID'),

  // Validation result handler
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors.array().map(error => error.msg);
      throw new ValidationError(errorMessages.join(', '));
    }
    next();
  }
];

/**
 * Validate transaction query parameters
 */
const validateTransactionQuery = [
  query('status')
    .optional()
    .isIn(['PENDING', 'SUCCESS', 'FAILED', 'CANCELLED', 'TIMEOUT'])
    .withMessage('Invalid status value'),
    
  query('type')
    .optional()
    .isIn(['STK_PUSH', 'C2B', 'B2C_REFUND', 'B2B', 'REVERSAL'])
    .withMessage('Invalid transaction type'),
    
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
    
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date')
    .custom((endDate, { req }) => {
      if (req.query.startDate && new Date(endDate) <= new Date(req.query.startDate)) {
        throw new Error('End date must be after start date');
      }
      return true;
    }),
    
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
    
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  // Validation result handler
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors.array().map(error => error.msg);
      throw new ValidationError(errorMessages.join(', '));
    }
    next();
  }
];

/**
 * Validate C2B simulation request (sandbox only)
 */
const validateC2BSimulation = [
  body('phoneNumber')
    .notEmpty()
    .withMessage('Phone number is required')
    .matches(/^(254|0)[17]\d{8}$/)
    .withMessage('Phone number must be in format ********** or ************'),
    
  body('amount')
    .notEmpty()
    .withMessage('Amount is required')
    .isFloat({ min: 1, max: 70000 })
    .withMessage('Amount must be between KES 1 and KES 70,000'),
    
  body('billRefNumber')
    .optional()
    .isLength({ min: 1, max: 20 })
    .withMessage('Bill reference number must be between 1 and 20 characters'),

  // Validation result handler
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors.array().map(error => error.msg);
      throw new ValidationError(errorMessages.join(', '));
    }
    next();
  }
];

/**
 * Validate B2C payment request
 */
const validateB2CPayment = [
  body('phoneNumber')
    .notEmpty()
    .withMessage('Phone number is required')
    .matches(/^(254|0)[17]\d{8}$/)
    .withMessage('Phone number must be in format ********** or ************'),
    
  body('amount')
    .notEmpty()
    .withMessage('Amount is required')
    .isFloat({ min: 1, max: 70000 })
    .withMessage('Amount must be between KES 1 and KES 70,000'),
    
  body('commandId')
    .optional()
    .isIn(['BusinessPayment', 'SalaryPayment', 'PromotionPayment'])
    .withMessage('Invalid command ID'),
    
  body('remarks')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Remarks must be between 1 and 100 characters'),
    
  body('occasion')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Occasion must be between 1 and 100 characters'),

  // Validation result handler
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const errorMessages = errors.array().map(error => error.msg);
      throw new ValidationError(errorMessages.join(', '));
    }
    next();
  }
];

/**
 * Custom validation for M-Pesa callback data
 */
const validateCallbackData = (req, res, next) => {
  try {
    const callbackData = req.body;
    
    // Basic structure validation
    if (!callbackData || typeof callbackData !== 'object') {
      throw new ValidationError('Invalid callback data structure');
    }
    
    // Validate STK Push callback
    if (callbackData.Body && callbackData.Body.stkCallback) {
      const stkCallback = callbackData.Body.stkCallback;
      
      if (!stkCallback.MerchantRequestID || !stkCallback.CheckoutRequestID) {
        throw new ValidationError('Missing required STK callback fields');
      }
      
      if (typeof stkCallback.ResultCode !== 'number') {
        throw new ValidationError('Invalid result code in STK callback');
      }
    }
    
    // Validate C2B callback
    if (callbackData.TransactionType) {
      if (!callbackData.TransID || !callbackData.TransAmount) {
        throw new ValidationError('Missing required C2B callback fields');
      }
    }
    
    next();
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message || 'Invalid callback data'
    });
  }
};

/**
 * Sanitize phone number
 */
const sanitizePhoneNumber = (req, res, next) => {
  if (req.body.phoneNumber) {
    let phone = req.body.phoneNumber.toString().trim();
    
    // Remove any non-digit characters except +
    phone = phone.replace(/[^\d+]/g, '');
    
    // Handle different formats
    if (phone.startsWith('+254')) {
      phone = phone.substring(1); // Remove +
    } else if (phone.startsWith('0')) {
      phone = `254${phone.substring(1)}`;
    } else if (!phone.startsWith('254')) {
      phone = `254${phone}`;
    }
    
    req.body.phoneNumber = phone;
  }
  
  next();
};

/**
 * Validate environment-specific operations
 */
const validateEnvironment = (allowedEnvironments = ['sandbox', 'production']) => {
  return (req, res, next) => {
    const currentEnv = process.env.MPESA_ENVIRONMENT || 'sandbox';
    
    if (!allowedEnvironments.includes(currentEnv)) {
      return res.status(403).json({
        success: false,
        message: `This operation is not allowed in ${currentEnv} environment`
      });
    }
    
    next();
  };
};

module.exports = {
  validateMpesaPayment,
  validateMpesaRefund,
  validateCheckoutRequestId,
  validateOrderId,
  validateTransactionQuery,
  validateC2BSimulation,
  validateB2CPayment,
  validateCallbackData,
  sanitizePhoneNumber,
  validateEnvironment
};
