/**
 * Jest Configuration for M-Pesa Backend Tests
 * Specialized configuration for M-Pesa test suite
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-01-15
 */

module.exports = {
  // Test environment
  testEnvironment: 'node',

  // Test file patterns
  testMatch: [
    '**/tests/mpesa/**/*.test.js'
  ],

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/../../tests/mpesa/setup.js'],

  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: '<rootDir>/coverage/mpesa',
  coverageReporters: ['text', 'lcov', 'html'],
  collectCoverageFrom: [
    'src/routes/mpesa/**/*.js',
    'src/services/mpesa/**/*.js',
    'src/controllers/mpesa/**/*.js',
    'src/middleware/mpesa/**/*.js',
    'src/db/models/mpesa*.js',
    '!**/node_modules/**',
    '!**/tests/**'
  ],

  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    './src/services/mpesa/': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    }
  },

  // Module paths
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/../../src/$1',
    '^@tests/(.*)$': '<rootDir>/$1'
  },

  // Test timeout
  testTimeout: 30000,

  // Verbose output
  verbose: true,

  // Clear mocks between tests
  clearMocks: true,

  // Restore mocks after each test
  restoreMocks: true,

  // Transform configuration
  transform: {
    '^.+\\.js$': 'babel-jest'
  },

  // Global setup and teardown (optional)
  // globalSetup: '<rootDir>/globalSetup.js',
  // globalTeardown: '<rootDir>/globalTeardown.js'
};
