const express = require('express');
const router = express.Router();
const pagesController = require('../controllers/pages.controller');

// Public routes for static pages
router.get('/about', pagesController.getAboutPage);
router.get('/support', pagesController.getSupportPage);
router.get('/contact', pagesController.getContactPage);
router.get('/privacy', pagesController.getPrivacyPage);
router.get('/terms', pagesController.getTermsPage);
router.get('/faq', pagesController.getFaqPage);

module.exports = router;
