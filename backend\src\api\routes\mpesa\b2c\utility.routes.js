/**
 * M-Pesa B2C Utility Routes
 * Public utility endpoints for B2C operations
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 2024-01-15
 */

const express = require('express');
const router = express.Router();
const B2CService = require('../../../../services/mpesa/b2c/B2CService');
const B2CConfig = require('../../../../services/mpesa/b2c/config/B2CConfig');
const B2CUtils = require('../../../../services/mpesa/b2c/utils/B2CUtils');

/**
 * @route   GET /api/mpesa/b2c/utility/health
 * @desc    Health check for B2C service
 * @access  Public
 */
router.get('/health', (req, res) => {
  try {
    const healthStatus = B2CService.getHealthStatus();
    
    res.json({
      success: true,
      ...healthStatus,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: '2.0.0'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Health check failed',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/mpesa/b2c/utility/transaction-types
 * @desc    Get available B2C transaction types
 * @access  Public
 */
router.get('/transaction-types', (req, res) => {
  try {
    const config = new B2CConfig();
    
    const transactionTypes = Object.entries(config.TRANSACTION_TYPES).map(([key, value]) => ({
      code: value,
      name: key.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
      description: config.getTransactionTypeDescription(value),
      commandId: config.getCommandId(value)
    }));

    res.json({
      success: true,
      data: {
        transactionTypes,
        commandIds: config.COMMAND_IDS,
        limits: config.LIMITS
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve transaction types',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/mpesa/b2c/utility/status-types
 * @desc    Get available transaction status types
 * @access  Public
 */
router.get('/status-types', (req, res) => {
  try {
    const config = new B2CConfig();
    
    const statusTypes = Object.entries(config.TRANSACTION_STATUS).map(([key, value]) => ({
      code: value,
      name: key.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
      description: config.getStatusDescription(value)
    }));

    res.json({
      success: true,
      data: {
        statusTypes
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve status types',
      error: error.message
    });
  }
});

/**
 * @route   POST /api/mpesa/b2c/utility/validate-phone
 * @desc    Validate Kenyan phone number format
 * @access  Public
 * @body    { phoneNumber }
 */
router.post('/validate-phone', (req, res) => {
  try {
    const { phoneNumber } = req.body;
    
    if (!phoneNumber) {
      return res.status(400).json({
        success: false,
        message: 'Phone number is required'
      });
    }

    const isValid = B2CUtils.isValidKenyanPhone(phoneNumber);
    const formatted = isValid ? B2CUtils.formatPhoneNumber(phoneNumber) : null;
    const masked = formatted ? B2CUtils.maskPhoneNumber(formatted) : null;

    res.json({
      success: true,
      data: {
        original: phoneNumber,
        isValid,
        formatted,
        masked,
        message: isValid ? 'Valid Kenyan phone number' : 'Invalid phone number format'
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Phone validation failed',
      error: error.message
    });
  }
});

/**
 * @route   POST /api/mpesa/b2c/utility/validate-amount
 * @desc    Validate transaction amount
 * @access  Public
 * @body    { amount, transactionType? }
 */
router.post('/validate-amount', (req, res) => {
  try {
    const { amount, transactionType } = req.body;
    
    if (!amount) {
      return res.status(400).json({
        success: false,
        message: 'Amount is required'
      });
    }

    const config = new B2CConfig();
    const validation = config.validateAmount(amount, transactionType);
    const estimatedFee = B2CUtils.calculateTransactionFee(amount);
    const formattedAmount = B2CUtils.formatCurrency(amount);

    res.json({
      success: true,
      data: {
        original: amount,
        isValid: validation.isValid,
        errors: validation.errors,
        formatted: formattedAmount,
        estimatedFee: B2CUtils.formatCurrency(estimatedFee),
        limits: {
          minimum: config.LIMITS.MIN_AMOUNT,
          maximum: config.LIMITS.MAX_AMOUNT,
          daily: config.LIMITS.MAX_DAILY_AMOUNT
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Amount validation failed',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/mpesa/b2c/utility/fee-calculator/:amount
 * @desc    Calculate estimated transaction fee
 * @access  Public
 */
router.get('/fee-calculator/:amount', (req, res) => {
  try {
    const { amount } = req.params;
    const numericAmount = parseFloat(amount);
    
    if (isNaN(numericAmount) || numericAmount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid amount provided'
      });
    }

    const fee = B2CUtils.calculateTransactionFee(numericAmount);
    const total = numericAmount + fee;

    res.json({
      success: true,
      data: {
        amount: B2CUtils.formatCurrency(numericAmount),
        fee: B2CUtils.formatCurrency(fee),
        total: B2CUtils.formatCurrency(total),
        feePercentage: ((fee / numericAmount) * 100).toFixed(2) + '%'
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Fee calculation failed',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/mpesa/b2c/utility/business-hours
 * @desc    Check if current time is within business hours
 * @access  Public
 */
router.get('/business-hours', (req, res) => {
  try {
    const now = new Date();
    const isBusinessHours = B2CUtils.isWithinBusinessHours(now);
    const nextBusinessDay = B2CUtils.getNextBusinessDay(now);

    res.json({
      success: true,
      data: {
        currentTime: now.toISOString(),
        isBusinessHours,
        businessHours: '6:00 AM - 10:00 PM daily',
        nextBusinessDay: nextBusinessDay.toISOString(),
        timezone: 'EAT (UTC+3)'
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Business hours check failed',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/mpesa/b2c/utility/test-data
 * @desc    Get test data for sandbox environment
 * @access  Public
 */
router.get('/test-data', (req, res) => {
  try {
    const config = new B2CConfig();
    const testData = config.getTestData();

    if (config.environment === 'production') {
      return res.status(403).json({
        success: false,
        message: 'Test data not available in production environment'
      });
    }

    res.json({
      success: true,
      data: {
        environment: config.environment,
        ...testData,
        sampleRequests: {
          refund: {
            orderId: 'sample-order-uuid',
            phoneNumber: testData.testPhoneNumbers[0],
            amount: 1000,
            reason: 'Product defect'
          },
          salary: {
            employeePhone: testData.testPhoneNumbers[1],
            amount: 50000,
            employeeId: 'EMP001',
            payrollPeriod: '2024-01'
          },
          promotion: {
            customerPhone: testData.testPhoneNumbers[2],
            amount: 500,
            promotionCode: 'WELCOME2024',
            campaignId: 'CAMP001'
          }
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve test data',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/mpesa/b2c/utility/config-summary
 * @desc    Get configuration summary (safe for public)
 * @access  Public
 */
router.get('/config-summary', (req, res) => {
  try {
    const config = new B2CConfig();
    const summary = config.getSummary();
    
    // Remove sensitive information
    delete summary.hasCredentials;
    delete summary.hasCallbackUrls;

    res.json({
      success: true,
      data: {
        ...summary,
        note: 'Sensitive configuration details are hidden for security'
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve configuration summary',
      error: error.message
    });
  }
});

/**
 * @route   POST /api/mpesa/b2c/utility/generate-reference
 * @desc    Generate transaction reference
 * @access  Public
 * @body    { type, orderId? }
 */
router.post('/generate-reference', (req, res) => {
  try {
    const { type, orderId } = req.body;
    
    if (!type) {
      return res.status(400).json({
        success: false,
        message: 'Transaction type is required'
      });
    }

    const reference = B2CUtils.generateTransactionReference(type, orderId);
    const conversationId = B2CUtils.generateConversationId('G20-B2C', type);

    res.json({
      success: true,
      data: {
        reference,
        conversationId,
        type,
        orderId: orderId || null,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Reference generation failed',
      error: error.message
    });
  }
});

module.exports = router;
