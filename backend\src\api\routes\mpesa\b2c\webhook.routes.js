/**
 * M-Pesa B2C Webhook Routes
 * Webhook endpoints for M-Pesa callbacks
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 2024-01-15
 */

const express = require('express');
const router = express.Router();
const b2cController = require('../../../controllers/mpesa/b2c.controller');
const logger = require('../../../../utils/logger');

// Rate limiting for webhook endpoints
const rateLimit = require('express-rate-limit');

const webhookRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // Allow more callbacks from Safaricom
  skipSuccessfulRequests: true,
  message: {
    success: false,
    message: 'Too many webhook requests, please try again later.',
    error: 'Rate limit exceeded'
  }
});

router.use(webhookRateLimit);

/**
 * @route   POST /api/mpesa/b2c/webhook/result/:transactionId
 * @desc    Handle B2C result callback from Safaricom
 * @access  Public (Safaricom webhook)
 */
router.post('/result/:transactionId', async (req, res) => {
  try {
    const { transactionId } = req.params;
    const callbackData = req.body;

    logger.info('📞 B2C Result webhook received:', {
      transactionId,
      resultCode: callbackData?.Result?.ResultCode,
      timestamp: new Date().toISOString()
    });

    // Process the callback using the controller
    await b2cController.handleResultCallback(req, res);

  } catch (error) {
    logger.error('❌ B2C result webhook error:', {
      transactionId: req.params.transactionId,
      error: error.message,
      stack: error.stack
    });

    res.status(500).json({
      ResultCode: 1,
      ResultDesc: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/mpesa/b2c/webhook/timeout/:transactionId
 * @desc    Handle B2C timeout callback from Safaricom
 * @access  Public (Safaricom webhook)
 */
router.post('/timeout/:transactionId', async (req, res) => {
  try {
    const { transactionId } = req.params;
    const callbackData = req.body;

    logger.info('📞 B2C Timeout webhook received:', {
      transactionId,
      timestamp: new Date().toISOString()
    });

    // Process the timeout callback using the controller
    await b2cController.handleTimeoutCallback(req, res);

  } catch (error) {
    logger.error('❌ B2C timeout webhook error:', {
      transactionId: req.params.transactionId,
      error: error.message,
      stack: error.stack
    });

    res.status(500).json({
      ResultCode: 1,
      ResultDesc: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/mpesa/b2c/webhook/balance-result
 * @desc    Handle account balance query result callback
 * @access  Public (Safaricom webhook)
 */
router.post('/balance-result', async (req, res) => {
  try {
    const callbackData = req.body;

    logger.info('📞 B2C Balance query result received:', {
      resultCode: callbackData?.Result?.ResultCode,
      timestamp: new Date().toISOString()
    });

    // Process balance query result
    const result = callbackData.Result;
    
    if (result.ResultCode === 0) {
      // Success - extract balance information
      const balanceInfo = {};
      
      if (result.ResultParameters && result.ResultParameters.ResultParameter) {
        result.ResultParameters.ResultParameter.forEach(param => {
          switch (param.Key) {
            case 'AccountBalance':
              balanceInfo.accountBalance = param.Value;
              break;
            case 'BOCompletedTime':
              balanceInfo.completedTime = param.Value;
              break;
            default:
              balanceInfo[param.Key] = param.Value;
          }
        });
      }

      logger.info('✅ Account balance query completed:', balanceInfo);
    } else {
      logger.error('❌ Account balance query failed:', {
        resultCode: result.ResultCode,
        resultDesc: result.ResultDesc
      });
    }

    res.status(200).json({
      ResultCode: 0,
      ResultDesc: 'Balance result processed successfully'
    });

  } catch (error) {
    logger.error('❌ Balance result webhook error:', error);
    res.status(500).json({
      ResultCode: 1,
      ResultDesc: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/mpesa/b2c/webhook/status-result
 * @desc    Handle transaction status query result callback
 * @access  Public (Safaricom webhook)
 */
router.post('/status-result', async (req, res) => {
  try {
    const callbackData = req.body;

    logger.info('📞 B2C Status query result received:', {
      resultCode: callbackData?.Result?.ResultCode,
      timestamp: new Date().toISOString()
    });

    // Process status query result
    const result = callbackData.Result;
    
    if (result.ResultCode === 0) {
      // Success - extract transaction status information
      const statusInfo = {};
      
      if (result.ResultParameters && result.ResultParameters.ResultParameter) {
        result.ResultParameters.ResultParameter.forEach(param => {
          switch (param.Key) {
            case 'ReceiptNo':
              statusInfo.receiptNumber = param.Value;
              break;
            case 'ConversationID':
              statusInfo.conversationId = param.Value;
              break;
            case 'FinalisedTime':
              statusInfo.finalisedTime = param.Value;
              break;
            case 'Amount':
              statusInfo.amount = parseFloat(param.Value);
              break;
            case 'TransactionStatus':
              statusInfo.transactionStatus = param.Value;
              break;
            default:
              statusInfo[param.Key] = param.Value;
          }
        });
      }

      logger.info('✅ Transaction status query completed:', statusInfo);
    } else {
      logger.error('❌ Transaction status query failed:', {
        resultCode: result.ResultCode,
        resultDesc: result.ResultDesc
      });
    }

    res.status(200).json({
      ResultCode: 0,
      ResultDesc: 'Status result processed successfully'
    });

  } catch (error) {
    logger.error('❌ Status result webhook error:', error);
    res.status(500).json({
      ResultCode: 1,
      ResultDesc: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/mpesa/b2c/webhook/reversal-result
 * @desc    Handle transaction reversal result callback
 * @access  Public (Safaricom webhook)
 */
router.post('/reversal-result', async (req, res) => {
  try {
    const callbackData = req.body;

    logger.info('📞 B2C Reversal result received:', {
      resultCode: callbackData?.Result?.ResultCode,
      timestamp: new Date().toISOString()
    });

    // Process reversal result
    const result = callbackData.Result;
    
    if (result.ResultCode === 0) {
      // Success - extract reversal information
      const reversalInfo = {};
      
      if (result.ResultParameters && result.ResultParameters.ResultParameter) {
        result.ResultParameters.ResultParameter.forEach(param => {
          switch (param.Key) {
            case 'DebitAccountBalance':
              reversalInfo.debitAccountBalance = param.Value;
              break;
            case 'Amount':
              reversalInfo.amount = parseFloat(param.Value);
              break;
            case 'TransactionReason':
              reversalInfo.transactionReason = param.Value;
              break;
            case 'DebitPartyAffectedAccountBalance':
              reversalInfo.affectedAccountBalance = param.Value;
              break;
            case 'TransactionID':
              reversalInfo.transactionId = param.Value;
              break;
            case 'ConversationID':
              reversalInfo.conversationId = param.Value;
              break;
            default:
              reversalInfo[param.Key] = param.Value;
          }
        });
      }

      logger.info('✅ Transaction reversal completed:', reversalInfo);
    } else {
      logger.error('❌ Transaction reversal failed:', {
        resultCode: result.ResultCode,
        resultDesc: result.ResultDesc
      });
    }

    res.status(200).json({
      ResultCode: 0,
      ResultDesc: 'Reversal result processed successfully'
    });

  } catch (error) {
    logger.error('❌ Reversal result webhook error:', error);
    res.status(500).json({
      ResultCode: 1,
      ResultDesc: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/mpesa/b2c/webhook/health
 * @desc    Webhook health check
 * @access  Public
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'B2C webhook endpoints are healthy',
    timestamp: new Date().toISOString(),
    endpoints: {
      result: '/webhook/result/:transactionId',
      timeout: '/webhook/timeout/:transactionId',
      balanceResult: '/webhook/balance-result',
      statusResult: '/webhook/status-result',
      reversalResult: '/webhook/reversal-result'
    }
  });
});

module.exports = router;
