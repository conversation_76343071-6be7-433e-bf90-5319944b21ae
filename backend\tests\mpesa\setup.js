/**
 * M-Pesa Backend Test Setup
 * Setup configuration for M-Pesa backend tests
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-01-15
 */

const { sequelize } = require('../../src/config/database');

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.MPESA_ENVIRONMENT = 'sandbox';
process.env.MPESA_CONSUMER_KEY = 'test_consumer_key';
process.env.MPESA_CONSUMER_SECRET = 'test_consumer_secret';
process.env.MPESA_SHORTCODE = '174379';
process.env.MPESA_PASSKEY = 'test_passkey';
process.env.MPESA_INITIATOR_NAME = 'testapi';
process.env.MPESA_SECURITY_CREDENTIAL = 'test_credential';

// Global test setup
beforeAll(async () => {
  // Ensure database connection
  try {
    await sequelize.authenticate();
    console.log('✅ Database connection established for M-Pesa tests');
  } catch (error) {
    console.error('❌ Unable to connect to database:', error);
    throw error;
  }
});

// Global test teardown
afterAll(async () => {
  // Close database connection
  try {
    await sequelize.close();
    console.log('✅ Database connection closed for M-Pesa tests');
  } catch (error) {
    console.error('❌ Error closing database connection:', error);
  }
});

// Mock external services
jest.mock('axios', () => ({
  get: jest.fn(),
  post: jest.fn(),
  create: jest.fn(() => ({
    get: jest.fn(),
    post: jest.fn()
  }))
}));

// Mock M-Pesa API responses
const mockMpesaResponses = {
  token: {
    access_token: 'mock_access_token_123456789',
    expires_in: '3599'
  },
  b2cSuccess: {
    ConversationID: 'AG_20240115_123456789',
    OriginatorConversationID: 'ORG_20240115_123456789',
    ResponseCode: '0',
    ResponseDescription: 'Accept the service request successfully.'
  },
  b2cError: {
    ResponseCode: '1',
    ResponseDescription: 'Invalid request'
  }
};

// Global mock functions
global.mockMpesaResponses = mockMpesaResponses;

// Console override for cleaner test output
const originalConsoleError = console.error;
console.error = (...args) => {
  // Suppress specific error messages during tests
  const message = args[0];
  if (typeof message === 'string' && (
    message.includes('Warning: ReactDOM.render is deprecated') ||
    message.includes('Warning: componentWillReceiveProps has been renamed')
  )) {
    return;
  }
  originalConsoleError.apply(console, args);
};

// Test utilities
global.testUtils = {
  // Generate test phone number
  generateTestPhone: () => `254708${Math.floor(Math.random() * 1000000).toString().padStart(6, '0')}`,
  
  // Generate test order ID
  generateTestOrderId: () => `ORD_TEST_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
  
  // Generate test conversation ID
  generateTestConversationId: () => `AG_TEST_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
  
  // Wait for async operations
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Create test user data
  createTestUser: (overrides = {}) => ({
    email: `test${Date.now()}@example.com`,
    password: 'password123',
    firstName: 'Test',
    lastName: 'User',
    phoneNumber: global.testUtils.generateTestPhone(),
    role: 'customer',
    ...overrides
  }),
  
  // Create test transaction data
  createTestTransaction: (overrides = {}) => ({
    conversationId: global.testUtils.generateTestConversationId(),
    originatorConversationId: `ORG_TEST_${Date.now()}`,
    amount: '1000.00',
    type: 'GENERAL',
    typeDescription: 'General Payment',
    status: 'SUBMITTED',
    statusDescription: 'Request submitted successfully',
    remarks: 'Test transaction',
    phoneNumber: global.testUtils.generateTestPhone(),
    userId: 'test_user_123',
    initiatedBy: 'test_admin_456',
    ...overrides
  })
};

// Custom matchers
expect.extend({
  toBeValidPhoneNumber(received) {
    const phoneRegex = /^254\d{9}$/;
    const pass = phoneRegex.test(received);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid Kenyan phone number`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid Kenyan phone number (254XXXXXXXXX)`,
        pass: false
      };
    }
  },
  
  toBeValidAmount(received) {
    const amount = parseFloat(received);
    const pass = !isNaN(amount) && amount > 0 && amount <= 250000;
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid transaction amount`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid transaction amount (1-250000)`,
        pass: false
      };
    }
  },
  
  toBeValidTransactionStatus(received) {
    const validStatuses = ['SUBMITTED', 'COMPLETED', 'FAILED', 'TIMEOUT', 'CANCELLED', 'PENDING'];
    const pass = validStatuses.includes(received);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid transaction status`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid transaction status (${validStatuses.join(', ')})`,
        pass: false
      };
    }
  }
});

console.log('🧪 M-Pesa Backend Test Setup Complete');
