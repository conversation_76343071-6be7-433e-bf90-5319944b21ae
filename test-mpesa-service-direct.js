/**
 * Test M-Pesa service directly to see the exact error
 * Run this with: node test-mpesa-service-direct.js
 */

require('dotenv').config();
const mpesaService = require('./src/services/mpesa.service');

async function testMpesaService() {
  try {
    console.log('🧪 Testing M-Pesa service directly...');
    console.log('');
    
    // Test parameters (same as your checkout)
    const phoneNumber = '************';
    const amount = 75;
    const accountReference = 'G20SHOP-TEST';
    const transactionDesc = 'Test payment';
    const orderId = 'test-order-' + Date.now();
    
    console.log('📋 Test Parameters:');
    console.log('Phone:', phoneNumber);
    console.log('Amount:', amount);
    console.log('Account Reference:', accountReference);
    console.log('Transaction Description:', transactionDesc);
    console.log('Order ID:', orderId);
    console.log('');
    
    // Call the M-Pesa service
    console.log('🚀 Calling M-Pesa service...');
    const result = await mpesaService.initiateSTKPush(
      phoneNumber,
      amount,
      accountReference,
      transactionDesc,
      orderId
    );
    
    console.log('📥 M-Pesa Service Result:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('✅ M-Pesa service call successful!');
    } else {
      console.log('❌ M-Pesa service call failed!');
      console.log('Error details:', result.error);
      console.log('Message:', result.message);
      if (result.details) {
        console.log('Additional details:', result.details);
      }
    }
    
  } catch (error) {
    console.log('❌ Test failed with exception:');
    console.log('Error:', error.message);
    console.log('Stack:', error.stack);
  }
}

// Run the test
testMpesaService().catch(console.error);
