/**
 * M-Pesa B2C Validation Service
 * Comprehensive validation for B2C operations
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-01-15
 */

const B2CUtils = require('../utils/B2CUtils');
const B2CConfig = require('../config/B2CConfig');
const logger = require('../../../../utils/logger');

class B2CValidator {
  
  /**
   * Validate payment data before processing
   * @param {Object} paymentData - Payment data to validate
   * @returns {Object} Validation result
   */
  static validatePaymentData(paymentData) {
    const errors = [];
    const {
      phoneNumber,
      amount,
      transactionType,
      commandId,
      remarks,
      occasion
    } = paymentData;

    // Phone number validation
    if (!phoneNumber) {
      errors.push('Phone number is required');
    } else if (!B2CUtils.isValidKenyanPhone(phoneNumber)) {
      errors.push('Invalid Kenyan phone number format. Use 254XXXXXXXXX format');
    }

    // Amount validation
    if (!amount) {
      errors.push('Amount is required');
    } else {
      const amountValidation = B2CUtils.validateAmount(amount);
      if (!amountValidation.isValid) {
        errors.push(...amountValidation.errors);
      }
    }

    // Transaction type validation
    const config = new B2CConfig();
    const validTypes = Object.values(config.TRANSACTION_TYPES);
    if (transactionType && !validTypes.includes(transactionType)) {
      errors.push(`Invalid transaction type. Must be one of: ${validTypes.join(', ')}`);
    }

    // Command ID validation
    const validCommandIds = Object.values(config.COMMAND_IDS);
    if (commandId && !validCommandIds.includes(commandId)) {
      errors.push(`Invalid command ID. Must be one of: ${validCommandIds.join(', ')}`);
    }

    // Remarks validation
    if (remarks && (typeof remarks !== 'string' || remarks.length > 100)) {
      errors.push('Remarks must be a string with maximum 100 characters');
    }

    // Occasion validation
    if (occasion && (typeof occasion !== 'string' || occasion.length > 100)) {
      errors.push('Occasion must be a string with maximum 100 characters');
    }

    // Business rules validation
    if (phoneNumber && amount && transactionType) {
      const businessRulesValidation = B2CUtils.validateBusinessRules({
        phoneNumber,
        amount,
        transactionType
      });
      
      if (!businessRulesValidation.isValid) {
        errors.push(...businessRulesValidation.errors);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate refund request
   * @param {Object} refundData - Refund data to validate
   * @returns {Object} Validation result
   */
  static validateRefundData(refundData) {
    const errors = [];
    const {
      orderId,
      phoneNumber,
      amount,
      reason
    } = refundData;

    // Order ID validation
    if (!orderId) {
      errors.push('Order ID is required for refunds');
    } else if (!this.isValidUUID(orderId)) {
      errors.push('Invalid order ID format');
    }

    // Phone number validation
    if (!phoneNumber) {
      errors.push('Phone number is required');
    } else if (!B2CUtils.isValidKenyanPhone(phoneNumber)) {
      errors.push('Invalid Kenyan phone number format');
    }

    // Amount validation
    if (!amount) {
      errors.push('Refund amount is required');
    } else {
      const amountValidation = B2CUtils.validateAmount(amount);
      if (!amountValidation.isValid) {
        errors.push(...amountValidation.errors);
      }
    }

    // Reason validation
    if (reason && (typeof reason !== 'string' || reason.length > 200)) {
      errors.push('Refund reason must be a string with maximum 200 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate salary payment request
   * @param {Object} salaryData - Salary payment data to validate
   * @returns {Object} Validation result
   */
  static validateSalaryData(salaryData) {
    const errors = [];
    const {
      employeePhone,
      amount,
      employeeId,
      payrollPeriod
    } = salaryData;

    // Employee phone validation
    if (!employeePhone) {
      errors.push('Employee phone number is required');
    } else if (!B2CUtils.isValidKenyanPhone(employeePhone)) {
      errors.push('Invalid employee phone number format');
    }

    // Amount validation with salary-specific limits
    if (!amount) {
      errors.push('Salary amount is required');
    } else {
      const amountValidation = B2CUtils.validateAmount(amount, 1, 100000);
      if (!amountValidation.isValid) {
        errors.push(...amountValidation.errors);
      }
    }

    // Employee ID validation
    if (!employeeId) {
      errors.push('Employee ID is required');
    } else if (typeof employeeId !== 'string' || employeeId.length > 50) {
      errors.push('Employee ID must be a string with maximum 50 characters');
    }

    // Payroll period validation
    if (!payrollPeriod) {
      errors.push('Payroll period is required');
    } else if (typeof payrollPeriod !== 'string' || payrollPeriod.length > 50) {
      errors.push('Payroll period must be a string with maximum 50 characters');
    }

    // Business hours check for salary payments
    if (!B2CUtils.isWithinBusinessHours()) {
      errors.push('Salary payments can only be processed during business hours (6 AM - 10 PM)');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate promotional payment request
   * @param {Object} promotionData - Promotional payment data to validate
   * @returns {Object} Validation result
   */
  static validatePromotionData(promotionData) {
    const errors = [];
    const {
      customerPhone,
      amount,
      promotionCode,
      campaignId
    } = promotionData;

    // Customer phone validation
    if (!customerPhone) {
      errors.push('Customer phone number is required');
    } else if (!B2CUtils.isValidKenyanPhone(customerPhone)) {
      errors.push('Invalid customer phone number format');
    }

    // Amount validation with promotion-specific limits
    if (!amount) {
      errors.push('Promotional amount is required');
    } else {
      const amountValidation = B2CUtils.validateAmount(amount, 1, 10000);
      if (!amountValidation.isValid) {
        errors.push(...amountValidation.errors);
      }
    }

    // Promotion code validation
    if (!promotionCode) {
      errors.push('Promotion code is required');
    } else if (typeof promotionCode !== 'string' || promotionCode.length > 50) {
      errors.push('Promotion code must be a string with maximum 50 characters');
    }

    // Campaign ID validation (optional)
    if (campaignId && (typeof campaignId !== 'string' || campaignId.length > 50)) {
      errors.push('Campaign ID must be a string with maximum 50 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate bulk payment request
   * @param {Object} bulkData - Bulk payment data to validate
   * @returns {Object} Validation result
   */
  static validateBulkPaymentData(bulkData) {
    const errors = [];
    const { payments, batchId } = bulkData;

    // Payments array validation
    if (!payments) {
      errors.push('Payments array is required');
    } else if (!Array.isArray(payments)) {
      errors.push('Payments must be an array');
    } else if (payments.length === 0) {
      errors.push('At least one payment is required');
    } else if (payments.length > 100) {
      errors.push('Maximum 100 payments allowed per batch');
    } else {
      // Validate each payment
      payments.forEach((payment, index) => {
        const paymentValidation = this.validatePaymentData(payment);
        if (!paymentValidation.isValid) {
          errors.push(`Payment ${index + 1}: ${paymentValidation.errors.join(', ')}`);
        }
      });

      // Check for duplicate phone numbers in the batch
      const phoneNumbers = payments.map(p => B2CUtils.formatPhoneNumber(p.phoneNumber));
      const duplicates = phoneNumbers.filter((phone, index) => phoneNumbers.indexOf(phone) !== index);
      if (duplicates.length > 0) {
        errors.push(`Duplicate phone numbers found in batch: ${duplicates.join(', ')}`);
      }
    }

    // Batch ID validation (optional)
    if (batchId && (typeof batchId !== 'string' || batchId.length > 50)) {
      errors.push('Batch ID must be a string with maximum 50 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate transaction query parameters
   * @param {Object} queryParams - Query parameters to validate
   * @returns {Object} Validation result
   */
  static validateQueryParams(queryParams) {
    const errors = [];
    const {
      status,
      transactionType,
      phoneNumber,
      startDate,
      endDate,
      page,
      limit
    } = queryParams;

    const config = new B2CConfig();

    // Status validation
    if (status) {
      const validStatuses = Object.values(config.TRANSACTION_STATUS);
      if (!validStatuses.includes(status)) {
        errors.push(`Invalid status. Must be one of: ${validStatuses.join(', ')}`);
      }
    }

    // Transaction type validation
    if (transactionType) {
      const validTypes = Object.values(config.TRANSACTION_TYPES);
      if (!validTypes.includes(transactionType)) {
        errors.push(`Invalid transaction type. Must be one of: ${validTypes.join(', ')}`);
      }
    }

    // Phone number validation
    if (phoneNumber && !B2CUtils.isValidKenyanPhone(phoneNumber)) {
      errors.push('Invalid phone number format');
    }

    // Date validation
    if (startDate && !this.isValidDate(startDate)) {
      errors.push('Invalid start date format. Use ISO 8601 format (YYYY-MM-DD)');
    }

    if (endDate && !this.isValidDate(endDate)) {
      errors.push('Invalid end date format. Use ISO 8601 format (YYYY-MM-DD)');
    }

    if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
      errors.push('Start date cannot be after end date');
    }

    // Pagination validation
    if (page && (!Number.isInteger(Number(page)) || Number(page) < 1)) {
      errors.push('Page must be a positive integer');
    }

    if (limit && (!Number.isInteger(Number(limit)) || Number(limit) < 1 || Number(limit) > 100)) {
      errors.push('Limit must be an integer between 1 and 100');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate callback data from M-Pesa
   * @param {Object} callbackData - Callback data to validate
   * @returns {Object} Validation result
   */
  static validateCallbackData(callbackData) {
    const errors = [];

    if (!callbackData) {
      errors.push('Callback data is required');
      return { isValid: false, errors };
    }

    if (!callbackData.Result) {
      errors.push('Result object is required in callback data');
      return { isValid: false, errors };
    }

    const result = callbackData.Result;

    // Result code validation
    if (typeof result.ResultCode === 'undefined') {
      errors.push('ResultCode is required');
    } else if (!Number.isInteger(result.ResultCode)) {
      errors.push('ResultCode must be an integer');
    }

    // Result description validation
    if (!result.ResultDesc) {
      errors.push('ResultDesc is required');
    } else if (typeof result.ResultDesc !== 'string') {
      errors.push('ResultDesc must be a string');
    }

    // Validate result parameters for successful transactions
    if (result.ResultCode === 0 && result.ResultParameters) {
      if (!Array.isArray(result.ResultParameters.ResultParameter)) {
        errors.push('ResultParameter must be an array for successful transactions');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check if string is a valid UUID
   * @param {string} uuid - String to validate
   * @returns {boolean} True if valid UUID
   */
  static isValidUUID(uuid) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Check if string is a valid date
   * @param {string} dateString - Date string to validate
   * @returns {boolean} True if valid date
   */
  static isValidDate(dateString) {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
  }

  /**
   * Validate environment configuration
   * @param {Object} config - Configuration to validate
   * @returns {Object} Validation result
   */
  static validateEnvironmentConfig(config) {
    const errors = [];
    const requiredFields = [
      'consumerKey',
      'consumerSecret',
      'businessShortCode',
      'initiatorName',
      'securityCredential'
    ];

    requiredFields.forEach(field => {
      if (!config[field]) {
        errors.push(`${field} is required`);
      }
    });

    // Validate URLs for production
    if (config.environment === 'production') {
      if (!config.resultUrl || !config.queueTimeoutUrl) {
        errors.push('Result URL and Queue Timeout URL are required for production');
      }

      if (config.resultUrl && !config.resultUrl.startsWith('https://')) {
        errors.push('Result URL must use HTTPS in production');
      }

      if (config.queueTimeoutUrl && !config.queueTimeoutUrl.startsWith('https://')) {
        errors.push('Queue Timeout URL must use HTTPS in production');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Sanitize and validate user input
   * @param {Object} input - User input to sanitize
   * @returns {Object} Sanitized input
   */
  static sanitizeInput(input) {
    const sanitized = {};

    Object.keys(input).forEach(key => {
      const value = input[key];
      
      if (typeof value === 'string') {
        // Trim whitespace and remove potentially harmful characters
        sanitized[key] = value.trim().replace(/[<>\"']/g, '');
      } else if (typeof value === 'number') {
        // Ensure numbers are finite
        sanitized[key] = Number.isFinite(value) ? value : 0;
      } else if (typeof value === 'boolean') {
        sanitized[key] = Boolean(value);
      } else if (Array.isArray(value)) {
        sanitized[key] = value.map(item => 
          typeof item === 'string' ? item.trim().replace(/[<>\"']/g, '') : item
        );
      } else {
        sanitized[key] = value;
      }
    });

    return sanitized;
  }
}

module.exports = B2CValidator;
