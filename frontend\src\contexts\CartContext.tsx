import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from '@clerk/clerk-react';
import axios from 'axios';

interface CartItem {
  id: string;
  productId: string;
  quantity: number;
  price: string;
  totalPrice: string;
  Product: {
    id: string;
    name: string;
    regularPrice: string;
    salePrice?: string;
    imageUrl?: string;
    slug: string;
    brand?: string;
    model?: string;
  };
}

interface CartContextType {
  items: CartItem[];
  totalItems: number;
  totalAmount: number;
  loading: boolean;
  addToCart: (productId: string, quantity?: number) => Promise<void>;
  removeFromCart: (itemId: string) => Promise<void>;
  updateQuantity: (itemId: string, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  refreshCart: () => Promise<void>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

interface CartProviderProps {
  children: React.ReactNode;
}

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const { isSignedIn, getToken } = useAuth();
  const [items, setItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);

  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
  const totalAmount = items.reduce((sum, item) => sum + parseFloat(item.totalPrice || '0'), 0);

  const refreshCart = useCallback(async () => {
    try {
      setLoading(true);
      console.log('🔄 Refreshing cart from backend...');

      const headers: any = {};

      // Add authorization header if user is signed in
      if (isSignedIn) {
        const token = await getToken();
        headers.Authorization = `Bearer ${token}`;
      }

      // Add session ID header if we have one (for guest users)
      if (!isSignedIn && sessionId) {
        headers['x-session-id'] = sessionId;
      }

      const response = await axios.get('/cart', { headers });

      // Handle new response format: { success: true, data: { items: [...] } }
      const cart = response.data.data || response.data;
      const cartItems = cart.items || [];

      // Store session ID if we're a guest user and don't have one yet
      if (!isSignedIn && !sessionId && cart.sessionId) {
        setSessionId(cart.sessionId);
        localStorage.setItem('cart_session_id', cart.sessionId);
      }

      console.log('🛒 Cart refreshed, items count:', cartItems.length);
      console.log('🛒 Cart data:', cart);
      setItems(cartItems);
    } catch (error) {
      console.error('❌ Error fetching cart:', error);
      setItems([]);
    } finally {
      setLoading(false);
    }
  }, [isSignedIn, sessionId, getToken]);

  const addToCart = useCallback(async (productId: string, quantity: number = 1) => {
    try {
      console.log('🛒 Adding to cart:', { productId, quantity, sessionId });
      const headers: any = {};

      // Add authorization header if user is signed in
      if (isSignedIn) {
        const token = await getToken();
        headers.Authorization = `Bearer ${token}`;
      }

      // Add session ID header if we have one (for guest users)
      if (!isSignedIn && sessionId) {
        headers['x-session-id'] = sessionId;
      }

      const response = await axios.post('/cart/items', {
        productId,
        quantity
      }, { headers });

      console.log('✅ Add to cart response:', response.data);

      // Store session ID if we're a guest user and got one back
      if (!isSignedIn && !sessionId && response.data.sessionId) {
        setSessionId(response.data.sessionId);
        localStorage.setItem('cart_session_id', response.data.sessionId);
      }

      await refreshCart();
    } catch (error) {
      console.error('❌ Error adding to cart:', error);
      throw error;
    }
  }, [isSignedIn, sessionId, getToken, refreshCart]);

  const removeFromCart = useCallback(async (itemId: string) => {
    try {
      const headers: any = {};

      // Add authorization header if user is signed in
      if (isSignedIn) {
        const token = await getToken();
        headers.Authorization = `Bearer ${token}`;
      }

      // Add session ID header if we have one (for guest users)
      if (!isSignedIn && sessionId) {
        headers['x-session-id'] = sessionId;
      }

      await axios.delete(`/cart/items/${itemId}`, { headers });
      await refreshCart();
    } catch (error) {
      console.error('Error removing from cart:', error);
      throw error;
    }
  }, [isSignedIn, sessionId, getToken, refreshCart]);

  const updateQuantity = useCallback(async (itemId: string, quantity: number) => {
    try {
      const headers: any = {};

      // Add authorization header if user is signed in
      if (isSignedIn) {
        const token = await getToken();
        headers.Authorization = `Bearer ${token}`;
      }

      // Add session ID header if we have one (for guest users)
      if (!isSignedIn && sessionId) {
        headers['x-session-id'] = sessionId;
      }

      await axios.put(`/cart/items/${itemId}`, {
        quantity
      }, { headers });
      await refreshCart();
    } catch (error) {
      console.error('Error updating quantity:', error);
      throw error;
    }
  }, [isSignedIn, sessionId, getToken, refreshCart]);

  const clearCart = useCallback(async () => {
    try {
      const headers: any = {};

      // Add authorization header if user is signed in
      if (isSignedIn) {
        const token = await getToken();
        headers.Authorization = `Bearer ${token}`;
      }

      // Add session ID header if we have one (for guest users)
      if (!isSignedIn && sessionId) {
        headers['x-session-id'] = sessionId;
      }

      await axios.delete('/cart', { headers });
      setItems([]);
    } catch (error) {
      console.error('Error clearing cart:', error);
      throw error;
    }
  }, [isSignedIn, sessionId, getToken]);

  // Load session ID from localStorage on mount
  useEffect(() => {
    if (!isSignedIn) {
      const storedSessionId = localStorage.getItem('cart_session_id');
      if (storedSessionId) {
        setSessionId(storedSessionId);
      }
    }
  }, [isSignedIn]);

  useEffect(() => {
    // Load cart for both signed-in users and guests
    refreshCart();
  }, [refreshCart]);

  const value: CartContextType = {
    items,
    totalItems,
    totalAmount,
    loading,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    refreshCart
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};
