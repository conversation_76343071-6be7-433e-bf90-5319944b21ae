const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const API_URL = 'http://localhost:3000/api';
let authToken = null;
let userId = null;
let testProductId = null;
let testCategoryId = null;
let testCartId = null;
let testCartItemId = null;
let testWishlistId = null;
let testOrderId = null;

// Create a directory for test results
const resultsDir = path.join(__dirname, 'test-results');
if (!fs.existsSync(resultsDir)) {
  fs.mkdirSync(resultsDir);
}

// Helper function to log results
const logResult = (testName, result) => {
  const logFile = path.join(resultsDir, `${testName}.json`);
  fs.writeFileSync(logFile, JSON.stringify(result, null, 2));
  console.log(`✅ ${testName} - Result saved to ${logFile}`);
};

// Helper function to log errors
const logError = (testName, error) => {
  const errorFile = path.join(resultsDir, `${testName}-error.json`);
  const errorData = {
    message: error.message,
    response: error.response ? {
      status: error.response.status,
      data: error.response.data
    } : null
  };
  fs.writeFileSync(errorFile, JSON.stringify(errorData, null, 2));
  console.error(`❌ ${testName} - Error saved to ${errorFile}`);
};

// Setup axios instance with auth token
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add auth token to requests when available
api.interceptors.request.use(config => {
  if (authToken) {
    config.headers.Authorization = `Bearer ${authToken}`;
  }
  return config;
});

// Test functions
const tests = {
  // Auth tests
  async registerUser() {
    try {
      const response = await api.post('/auth/register', {
        name: 'Test User',
        email: `test${Date.now()}@example.com`,
        password: 'Test@123',
        phone: '1234567890'
      });
      logResult('register-user', response.data);
      return response.data;
    } catch (error) {
      logError('register-user', error);
      throw error;
    }
  },

  async loginUser(email, password) {
    try {
      const response = await api.post('/auth/login', {
        email,
        password
      });
      authToken = response.data.token;
      userId = response.data.user.id;
      logResult('login-user', response.data);
      return response.data;
    } catch (error) {
      logError('login-user', error);
      throw error;
    }
  },

  async getUserProfile() {
    try {
      const response = await api.get('/auth/profile');
      logResult('get-user-profile', response.data);
      return response.data;
    } catch (error) {
      logError('get-user-profile', error);
      throw error;
    }
  },

  // Product tests
  async getProducts() {
    try {
      const response = await api.get('/products');
      if (response.data.products && response.data.products.length > 0) {
        testProductId = response.data.products[0].id;
      }
      logResult('get-products', response.data);
      return response.data;
    } catch (error) {
      logError('get-products', error);
      throw error;
    }
  },

  async getProductById() {
    if (!testProductId) {
      console.log('⚠️ No product ID available for testing');
      return null;
    }
    try {
      const response = await api.get(`/products/${testProductId}`);
      logResult('get-product-by-id', response.data);
      return response.data;
    } catch (error) {
      logError('get-product-by-id', error);
      throw error;
    }
  },

  async getCategories() {
    try {
      const response = await api.get('/products/categories');
      if (response.data && response.data.length > 0) {
        testCategoryId = response.data[0].id;
      }
      logResult('get-categories', response.data);
      return response.data;
    } catch (error) {
      logError('get-categories', error);
      throw error;
    }
  },

  // Cart tests
  async getCart() {
    try {
      const response = await api.get('/cart');
      testCartId = response.data.id;
      logResult('get-cart', response.data);
      return response.data;
    } catch (error) {
      logError('get-cart', error);
      throw error;
    }
  },

  async addToCart() {
    if (!testProductId) {
      console.log('⚠️ No product ID available for testing');
      return null;
    }
    try {
      const response = await api.post('/cart/items', {
        productId: testProductId,
        quantity: 1
      });
      testCartItemId = response.data.id;
      logResult('add-to-cart', response.data);
      return response.data;
    } catch (error) {
      logError('add-to-cart', error);
      throw error;
    }
  },

  async updateCartItem() {
    if (!testCartItemId) {
      console.log('⚠️ No cart item ID available for testing');
      return null;
    }
    try {
      const response = await api.put(`/cart/items/${testCartItemId}`, {
        quantity: 2
      });
      logResult('update-cart-item', response.data);
      return response.data;
    } catch (error) {
      logError('update-cart-item', error);
      throw error;
    }
  },

  async removeCartItem() {
    if (!testCartItemId) {
      console.log('⚠️ No cart item ID available for testing');
      return null;
    }
    try {
      const response = await api.delete(`/cart/items/${testCartItemId}`);
      logResult('remove-cart-item', response.data);
      return response.data;
    } catch (error) {
      logError('remove-cart-item', error);
      throw error;
    }
  },

  // Wishlist tests
  async getWishlists() {
    try {
      const response = await api.get('/wishlist');
      if (response.data && response.data.length > 0) {
        testWishlistId = response.data[0].id;
      }
      logResult('get-wishlists', response.data);
      return response.data;
    } catch (error) {
      logError('get-wishlists', error);
      throw error;
    }
  },

  async addToWishlist() {
    if (!testProductId || !testWishlistId) {
      console.log('⚠️ No product ID or wishlist ID available for testing');
      return null;
    }
    try {
      const response = await api.post(`/wishlist/${testWishlistId}/items`, {
        productId: testProductId
      });
      logResult('add-to-wishlist', response.data);
      return response.data;
    } catch (error) {
      logError('add-to-wishlist', error);
      throw error;
    }
  },

  // Order tests
  async createOrder() {
    try {
      // First add a product to cart
      await this.addToCart();
      
      // Then create an order from the cart
      const response = await api.post('/orders', {
        shippingAddress: {
          fullName: 'Test User',
          addressLine1: '123 Test St',
          city: 'Test City',
          state: 'Test State',
          postalCode: '12345',
          country: 'Test Country',
          phone: '1234567890'
        },
        paymentMethod: 'credit_card'
      });
      testOrderId = response.data.id;
      logResult('create-order', response.data);
      return response.data;
    } catch (error) {
      logError('create-order', error);
      throw error;
    }
  },

  async getUserOrders() {
    try {
      const response = await api.get('/orders/user');
      logResult('get-user-orders', response.data);
      return response.data;
    } catch (error) {
      logError('get-user-orders', error);
      throw error;
    }
  },

  async getOrderById() {
    if (!testOrderId) {
      console.log('⚠️ No order ID available for testing');
      return null;
    }
    try {
      const response = await api.get(`/orders/${testOrderId}`);
      logResult('get-order-by-id', response.data);
      return response.data;
    } catch (error) {
      logError('get-order-by-id', error);
      throw error;
    }
  }
};

// Run tests
async function runTests() {
  console.log('🚀 Starting API tests...');
  
  try {
    // Register and login
    const registerData = await tests.registerUser();
    await tests.loginUser(registerData.user.email, 'Test@123');
    await tests.getUserProfile();
    
    // Test products and categories
    await tests.getProducts();
    await tests.getProductById();
    await tests.getCategories();
    
    // Test cart functionality
    await tests.getCart();
    await tests.addToCart();
    await tests.updateCartItem();
    await tests.getCart(); // Check updated cart
    
    // Test wishlist functionality
    await tests.getWishlists();
    await tests.addToWishlist();
    
    // Test order functionality
    await tests.createOrder();
    await tests.getUserOrders();
    await tests.getOrderById();
    
    // Clean up
    await tests.removeCartItem();
    
    console.log('✅ All tests completed successfully!');
  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
  }
}

// Run the tests
runTests();
