const express = require('express');
const router = express.Router();
const paymentsController = require('../controllers/payments.controller');
const { authenticate, authorize, optionalAuth } = require('../middlewares/auth.middleware');
const { validatePaymentIntent, validateRefund } = require('../validators/payment.validator');

// Public routes (with optional auth)
router.post(
  '/create-intent',
  optionalAuth,
  validatePaymentIntent,
  paymentsController.createPaymentIntent
);

// Webhook route (no auth)
router.post(
  '/webhook',
  express.raw({ type: 'application/json' }),
  paymentsController.processWebhook
);

// Admin routes
router.post(
  '/refund/:orderId',
  authenticate,
  authorize(['admin']),
  validateRefund,
  paymentsController.processRefund
);

module.exports = router;
