/**
 * M-Pesa B2C Utility Functions
 * Helper functions for B2C operations
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-01-15
 */

const crypto = require('crypto');
const moment = require('moment');
const logger = require('../../../../utils/logger');

class B2CUtils {
  
  /**
   * Generate unique conversation ID
   * @param {string} prefix - Prefix for the ID
   * @param {string} transactionType - Type of transaction
   * @returns {string} Unique conversation ID
   */
  static generateConversationId(prefix = 'G20-B2C', transactionType = 'GENERAL') {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    const typeCode = transactionType.substring(0, 3).toUpperCase();
    return `${prefix}-${typeCode}-${timestamp}-${random}`;
  }

  /**
   * Format phone number to international format
   * @param {string} phoneNumber - Phone number to format
   * @returns {string|null} Formatted phone number or null if invalid
   */
  static formatPhoneNumber(phoneNumber) {
    if (!phoneNumber) return null;
    
    // Remove any spaces, dashes, or special characters
    const cleaned = phoneNumber.replace(/[\s\-\(\)]/g, '');
    
    // Handle different formats
    if (cleaned.startsWith('0')) {
      return `254${cleaned.substring(1)}`;
    } else if (cleaned.startsWith('254')) {
      return cleaned;
    } else if (cleaned.startsWith('+254')) {
      return cleaned.substring(1);
    } else if (cleaned.length === 9) {
      return `254${cleaned}`;
    } else {
      return null; // Invalid format
    }
  }

  /**
   * Validate Kenyan phone number
   * @param {string} phoneNumber - Phone number to validate
   * @returns {boolean} True if valid
   */
  static isValidKenyanPhone(phoneNumber) {
    const formatted = this.formatPhoneNumber(phoneNumber);
    if (!formatted) return false;
    
    // Check if it matches Kenyan mobile number pattern
    return /^254[0-9]{9}$/.test(formatted);
  }

  /**
   * Mask phone number for logging (show only last 4 digits)
   * @param {string} phoneNumber - Phone number to mask
   * @returns {string} Masked phone number
   */
  static maskPhoneNumber(phoneNumber) {
    if (!phoneNumber || phoneNumber.length < 4) return '****';
    return `****${phoneNumber.slice(-4)}`;
  }

  /**
   * Format amount to ensure it's a valid number
   * @param {number|string} amount - Amount to format
   * @returns {number} Formatted amount
   * @throws {Error} If amount is invalid
   */
  static formatAmount(amount) {
    const parsed = parseFloat(amount);
    if (isNaN(parsed) || parsed <= 0) {
      throw new Error('Invalid amount: must be a positive number');
    }
    return Math.round(parsed); // M-Pesa doesn't support decimals
  }

  /**
   * Validate amount is within B2C limits
   * @param {number} amount - Amount to validate
   * @param {number} minAmount - Minimum allowed amount
   * @param {number} maxAmount - Maximum allowed amount
   * @returns {Object} Validation result
   */
  static validateAmount(amount, minAmount = 1, maxAmount = 150000) {
    const errors = [];
    
    try {
      const formatted = this.formatAmount(amount);
      
      if (formatted < minAmount) {
        errors.push(`Amount must be at least KES ${minAmount}`);
      }
      
      if (formatted > maxAmount) {
        errors.push(`Amount cannot exceed KES ${maxAmount}`);
      }
      
      return {
        isValid: errors.length === 0,
        errors,
        formattedAmount: formatted
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [error.message],
        formattedAmount: null
      };
    }
  }

  /**
   * Parse M-Pesa callback result parameters
   * @param {Array} resultParameters - Result parameters from callback
   * @returns {Object} Parsed parameters
   */
  static parseCallbackResult(resultParameters) {
    if (!resultParameters || !Array.isArray(resultParameters)) {
      return {};
    }

    const parsed = {};
    
    resultParameters.forEach(param => {
      if (param.Key && param.Value !== undefined) {
        switch (param.Key) {
          case 'ReceiptNo':
            parsed.receiptNumber = param.Value;
            break;
          case 'TransactionAmount':
            parsed.transactionAmount = parseFloat(param.Value);
            break;
          case 'TransactionCompletedDateTime':
            parsed.transactionDate = new Date(param.Value);
            break;
          case 'B2CChargesPaidAccountAvailableFunds':
            parsed.b2cCharges = parseFloat(param.Value);
            break;
          case 'ReceiverPartyPublicName':
            parsed.receiverName = param.Value;
            break;
          case 'B2CUtilityAccountAvailableFunds':
            parsed.utilityBalance = parseFloat(param.Value);
            break;
          case 'B2CWorkingAccountAvailableFunds':
            parsed.workingBalance = parseFloat(param.Value);
            break;
          case 'B2CRecipientIsRegisteredCustomer':
            parsed.isRegisteredCustomer = param.Value === 'Y';
            break;
          default:
            // Store any additional parameters
            parsed[this.camelCase(param.Key)] = param.Value;
        }
      }
    });

    return parsed;
  }

  /**
   * Convert string to camelCase
   * @param {string} str - String to convert
   * @returns {string} CamelCase string
   */
  static camelCase(str) {
    return str.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
      return index === 0 ? word.toLowerCase() : word.toUpperCase();
    }).replace(/\s+/g, '');
  }

  /**
   * Calculate estimated transaction fees (approximate)
   * @param {number} amount - Transaction amount
   * @returns {number} Estimated fee
   */
  static calculateTransactionFee(amount) {
    // M-Pesa B2C charges (approximate - check current rates)
    if (amount <= 49) return 7;
    if (amount <= 100) return 7;
    if (amount <= 500) return 7;
    if (amount <= 1000) return 7;
    if (amount <= 1500) return 12;
    if (amount <= 2500) return 23;
    if (amount <= 3500) return 28;
    if (amount <= 5000) return 33;
    if (amount <= 7500) return 53;
    if (amount <= 10000) return 58;
    if (amount <= 15000) return 78;
    if (amount <= 20000) return 83;
    if (amount <= 25000) return 88;
    if (amount <= 30000) return 93;
    if (amount <= 35000) return 103;
    if (amount <= 40000) return 108;
    if (amount <= 45000) return 113;
    if (amount <= 50000) return 118;
    if (amount <= 150000) return 118;
    
    return 118; // Maximum fee
  }

  /**
   * Generate transaction reference
   * @param {string} type - Transaction type
   * @param {string} orderId - Order ID (optional)
   * @returns {string} Transaction reference
   */
  static generateTransactionReference(type, orderId = null) {
    const timestamp = moment().format('YYMMDDHHmmss');
    const random = Math.random().toString(36).substring(2, 6).toUpperCase();
    
    if (orderId) {
      const shortOrderId = orderId.substring(0, 8);
      return `${type}-${shortOrderId}-${timestamp}-${random}`;
    }
    
    return `${type}-${timestamp}-${random}`;
  }

  /**
   * Validate callback authenticity (basic check)
   * @param {Object} callbackData - Callback data from M-Pesa
   * @param {string} expectedTransactionId - Expected transaction ID
   * @returns {boolean} True if callback appears valid
   */
  static validateCallback(callbackData, expectedTransactionId = null) {
    try {
      // Basic validation
      if (!callbackData || !callbackData.Result) {
        return false;
      }

      const result = callbackData.Result;
      if (typeof result.ResultCode === 'undefined' || !result.ResultDesc) {
        return false;
      }

      // Additional validation can be added here
      // e.g., signature verification, timestamp checks, etc.

      return true;
    } catch (error) {
      logger.error('❌ Callback validation error:', error);
      return false;
    }
  }

  /**
   * Sanitize callback data for logging
   * @param {Object} callbackData - Callback data to sanitize
   * @returns {Object} Sanitized callback data
   */
  static sanitizeCallbackData(callbackData) {
    try {
      const sanitized = JSON.parse(JSON.stringify(callbackData));
      
      // Remove or mask sensitive information
      if (sanitized.Result && sanitized.Result.ResultParameters) {
        sanitized.Result.ResultParameters = sanitized.Result.ResultParameters.map(param => {
          if (param.Key === 'ReceiverPartyPublicName') {
            param.Value = this.maskPhoneNumber(param.Value);
          }
          return param;
        });
      }
      
      return sanitized;
    } catch (error) {
      return { error: 'Failed to sanitize callback data' };
    }
  }

  /**
   * Format currency for display
   * @param {number} amount - Amount to format
   * @param {string} currency - Currency code
   * @returns {string} Formatted currency string
   */
  static formatCurrency(amount, currency = 'KES') {
    try {
      const formatted = parseFloat(amount).toLocaleString('en-KE', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
      
      return formatted;
    } catch (error) {
      return `${currency} ${amount}`;
    }
  }

  /**
   * Generate batch ID for bulk payments
   * @param {string} prefix - Prefix for batch ID
   * @returns {string} Batch ID
   */
  static generateBatchId(prefix = 'BATCH') {
    const timestamp = moment().format('YYYYMMDD-HHmmss');
    const random = Math.random().toString(36).substring(2, 6).toUpperCase();
    return `${prefix}-${timestamp}-${random}`;
  }

  /**
   * Check if transaction is within business hours
   * @param {Date} date - Date to check
   * @returns {boolean} True if within business hours
   */
  static isWithinBusinessHours(date = new Date()) {
    const hour = date.getHours();
    // M-Pesa operates 24/7, but you might want to restrict business payments
    // Business hours: 6 AM to 10 PM
    return hour >= 6 && hour <= 22;
  }

  /**
   * Get next business day
   * @param {Date} date - Starting date
   * @returns {Date} Next business day
   */
  static getNextBusinessDay(date = new Date()) {
    const nextDay = new Date(date);
    nextDay.setDate(nextDay.getDate() + 1);
    
    // If it's Saturday (6) or Sunday (0), move to Monday
    while (nextDay.getDay() === 0 || nextDay.getDay() === 6) {
      nextDay.setDate(nextDay.getDate() + 1);
    }
    
    return nextDay;
  }

  /**
   * Create retry delay with exponential backoff
   * @param {number} attempt - Current attempt number
   * @param {number} baseDelay - Base delay in milliseconds
   * @param {number} maxDelay - Maximum delay in milliseconds
   * @returns {number} Delay in milliseconds
   */
  static calculateRetryDelay(attempt, baseDelay = 1000, maxDelay = 30000) {
    const delay = baseDelay * Math.pow(2, attempt - 1);
    return Math.min(delay, maxDelay);
  }

  /**
   * Sleep for specified milliseconds
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise} Promise that resolves after delay
   */
  static sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Generate secure random string
   * @param {number} length - Length of random string
   * @returns {string} Random string
   */
  static generateSecureRandom(length = 16) {
    return crypto.randomBytes(length).toString('hex').substring(0, length);
  }

  /**
   * Hash sensitive data for logging
   * @param {string} data - Data to hash
   * @returns {string} Hashed data
   */
  static hashSensitiveData(data) {
    return crypto.createHash('sha256').update(data).digest('hex').substring(0, 8);
  }

  /**
   * Validate business rules for B2C transactions
   * @param {Object} transactionData - Transaction data to validate
   * @returns {Object} Validation result
   */
  static validateBusinessRules(transactionData) {
    const errors = [];
    
    const { amount, transactionType, phoneNumber } = transactionData;
    
    // Transaction type specific rules
    if (transactionType === 'SALARY' && amount > 100000) {
      errors.push('Salary payments cannot exceed KES 100,000');
    }
    
    if (transactionType === 'PROMOTION' && amount > 10000) {
      errors.push('Promotional payments cannot exceed KES 10,000');
    }
    
    // Time-based restrictions
    if (transactionType === 'SALARY' && !this.isWithinBusinessHours()) {
      errors.push('Salary payments can only be processed during business hours (6 AM - 10 PM)');
    }
    
    // Phone number validation
    if (!this.isValidKenyanPhone(phoneNumber)) {
      errors.push('Invalid Kenyan phone number format');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get transaction summary for reporting
   * @param {Object} transaction - Transaction object
   * @returns {Object} Transaction summary
   */
  static getTransactionSummary(transaction) {
    return {
      id: transaction.id,
      conversationId: transaction.conversationId,
      type: transaction.transactionType,
      amount: this.formatCurrency(transaction.amount),
      phone: this.maskPhoneNumber(transaction.phoneNumber),
      status: transaction.status,
      initiatedAt: moment(transaction.initiatedAt).format('YYYY-MM-DD HH:mm:ss'),
      completedAt: transaction.completedAt ? moment(transaction.completedAt).format('YYYY-MM-DD HH:mm:ss') : null,
      duration: transaction.completedAt ? moment(transaction.completedAt).diff(moment(transaction.initiatedAt), 'seconds') : null,
      receiptNumber: transaction.mpesaReceiptNumber || null
    };
  }
}

module.exports = B2CUtils;
