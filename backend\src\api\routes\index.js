const express = require('express');
const router = express.Router();

console.log('🔍 Loading route modules...');

try {
  const authRoutes = require('./auth.routes');
  console.log('✅ Auth routes loaded');

  const userRoutes = require('./users.routes');
  console.log('✅ User routes loaded');

  const productRoutes = require('./products.routes');
  console.log('✅ Product routes loaded');

  const cartRoutes = require('./cart.routes');
  console.log('✅ Cart routes loaded');

  const wishlistRoutes = require('./wishlist.routes');
  console.log('✅ Wishlist routes loaded');

  const orderRoutes = require('./orders.routes');
  console.log('✅ Order routes loaded');

  const paymentRoutes = require('./payments.routes');
  console.log('✅ Payment routes loaded');

  const reviewRoutes = require('./reviews.routes');
  console.log('✅ Review routes loaded');

  const couponRoutes = require('./coupons.routes');
  console.log('✅ Coupon routes loaded');

  const pageRoutes = require('./pages.routes');
  console.log('✅ Page routes loaded');

  const adminRoutes = require('./admin.routes');
  console.log('✅ Admin routes loaded');

  const mpesaRoutes = require('./mpesa.routes');
  console.log('✅ M-Pesa routes loaded');

  // Mount routes
  console.log('🔍 Mounting routes...');
  router.use('/auth', authRoutes);
  router.use('/users', userRoutes);
  router.use('/products', productRoutes);
  router.use('/cart', cartRoutes);
  router.use('/wishlist', wishlistRoutes);
  router.use('/orders', orderRoutes);
  router.use('/payments', paymentRoutes);
  router.use('/reviews', reviewRoutes);
  router.use('/coupons', couponRoutes);
  router.use('/admin', adminRoutes);
  router.use('/mpesa', mpesaRoutes);
  router.use('/', pageRoutes);
  console.log('✅ All routes mounted successfully');

} catch (error) {
  console.error('❌ Error loading routes:', error.message);
  console.error('Stack:', error.stack);
}

module.exports = router;
