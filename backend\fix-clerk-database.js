require('dotenv').config();
const { Sequelize } = require('sequelize');

// Create database connection
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: false // Set to true if you want to see SQL queries
  }
);

async function fixClerkDatabase() {
  console.log('🔧 Starting Clerk Database Fix...\n');
  
  try {
    // Test connection
    console.log('🔍 Testing database connection...');
    await sequelize.authenticate();
    console.log('✅ Database connection successful\n');

    // Check current table structure
    console.log('🔍 Checking current Users table structure...');
    const [columns] = await sequelize.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'Users' 
      ORDER BY ordinal_position;
    `);
    
    console.log('Current columns:');
    columns.forEach(col => {
      console.log(`  - ${col.column_name} (${col.data_type}, nullable: ${col.is_nullable})`);
    });
    console.log('');

    // Check if clerkId column exists
    const hasClerkId = columns.some(col => col.column_name === 'clerkId');
    const hasAuthProvider = columns.some(col => col.column_name === 'authProvider');

    if (hasClerkId && hasAuthProvider) {
      console.log('✅ All Clerk columns already exist! No changes needed.');
      return;
    }

    console.log('🔧 Adding missing Clerk columns...\n');

    // Add clerkId column if it doesn't exist
    if (!hasClerkId) {
      console.log('📝 Adding clerkId column...');
      await sequelize.query(`
        ALTER TABLE "Users" 
        ADD COLUMN "clerkId" VARCHAR(255);
      `);
      console.log('✅ clerkId column added');
    } else {
      console.log('✅ clerkId column already exists');
    }

    // Create enum type for authProvider if it doesn't exist
    console.log('📝 Creating authProvider enum type...');
    await sequelize.query(`
      DO $$ BEGIN
        CREATE TYPE user_auth_provider AS ENUM ('local', 'clerk');
      EXCEPTION
        WHEN duplicate_object THEN 
          RAISE NOTICE 'Type user_auth_provider already exists, skipping...';
      END $$;
    `);

    // Add authProvider column if it doesn't exist
    if (!hasAuthProvider) {
      console.log('📝 Adding authProvider column...');
      await sequelize.query(`
        ALTER TABLE "Users" 
        ADD COLUMN "authProvider" user_auth_provider DEFAULT 'local';
      `);
      console.log('✅ authProvider column added');
    } else {
      console.log('✅ authProvider column already exists');
    }

    // Make password column nullable
    console.log('📝 Making password column optional...');
    await sequelize.query(`
      ALTER TABLE "Users" 
      ALTER COLUMN "password" DROP NOT NULL;
    `);
    console.log('✅ Password column is now optional');

    // Add unique constraint for clerkId
    console.log('📝 Adding unique constraint for clerkId...');
    await sequelize.query(`
      DO $$ BEGIN
        ALTER TABLE "Users" ADD CONSTRAINT "users_clerk_id_unique" UNIQUE ("clerkId");
      EXCEPTION
        WHEN duplicate_table THEN 
          RAISE NOTICE 'Unique constraint already exists, skipping...';
      END $$;
    `);
    console.log('✅ Unique constraint added for clerkId');

    // Verify the changes
    console.log('\n🔍 Verifying changes...');
    const [newColumns] = await sequelize.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'Users' 
      ORDER BY ordinal_position;
    `);
    
    console.log('Updated columns:');
    newColumns.forEach(col => {
      console.log(`  - ${col.column_name} (${col.data_type}, nullable: ${col.is_nullable})`);
    });

    console.log('\n🎉 Clerk database fix completed successfully!');
    console.log('📋 Summary of changes:');
    console.log('   ✅ Added clerkId column for Clerk user IDs');
    console.log('   ✅ Added authProvider column to track auth method');
    console.log('   ✅ Made password column optional for Clerk users');
    console.log('   ✅ Added unique constraint for clerkId');
    console.log('\n🚀 You can now restart your backend server and test the authentication!');

  } catch (error) {
    console.error('\n❌ Error fixing database:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    await sequelize.close();
    console.log('\n🔍 Database connection closed');
  }
}

// Run the fix
fixClerkDatabase();
