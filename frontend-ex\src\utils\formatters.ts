/**
 * Format a price value to a currency string
 * @param price - The price value to format
 * @param locale - The locale to use for formatting (default: 'en-US')
 * @param currency - The currency to use for formatting (default: 'USD')
 * @returns Formatted price string
 */
export const formatPrice = (
  price: number,
  locale = 'en-US',
  currency = 'USD'
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(price);
};

/**
 * Format a date to a localized string
 * @param date - The date to format
 * @param locale - The locale to use for formatting (default: 'en-US')
 * @returns Formatted date string
 */
export const formatDate = (
  date: Date | string,
  locale = 'en-US'
): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

/**
 * Truncate a string to a specified length
 * @param str - The string to truncate
 * @param length - The maximum length of the string (default: 100)
 * @returns Truncated string
 */
export const truncateString = (str: string, length = 100): string => {
  if (str.length <= length) return str;
  return str.slice(0, length) + '...';
};
