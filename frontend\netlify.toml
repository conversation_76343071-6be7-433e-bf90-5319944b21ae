[build]
  command = "npm run build"
  publish = "dist"

# Handle SPA routing by redirecting all requests to index.html
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Environment variable settings
[context.production.environment]
  VITE_API_URL = "https://your-production-api-url.com/api"

[context.deploy-preview.environment]
  VITE_API_URL = "https://your-staging-api-url.com/api"

[context.branch-deploy.environment]
  VITE_API_URL = "https://your-staging-api-url.com/api"
