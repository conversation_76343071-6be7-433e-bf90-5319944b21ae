const crypto = require('crypto');
const moment = require('moment');
const logger = require('../../../utils/logger');

/**
 * M-Pesa B2C Utility Functions
 * Helper functions for B2C operations
 */
class B2CUtils {
  
  /**
   * Generate unique conversation ID
   */
  static generateConversationId(prefix = 'G20-B2C') {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `${prefix}-${timestamp}-${random}`;
  }

  /**
   * Format phone number to international format
   */
  static formatPhoneNumber(phoneNumber) {
    if (!phoneNumber) return null;
    
    // Remove any spaces, dashes, or special characters
    const cleaned = phoneNumber.replace(/[\s\-\(\)]/g, '');
    
    // Handle different formats
    if (cleaned.startsWith('0')) {
      return `254${cleaned.substring(1)}`;
    } else if (cleaned.startsWith('254')) {
      return cleaned;
    } else if (cleaned.startsWith('+254')) {
      return cleaned.substring(1);
    } else {
      return `254${cleaned}`;
    }
  }

  /**
   * Validate Kenyan phone number
   */
  static isValidKenyanPhone(phoneNumber) {
    const formatted = this.formatPhoneNumber(phoneNumber);
    return /^254[0-9]{9}$/.test(formatted);
  }

  /**
   * Mask phone number for logging (show only last 4 digits)
   */
  static maskPhoneNumber(phoneNumber) {
    if (!phoneNumber || phoneNumber.length < 4) return '****';
    return `****${phoneNumber.slice(-4)}`;
  }

  /**
   * Format amount to ensure it's a valid number
   */
  static formatAmount(amount) {
    const parsed = parseFloat(amount);
    if (isNaN(parsed) || parsed <= 0) {
      throw new Error('Invalid amount');
    }
    return Math.round(parsed); // M-Pesa doesn't support decimals
  }

  /**
   * Validate amount is within B2C limits
   */
  static validateAmount(amount) {
    const formatted = this.formatAmount(amount);
    
    if (formatted < 1) {
      throw new Error('Amount must be at least KES 1');
    }
    
    if (formatted > 150000) {
      throw new Error('Amount cannot exceed KES 150,000');
    }
    
    return formatted;
  }

  /**
   * Generate security credential (for production)
   * This is a placeholder - in production, you'd use the actual M-Pesa certificate
   */
  static generateSecurityCredential(initiatorPassword) {
    // In production, this should encrypt the password using M-Pesa's public certificate
    // For sandbox, you can use the provided security credential
    return process.env.MPESA_SECURITY_CREDENTIAL || initiatorPassword;
  }

  /**
   * Parse M-Pesa callback result parameters
   */
  static parseCallbackResult(resultParameters) {
    if (!resultParameters || !Array.isArray(resultParameters)) {
      return {};
    }

    const parsed = {};
    
    resultParameters.forEach(param => {
      if (param.Key && param.Value !== undefined) {
        switch (param.Key) {
          case 'ReceiptNo':
            parsed.receiptNumber = param.Value;
            break;
          case 'TransactionAmount':
            parsed.transactionAmount = parseFloat(param.Value);
            break;
          case 'TransactionCompletedDateTime':
            parsed.transactionDate = new Date(param.Value);
            break;
          case 'B2CChargesPaidAccountAvailableFunds':
            parsed.b2cCharges = parseFloat(param.Value);
            break;
          case 'ReceiverPartyPublicName':
            parsed.receiverName = param.Value;
            break;
          case 'B2CUtilityAccountAvailableFunds':
            parsed.utilityBalance = parseFloat(param.Value);
            break;
          case 'B2CWorkingAccountAvailableFunds':
            parsed.workingBalance = parseFloat(param.Value);
            break;
          default:
            parsed[param.Key] = param.Value;
        }
      }
    });

    return parsed;
  }

  /**
   * Calculate transaction fees (approximate)
   */
  static calculateTransactionFee(amount) {
    // M-Pesa B2C charges (approximate - check current rates)
    if (amount <= 49) return 7;
    if (amount <= 100) return 7;
    if (amount <= 500) return 7;
    if (amount <= 1000) return 7;
    if (amount <= 1500) return 12;
    if (amount <= 2500) return 23;
    if (amount <= 3500) return 28;
    if (amount <= 5000) return 33;
    if (amount <= 7500) return 53;
    if (amount <= 10000) return 58;
    if (amount <= 15000) return 78;
    if (amount <= 20000) return 83;
    if (amount <= 25000) return 88;
    if (amount <= 30000) return 93;
    if (amount <= 35000) return 103;
    if (amount <= 40000) return 108;
    if (amount <= 45000) return 113;
    if (amount <= 50000) return 118;
    if (amount <= 150000) return 118;
    
    return 118; // Maximum fee
  }

  /**
   * Get transaction status description
   */
  static getStatusDescription(status) {
    const descriptions = {
      'PENDING': 'Transaction is pending initiation',
      'SUBMITTED': 'Transaction has been submitted to M-Pesa',
      'COMPLETED': 'Transaction completed successfully',
      'FAILED': 'Transaction failed',
      'TIMEOUT': 'Transaction timed out',
      'CANCELLED': 'Transaction was cancelled'
    };
    
    return descriptions[status] || 'Unknown status';
  }

  /**
   * Get transaction type description
   */
  static getTransactionTypeDescription(type) {
    const descriptions = {
      'REFUND': 'Customer refund',
      'SALARY': 'Employee salary payment',
      'PROMOTION': 'Promotional payment',
      'GENERAL': 'General business payment'
    };
    
    return descriptions[type] || 'Unknown transaction type';
  }

  /**
   * Generate transaction reference
   */
  static generateTransactionReference(type, orderId = null) {
    const timestamp = moment().format('YYMMDDHHmmss');
    const random = Math.random().toString(36).substring(2, 6).toUpperCase();
    
    if (orderId) {
      return `${type}-${orderId.substring(0, 8)}-${timestamp}-${random}`;
    }
    
    return `${type}-${timestamp}-${random}`;
  }

  /**
   * Validate callback authenticity (basic check)
   */
  static validateCallback(callbackData, expectedTransactionId) {
    try {
      // Basic validation - in production, you might want to add more security
      if (!callbackData || !callbackData.Result) {
        return false;
      }

      // Check if the callback contains expected structure
      const result = callbackData.Result;
      if (typeof result.ResultCode === 'undefined' || !result.ResultDesc) {
        return false;
      }

      return true;
    } catch (error) {
      logger.error('❌ Callback validation error:', error);
      return false;
    }
  }

  /**
   * Sanitize callback data for logging
   */
  static sanitizeCallbackData(callbackData) {
    try {
      const sanitized = JSON.parse(JSON.stringify(callbackData));
      
      // Remove or mask sensitive information
      if (sanitized.Result && sanitized.Result.ResultParameters) {
        sanitized.Result.ResultParameters = sanitized.Result.ResultParameters.map(param => {
          if (param.Key === 'ReceiverPartyPublicName') {
            param.Value = this.maskPhoneNumber(param.Value);
          }
          return param;
        });
      }
      
      return sanitized;
    } catch (error) {
      return { error: 'Failed to sanitize callback data' };
    }
  }

  /**
   * Check if transaction is within business hours
   */
  static isWithinBusinessHours(date = new Date()) {
    const hour = date.getHours();
    const day = date.getDay(); // 0 = Sunday, 6 = Saturday
    
    // M-Pesa operates 24/7, but you might want to restrict business payments
    // to business hours for certain transaction types
    
    // Business hours: Monday-Friday 6 AM to 10 PM, Saturday-Sunday 6 AM to 10 PM
    if (hour >= 6 && hour <= 22) {
      return true;
    }
    
    return false;
  }

  /**
   * Get next business day
   */
  static getNextBusinessDay(date = new Date()) {
    const nextDay = new Date(date);
    nextDay.setDate(nextDay.getDate() + 1);
    
    // If it's Saturday (6) or Sunday (0), move to Monday
    while (nextDay.getDay() === 0 || nextDay.getDay() === 6) {
      nextDay.setDate(nextDay.getDate() + 1);
    }
    
    return nextDay;
  }

  /**
   * Format currency for display
   */
  static formatCurrency(amount, currency = 'KES') {
    const formatted = parseFloat(amount).toLocaleString('en-KE', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
    
    return formatted;
  }

  /**
   * Generate batch ID for bulk payments
   */
  static generateBatchId(prefix = 'BATCH') {
    const timestamp = moment().format('YYYYMMDD-HHmmss');
    const random = Math.random().toString(36).substring(2, 6).toUpperCase();
    return `${prefix}-${timestamp}-${random}`;
  }

  /**
   * Validate business rules for B2C transactions
   */
  static validateBusinessRules(transactionData) {
    const errors = [];
    
    const { amount, transactionType, phoneNumber } = transactionData;
    
    // Daily limit check (example: max 500,000 per day per phone number)
    // This would require checking against database records
    
    // Transaction type specific rules
    if (transactionType === 'SALARY' && amount > 100000) {
      errors.push('Salary payments cannot exceed KES 100,000');
    }
    
    if (transactionType === 'PROMOTION' && amount > 10000) {
      errors.push('Promotional payments cannot exceed KES 10,000');
    }
    
    // Time-based restrictions
    if (transactionType === 'SALARY' && !this.isWithinBusinessHours()) {
      errors.push('Salary payments can only be processed during business hours');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

module.exports = B2CUtils;
