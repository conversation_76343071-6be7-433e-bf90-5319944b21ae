const { Review, Product, User, OrderItem, Order } = require('../db/models');
const { NotFoundError, ValidationError, AuthorizationError } = require('../utils/errors');
const { sequelize } = require('../db/models');
const { Op } = require('sequelize');

/**
 * Create a new product review
 */
exports.createReview = async (userId, reviewData) => {
  const { productId, orderId, orderItemId, rating, title, comment, images } = reviewData;
  
  // Verify product exists
  const product = await Product.findByPk(productId);
  
  if (!product) {
    throw new NotFoundError('Product not found');
  }
  
  // Check if user has already reviewed this product for this order
  if (orderId) {
    const existingReview = await Review.findOne({
      where: {
        userId,
        productId,
        orderId
      }
    });
    
    if (existingReview) {
      throw new ValidationError('You have already reviewed this product for this order');
    }
  }
  
  // Verify purchase if order details provided
  let isVerifiedPurchase = false;
  
  if (orderId && orderItemId) {
    const orderItem = await OrderItem.findOne({
      where: {
        id: orderItemId,
        orderId,
        productId
      },
      include: [
        {
          model: Order,
          where: {
            userId,
            status: { [Op.in]: ['delivered', 'completed'] }
          }
        }
      ]
    });
    
    if (!orderItem) {
      throw new ValidationError('You can only review products you have purchased');
    }
    
    isVerifiedPurchase = true;
    
    // Mark order item as reviewed
    await orderItem.update({ isReviewed: true });
  }
  
  // Create review
  const review = await Review.create({
    userId,
    productId,
    orderId,
    orderItemId,
    rating,
    title,
    comment,
    images,
    isVerifiedPurchase
  });
  
  // Update product average rating
  await updateProductRating(productId);
  
  return getReviewById(review.id);
};

/**
 * Get review by ID
 */
const getReviewById = async (id) => {
  const review = await Review.findByPk(id, {
    include: [
      {
        model: User,
        attributes: ['id', 'name']
      },
      {
        model: Product,
        attributes: ['id', 'name', 'slug']
      }
    ]
  });
  
  if (!review) {
    throw new NotFoundError('Review not found');
  }
  
  return review;
};

exports.getReviewById = getReviewById;

/**
 * Update a review
 */
exports.updateReview = async (id, userId, updateData) => {
  const review = await Review.findByPk(id);
  
  if (!review) {
    throw new NotFoundError('Review not found');
  }
  
  // Verify ownership
  if (review.userId !== userId) {
    throw new AuthorizationError('You can only update your own reviews');
  }
  
  // Update review
  await review.update({
    rating: updateData.rating || review.rating,
    title: updateData.title || review.title,
    comment: updateData.comment || review.comment,
    images: updateData.images || review.images
  });
  
  // Update product average rating if rating changed
  if (updateData.rating) {
    await updateProductRating(review.productId);
  }
  
  return getReviewById(id);
};

/**
 * Delete a review
 */
exports.deleteReview = async (id, userId, isAdmin = false) => {
  const review = await Review.findByPk(id);
  
  if (!review) {
    throw new NotFoundError('Review not found');
  }
  
  // Verify ownership or admin status
  if (!isAdmin && review.userId !== userId) {
    throw new AuthorizationError('You can only delete your own reviews');
  }
  
  const productId = review.productId;
  
  // Delete review
  await review.destroy();
  
  // Update product average rating
  await updateProductRating(productId);
  
  return true;
};

/**
 * Get reviews for a product
 */
exports.getProductReviews = async (productId, { page = 1, limit = 10, sort = 'createdAt', order = 'DESC' }) => {
  const offset = (page - 1) * limit;
  
  // Verify product exists
  const product = await Product.findByPk(productId);
  
  if (!product) {
    throw new NotFoundError('Product not found');
  }
  
  // Get reviews
  const { count, rows } = await Review.findAndCountAll({
    where: {
      productId,
      isApproved: true
    },
    include: [
      {
        model: User,
        attributes: ['id', 'name']
      }
    ],
    order: [[sort, order]],
    limit,
    offset
  });
  
  return {
    reviews: rows,
    totalItems: count,
    totalPages: Math.ceil(count / limit),
    currentPage: page
  };
};

/**
 * Get reviews by a user
 */
exports.getUserReviews = async (userId, { page = 1, limit = 10 }) => {
  const offset = (page - 1) * limit;
  
  const { count, rows } = await Review.findAndCountAll({
    where: { userId },
    include: [
      {
        model: Product,
        attributes: ['id', 'name', 'slug']
      }
    ],
    order: [['createdAt', 'DESC']],
    limit,
    offset
  });
  
  return {
    reviews: rows,
    totalItems: count,
    totalPages: Math.ceil(count / limit),
    currentPage: page
  };
};

/**
 * Admin: Approve or reject a review
 */
exports.moderateReview = async (id, isApproved, adminResponse = null) => {
  const review = await Review.findByPk(id);
  
  if (!review) {
    throw new NotFoundError('Review not found');
  }
  
  // Update review
  await review.update({
    isApproved,
    adminResponse,
    adminResponseAt: adminResponse ? new Date() : null
  });
  
  // Update product average rating
  await updateProductRating(review.productId);
  
  return getReviewById(id);
};

/**
 * Mark review as helpful or not helpful
 */
exports.markReviewHelpfulness = async (id, isHelpful) => {
  const review = await Review.findByPk(id);
  
  if (!review) {
    throw new NotFoundError('Review not found');
  }
  
  // Update helpfulness count
  if (isHelpful) {
    await review.increment('isHelpful');
  } else {
    await review.increment('isNotHelpful');
  }
  
  return getReviewById(id);
};

/**
 * Update product average rating
 */
const updateProductRating = async (productId) => {
  const result = await Review.findAll({
    where: {
      productId,
      isApproved: true
    },
    attributes: [
      [sequelize.fn('AVG', sequelize.col('rating')), 'averageRating'],
      [sequelize.fn('COUNT', sequelize.col('id')), 'totalReviews']
    ],
    raw: true
  });
  
  const averageRating = result[0].averageRating || 0;
  const totalReviews = result[0].totalReviews || 0;
  
  await Product.update(
    {
      averageRating,
      totalReviews
    },
    { where: { id: productId } }
  );
};
