const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

async function runFinalTest() {
  console.log('🚀 Final API Test Summary\n');
  console.log('=' .repeat(50));

  const tests = [
    {
      name: 'Products List',
      url: `${API_BASE_URL}/products`,
      expectedStatus: 200,
      description: 'Public endpoint - should work'
    },
    {
      name: 'Product Categories',
      url: `${API_BASE_URL}/products/categories`,
      expectedStatus: 200,
      description: 'Public endpoint - should work'
    },
    {
      name: 'Users Profile (No Auth)',
      url: `${API_BASE_URL}/users/profile`,
      expectedStatus: 401,
      description: 'Protected endpoint - should require auth'
    },
    {
      name: 'Admin Dashboard (No Auth)',
      url: `${API_BASE_URL}/admin/dashboard`,
      expectedStatus: 401,
      description: 'Admin endpoint - should require auth'
    }
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      const response = await axios.get(test.url, {
        validateStatus: () => true,
        timeout: 5000
      });

      const success = response.status === test.expectedStatus;
      const status = success ? '✅' : '❌';
      
      console.log(`${status} ${test.name}: ${response.status} (expected ${test.expectedStatus})`);
      console.log(`   ${test.description}`);
      
      if (success) {
        passed++;
      } else {
        failed++;
        console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
      }
      
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.message}`);
      failed++;
    }
    console.log('');
  }

  console.log('=' .repeat(50));
  console.log(`📊 Test Results: ${passed} passed, ${failed} failed`);
  
  if (failed === 0) {
    console.log('🎉 ALL TESTS PASSED! Your API is working correctly.');
    console.log('\n💡 The 404 and 500 errors you were seeing should now be resolved!');
    console.log('\n🔧 What was fixed:');
    console.log('   • Added missing /users/profile endpoint');
    console.log('   • Fixed Clerk authentication middleware to return 401 instead of 500');
    console.log('   • Improved error handling for authentication failures');
  } else {
    console.log('⚠️  Some tests failed. Please check the errors above.');
  }
}

runFinalTest().catch(console.error);
