/**
 * Run B2C Migration Script
 * This script will create the B2C transactions table directly using Sequelize
 */

const sequelize = require('./src/config/database');

async function runB2CMigration() {
  try {
    console.log('🚀 Starting B2C Migration...');

    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // Create ENUM types
    console.log('📝 Creating ENUM types...');

    await sequelize.query(`
      DO $$ BEGIN
        CREATE TYPE enum_b2c_transactions_transactiontype AS ENUM ('REFUND', 'SALARY', 'PROMOTION', 'GENERAL');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await sequelize.query(`
      DO $$ BEGIN
        CREATE TYPE enum_b2c_transactions_commandid AS ENUM ('BusinessPayment', 'SalaryPayment', 'PromotionPayment');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await sequelize.query(`
      DO $$ BEGIN
        CREATE TYPE enum_b2c_transactions_status AS ENUM ('PENDING', 'SUBMITTED', 'COMPLETED', 'FAILED', 'TIMEOUT', 'CANCELLED');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    console.log('✅ ENUM types created successfully.');

    // Create the b2c_transactions table
    console.log('📝 Creating b2c_transactions table...');

    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS b2c_transactions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        "conversationId" VARCHAR(255) NOT NULL UNIQUE,
        "mpesaConversationId" VARCHAR(255),
        "originatorConversationId" VARCHAR(255),
        "phoneNumber" VARCHAR(255) NOT NULL,
        amount DECIMAL(10, 2) NOT NULL,
        "transactionType" enum_b2c_transactions_transactiontype NOT NULL DEFAULT 'GENERAL',
        "commandId" enum_b2c_transactions_commandid NOT NULL DEFAULT 'BusinessPayment',
        remarks VARCHAR(255) NOT NULL DEFAULT 'G20Shop Payment',
        occasion VARCHAR(255) NOT NULL DEFAULT 'Payment Processing',
        status enum_b2c_transactions_status NOT NULL DEFAULT 'PENDING',
        "responseCode" VARCHAR(255),
        "responseDescription" VARCHAR(255),
        "resultCode" INTEGER,
        "resultDescription" VARCHAR(255),
        "mpesaReceiptNumber" VARCHAR(255),
        "transactionAmount" DECIMAL(10, 2),
        "transactionDate" TIMESTAMP WITH TIME ZONE,
        "b2cCharges" DECIMAL(10, 2),
        "receiverPartyName" VARCHAR(255),
        "b2cUtilityBalance" DECIMAL(15, 2),
        "b2cWorkingBalance" DECIMAL(15, 2),
        "orderId" UUID,
        "userId" VARCHAR(255),
        metadata JSONB,
        "callbackData" JSONB,
        "initiatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        "completedAt" TIMESTAMP WITH TIME ZONE,
        "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
      );
    `);

    console.log('✅ b2c_transactions table created successfully.');

    // Create indexes
    console.log('📝 Creating indexes...');

    const indexes = [
      'CREATE UNIQUE INDEX IF NOT EXISTS idx_b2c_transactions_conversation_id ON b2c_transactions ("conversationId");',
      'CREATE INDEX IF NOT EXISTS idx_b2c_transactions_mpesa_conversation_id ON b2c_transactions ("mpesaConversationId");',
      'CREATE INDEX IF NOT EXISTS idx_b2c_transactions_phone_number ON b2c_transactions ("phoneNumber");',
      'CREATE INDEX IF NOT EXISTS idx_b2c_transactions_status ON b2c_transactions (status);',
      'CREATE INDEX IF NOT EXISTS idx_b2c_transactions_type ON b2c_transactions ("transactionType");',
      'CREATE INDEX IF NOT EXISTS idx_b2c_transactions_order_id ON b2c_transactions ("orderId");',
      'CREATE INDEX IF NOT EXISTS idx_b2c_transactions_user_id ON b2c_transactions ("userId");',
      'CREATE INDEX IF NOT EXISTS idx_b2c_transactions_initiated_at ON b2c_transactions ("initiatedAt");',
      'CREATE UNIQUE INDEX IF NOT EXISTS idx_b2c_transactions_receipt_number ON b2c_transactions ("mpesaReceiptNumber") WHERE "mpesaReceiptNumber" IS NOT NULL;'
    ];

    for (const indexQuery of indexes) {
      await sequelize.query(indexQuery);
    }

    console.log('✅ Indexes created successfully.');

    // Create trigger for auto-updating updatedAt
    console.log('📝 Creating trigger...');

    await sequelize.query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW."updatedAt" = NOW();
          RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);

    await sequelize.query(`
      DROP TRIGGER IF EXISTS update_b2c_transactions_updated_at ON b2c_transactions;
      CREATE TRIGGER update_b2c_transactions_updated_at
          BEFORE UPDATE ON b2c_transactions
          FOR EACH ROW
          EXECUTE FUNCTION update_updated_at_column();
    `);

    console.log('✅ Trigger created successfully.');

    // Skip SequelizeMeta update since table doesn't exist yet
    console.log('ℹ️  Skipping SequelizeMeta update (table not found).');

    // Verify table creation
    console.log('🔍 Verifying table structure...');

    const [results] = await sequelize.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'b2c_transactions'
      ORDER BY ordinal_position;
    `);

    console.log('📊 Table structure:');
    console.table(results);

    console.log('🎉 B2C Migration completed successfully!');
    console.log('✅ The b2c_transactions table is now ready for use.');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run the migration
runB2CMigration();
