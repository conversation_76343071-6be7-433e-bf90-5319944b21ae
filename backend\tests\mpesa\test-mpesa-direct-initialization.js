/**
 * Direct M-Pesa STK Push test with detailed logging
 * Run this with: node test-mpesa-direct.js
 */

require('dotenv').config({ path: './backend/.env' });
const axios = require('axios');
const moment = require('moment');

// M-Pesa configuration from environment
const config = {
  environment: process.env.MPESA_ENVIRONMENT || 'sandbox',
  consumerKey: process.env.MPESA_CONSUMER_KEY,
  consumerSecret: process.env.MPESA_CONSUMER_SECRET,
  businessShortCode: process.env.MPESA_BUSINESS_SHORT_CODE,
  passkey: process.env.MPESA_LIPA_NA_MPESA_ONLINE_PASSKEY,
  callbackUrl: process.env.MPESA_CALLBACK_URL
};

// Set base URL based on environment
const baseUrl = config.environment === 'production' 
  ? 'https://api.safaricom.co.ke'
  : 'https://sandbox.safaricom.co.ke';

console.log('🔧 M-Pesa Direct Test Configuration:');
console.log('Environment:', config.environment);
console.log('Consumer Key:', config.consumerKey ? `${config.consumerKey.substring(0, 10)}...` : 'NOT SET');
console.log('Consumer Secret:', config.consumerSecret ? `${config.consumerSecret.substring(0, 10)}...` : 'NOT SET');
console.log('Business Short Code:', config.businessShortCode);
console.log('Passkey:', config.passkey ? `${config.passkey.substring(0, 10)}...` : 'NOT SET');
console.log('Callback URL:', config.callbackUrl);
console.log('Base URL:', baseUrl);
console.log('');

// Generate access token
async function generateAccessToken() {
  try {
    console.log('🔑 Generating access token...');
    
    if (!config.consumerKey || !config.consumerSecret) {
      throw new Error('Consumer key or secret not configured');
    }
    
    const auth = Buffer.from(`${config.consumerKey}:${config.consumerSecret}`).toString('base64');
    
    const response = await axios.get(
      `${baseUrl}/oauth/v1/generate?grant_type=client_credentials`,
      {
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      }
    );
    
    console.log('✅ Access token generated successfully!');
    return response.data.access_token;
    
  } catch (error) {
    console.log('❌ Access token generation failed!');
    
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('Error:', error.message);
    }
    
    throw error;
  }
}

// Generate password for STK Push
function generatePassword() {
  const timestamp = moment().format('YYYYMMDDHHmmss');
  const password = Buffer.from(`${config.businessShortCode}${config.passkey}${timestamp}`).toString('base64');
  return { password, timestamp };
}

// Test STK Push with exact same parameters as your checkout
async function testSTKPush() {
  try {
    console.log('💳 Testing STK Push with checkout parameters...');
    
    const accessToken = await generateAccessToken();
    const { password, timestamp } = generatePassword();
    
    // Use the same parameters as your checkout
    const testPhone = '************';
    const testAmount = 75; // Same as your checkout
    const testOrderId = 'test-order-' + Date.now();
    
    const requestBody = {
      BusinessShortCode: config.businessShortCode,
      Password: password,
      Timestamp: timestamp,
      TransactionType: 'CustomerPayBillOnline',
      Amount: Math.round(testAmount),
      PartyA: testPhone,
      PartyB: config.businessShortCode,
      PhoneNumber: testPhone,
      CallBackURL: `${config.callbackUrl}/${testOrderId}`,
      AccountReference: `G20SHOP-${testOrderId}`,
      TransactionDesc: `Payment for G20Shop Order ${testOrderId}`
    };
    
    console.log('📤 STK Push Request:');
    console.log('URL:', `${baseUrl}/mpesa/stkpush/v1/processrequest`);
    console.log('Headers:', {
      'Authorization': `Bearer ${accessToken.substring(0, 20)}...`,
      'Content-Type': 'application/json'
    });
    console.log('Body:', JSON.stringify(requestBody, null, 2));
    console.log('');
    
    const response = await axios.post(
      `${baseUrl}/mpesa/stkpush/v1/processrequest`,
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    );
    
    console.log('✅ STK Push successful!');
    console.log('📥 Response Status:', response.status);
    console.log('📥 Response Data:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('❌ STK Push failed!');
    console.log('');
    
    if (error.response) {
      console.log('📊 Response Status:', error.response.status);
      console.log('📊 Response Status Text:', error.response.statusText);
      console.log('📊 Response Headers:', JSON.stringify(error.response.headers, null, 2));
      console.log('📊 Response Data:', JSON.stringify(error.response.data, null, 2));
      
      // Analyze common M-Pesa errors
      if (error.response.data) {
        const errorData = error.response.data;
        
        console.log('');
        console.log('🔍 Error Analysis:');
        
        if (errorData.errorCode) {
          console.log('Error Code:', errorData.errorCode);
          
          // Common M-Pesa error codes
          const errorCodes = {
            '404.001.03': 'Invalid Access Token - Token may be expired or invalid',
            '400.002.02': 'Bad Request - Invalid request format',
            '************': 'Unable to lock subscriber - Phone number may be invalid',
            '400.008.01': 'Bad Request - Missing required parameters'
          };
          
          if (errorCodes[errorData.errorCode]) {
            console.log('Meaning:', errorCodes[errorData.errorCode]);
          }
        }
        
        if (errorData.errorMessage) {
          console.log('Error Message:', errorData.errorMessage);
        }
      }
      
    } else if (error.request) {
      console.log('🌐 Network Error - No response received');
      console.log('Error:', error.message);
    } else {
      console.log('⚠️ Request Setup Error:', error.message);
    }
  }
}

// Main test function
async function runTest() {
  console.log('🚀 Starting direct M-Pesa STK Push test...\n');
  
  try {
    await testSTKPush();
  } catch (error) {
    console.log('❌ Test failed:', error.message);
  }
}

// Run the test
runTest().catch(console.error);
