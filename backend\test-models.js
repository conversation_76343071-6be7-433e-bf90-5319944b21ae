const { sequelize, User, Product, Category, ProductImage, Cart, CartItem, Wishlist, WishlistItem, Order, OrderItem, Review } = require('./src/db/models');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');

// Create a directory for test results
const resultsDir = path.join(__dirname, 'test-results');
if (!fs.existsSync(resultsDir)) {
  fs.mkdirSync(resultsDir);
}

// Helper function to log results
const logResult = (testName, result) => {
  const logFile = path.join(resultsDir, `${testName}.json`);
  fs.writeFileSync(logFile, JSON.stringify(result, null, 2));
  console.log(`✅ ${testName} - Result saved to ${logFile}`);
};

// Helper function to log errors
const logError = (testName, error) => {
  const errorFile = path.join(resultsDir, `${testName}-error.json`);
  fs.writeFileSync(errorFile, JSON.stringify({
    message: error.message,
    stack: error.stack
  }, null, 2));
  console.error(`❌ ${testName} - Error saved to ${errorFile}`);
};

// Test data
const testData = {
  user: {
    name: 'Test User',
    email: `test${Date.now()}@example.com`,
    password: 'Test@123',
    role: 'customer'
  },
  category: {
    name: 'Test Category',
    slug: 'test-category',
    description: 'A test category'
  },
  product: {
    name: 'Test Product',
    brand: 'Test Brand',
    model: 'Test Model',
    description: 'A test product',
    sku: `TEST-${Date.now()}`,
    regularPrice: 99.99,
    salePrice: 79.99,
    cost: 50.00,
    stockQuantity: 100,
    slug: 'test-product'
  },
  productImage: {
    imageUrl: 'https://example.com/test-image.jpg',
    altText: 'Test Image',
    isPrimary: true
  }
};

// Test functions
const tests = {
  // Database connection test
  async testConnection() {
    try {
      await sequelize.authenticate();
      console.log('✅ Database connection established successfully.');
      return true;
    } catch (error) {
      console.error('❌ Unable to connect to the database:', error);
      return false;
    }
  },

  // User model tests
  async createUser() {
    try {
      const user = await User.create(testData.user);
      logResult('create-user', user.toJSON());
      return user;
    } catch (error) {
      logError('create-user', error);
      throw error;
    }
  },

  async findUserByEmail(email) {
    try {
      const user = await User.findOne({ where: { email } });
      logResult('find-user-by-email', user.toJSON());
      return user;
    } catch (error) {
      logError('find-user-by-email', error);
      throw error;
    }
  },

  // Category model tests
  async createCategory() {
    try {
      const category = await Category.create(testData.category);
      logResult('create-category', category.toJSON());
      return category;
    } catch (error) {
      logError('create-category', error);
      throw error;
    }
  },

  // Product model tests
  async createProduct(categoryId) {
    try {
      // Create product
      const product = await Product.create(testData.product);
      
      // Add product to category
      if (categoryId) {
        await product.addCategory(categoryId);
      }
      
      // Add product image
      await ProductImage.create({
        ...testData.productImage,
        productId: product.id
      });
      
      // Get product with associations
      const productWithAssociations = await Product.findByPk(product.id, {
        include: [
          { model: ProductImage, as: 'images' },
          { model: Category, as: 'categories' }
        ]
      });
      
      logResult('create-product', productWithAssociations.toJSON());
      return productWithAssociations;
    } catch (error) {
      logError('create-product', error);
      throw error;
    }
  },

  // Cart model tests
  async createCart(userId) {
    try {
      const cart = await Cart.create({
        userId,
        status: 'active'
      });
      logResult('create-cart', cart.toJSON());
      return cart;
    } catch (error) {
      logError('create-cart', error);
      throw error;
    }
  },

  async addItemToCart(cartId, productId) {
    try {
      const product = await Product.findByPk(productId);
      
      const cartItem = await CartItem.create({
        cartId,
        productId,
        quantity: 1,
        price: product.salePrice || product.regularPrice,
        totalPrice: product.salePrice || product.regularPrice
      });
      
      // Update cart totals
      const cart = await Cart.findByPk(cartId);
      await cart.update({
        itemCount: 1,
        totalAmount: cartItem.totalPrice
      });
      
      // Get cart with items
      const cartWithItems = await Cart.findByPk(cartId, {
        include: [
          {
            model: CartItem,
            as: 'items',
            include: [{ model: Product }]
          }
        ]
      });
      
      logResult('add-item-to-cart', cartWithItems.toJSON());
      return cartWithItems;
    } catch (error) {
      logError('add-item-to-cart', error);
      throw error;
    }
  },

  // Wishlist model tests
  async createWishlist(userId) {
    try {
      const wishlist = await Wishlist.create({
        userId,
        name: 'Test Wishlist'
      });
      logResult('create-wishlist', wishlist.toJSON());
      return wishlist;
    } catch (error) {
      logError('create-wishlist', error);
      throw error;
    }
  },

  async addItemToWishlist(wishlistId, productId) {
    try {
      const wishlistItem = await WishlistItem.create({
        wishlistId,
        productId
      });
      
      // Get wishlist with items
      const wishlistWithItems = await Wishlist.findByPk(wishlistId, {
        include: [
          {
            model: WishlistItem,
            as: 'items',
            include: [{ model: Product }]
          }
        ]
      });
      
      logResult('add-item-to-wishlist', wishlistWithItems.toJSON());
      return wishlistWithItems;
    } catch (error) {
      logError('add-item-to-wishlist', error);
      throw error;
    }
  },

  // Order model tests
  async createOrder(userId, productId) {
    try {
      const product = await Product.findByPk(productId);
      
      // Create order
      const order = await Order.create({
        userId,
        orderNumber: `ORD-${Date.now()}`,
        status: 'pending',
        totalAmount: product.salePrice || product.regularPrice,
        subtotal: product.salePrice || product.regularPrice,
        tax: 0,
        shippingCost: 0,
        discount: 0,
        shippingAddress: {
          fullName: 'Test User',
          addressLine1: '123 Test St',
          city: 'Test City',
          state: 'Test State',
          postalCode: '12345',
          country: 'Test Country',
          phone: '1234567890'
        },
        billingAddress: {
          fullName: 'Test User',
          addressLine1: '123 Test St',
          city: 'Test City',
          state: 'Test State',
          postalCode: '12345',
          country: 'Test Country',
          phone: '1234567890'
        },
        paymentMethod: 'credit_card',
        paymentStatus: 'pending'
      });
      
      // Create order item
      await OrderItem.create({
        orderId: order.id,
        productId,
        quantity: 1,
        price: product.salePrice || product.regularPrice,
        totalPrice: product.salePrice || product.regularPrice,
        productName: product.name,
        productSku: product.sku
      });
      
      // Get order with items
      const orderWithItems = await Order.findByPk(order.id, {
        include: [
          {
            model: OrderItem,
            as: 'items',
            include: [{ model: Product }]
          }
        ]
      });
      
      logResult('create-order', orderWithItems.toJSON());
      return orderWithItems;
    } catch (error) {
      logError('create-order', error);
      throw error;
    }
  },

  // Review model tests
  async createReview(userId, productId) {
    try {
      const review = await Review.create({
        userId,
        productId,
        rating: 5,
        title: 'Great product',
        comment: 'This is a test review for the product.',
        isVerifiedPurchase: true
      });
      
      // Get review with associations
      const reviewWithAssociations = await Review.findByPk(review.id, {
        include: [
          { model: User, attributes: ['id', 'name'] },
          { model: Product, attributes: ['id', 'name', 'slug'] }
        ]
      });
      
      logResult('create-review', reviewWithAssociations.toJSON());
      return reviewWithAssociations;
    } catch (error) {
      logError('create-review', error);
      throw error;
    }
  }
};

// Run tests
async function runTests() {
  console.log('🚀 Starting model tests...');
  
  try {
    // Test database connection
    const connectionSuccess = await tests.testConnection();
    if (!connectionSuccess) {
      throw new Error('Database connection failed');
    }
    
    // Create test user
    const user = await tests.createUser();
    const foundUser = await tests.findUserByEmail(user.email);
    
    // Create test category
    const category = await tests.createCategory();
    
    // Create test product
    const product = await tests.createProduct(category.id);
    
    // Create test cart and add item
    const cart = await tests.createCart(user.id);
    await tests.addItemToCart(cart.id, product.id);
    
    // Create test wishlist and add item
    const wishlist = await tests.createWishlist(user.id);
    await tests.addItemToWishlist(wishlist.id, product.id);
    
    // Create test order
    await tests.createOrder(user.id, product.id);
    
    // Create test review
    await tests.createReview(user.id, product.id);
    
    console.log('✅ All model tests completed successfully!');
  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
  } finally {
    // Close database connection
    await sequelize.close();
  }
}

// Run the tests
runTests();
