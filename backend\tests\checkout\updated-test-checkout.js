/**
 * Test script to debug checkout validation issues
 * This follows the correct workflow: Add to cart -> Create order
 * Run this with: node test-checkout.js
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';
const TEST_SESSION_ID = 'test-session-123';

// Test product data for adding to cart
const testProduct = {
  productId: "9052489f-1a90-4504-8245-177a524b6d2e", // Real product ID from database
  quantity: 1
};

// Test order data (shipping info only, items come from cart)
const testOrderData = {
  shippingAddress: {
    fullName: "<PERSON>",
    email: "<EMAIL>",
    phone: "0712345678",
    addressLine1: "123 Test Street",
    city: "Nairobi",
    state: "Nairobi",
    postalCode: "00100",
    country: "Kenya"
  },
  paymentMethod: "mpesa",
  subtotal: 1000,
  tax: 160,
  shippingCost: 0,
  totalAmount: 1160,
  email: "<EMAIL>", // Root level email
  phone: "0712345678"
};

// Test adding item to cart
async function testAddToCart() {
  try {
    console.log('🛒 Testing add to cart...');
    console.log('📤 Adding product to cart:', JSON.stringify(testProduct, null, 2));

    const response = await axios.post(
      `${API_BASE_URL}/cart/items`,
      testProduct,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-session-id': TEST_SESSION_ID
        }
      }
    );

    console.log('✅ Item added to cart successfully!');
    console.log('📥 Cart response:', JSON.stringify(response.data, null, 2));
    return true;

  } catch (error) {
    console.log('❌ Add to cart failed!');

    if (error.response) {
      console.log('📊 Status:', error.response.status);
      console.log('📋 Error data:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log('🌐 Network error - no response received');
    } else {
      console.log('⚠️ Error:', error.message);
    }
    return false;
  }
}

// Test getting cart contents
async function testGetCart() {
  try {
    console.log('📋 Testing get cart...');

    const response = await axios.get(
      `${API_BASE_URL}/cart`,
      {
        headers: {
          'x-session-id': TEST_SESSION_ID
        }
      }
    );

    console.log('✅ Cart retrieved successfully!');
    console.log('📥 Cart contents:', JSON.stringify(response.data, null, 2));
    return response.data;

  } catch (error) {
    console.log('❌ Get cart failed!');

    if (error.response) {
      console.log('📊 Status:', error.response.status);
      console.log('📋 Error data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('⚠️ Error:', error.message);
    }
    return null;
  }
}

async function testOrderCreation() {
  try {
    console.log('🧪 Testing order creation...');
    console.log('📤 Sending order data:', JSON.stringify(testOrderData, null, 2));

    const response = await axios.post(
      `${API_BASE_URL}/orders`,
      testOrderData,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-session-id': TEST_SESSION_ID // For guest checkout
        }
      }
    );

    console.log('✅ Order created successfully!');
    console.log('📥 Response:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.log('❌ Order creation failed!');

    if (error.response) {
      console.log('📊 Status:', error.response.status);
      console.log('📋 Error data:', JSON.stringify(error.response.data, null, 2));

      // Check for validation errors
      if (error.response.data.errors) {
        console.log('\n🔍 Validation errors:');
        error.response.data.errors.forEach((err, index) => {
          console.log(`  ${index + 1}. Field: ${err.field || 'unknown'}`);
          console.log(`     Message: ${err.message}`);
          console.log(`     Value: ${err.value || 'undefined'}`);
        });
      }
    } else if (error.request) {
      console.log('🌐 Network error - no response received');
      console.log('Request details:', error.request);
    } else {
      console.log('⚠️ Error:', error.message);
    }
  }
}

// Test server connectivity first
async function testServerConnection() {
  try {
    console.log('🔗 Testing server connection...');
    const response = await axios.get(`${API_BASE_URL.replace('/api', '')}/health`);
    console.log('✅ Server is responding:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Server connection failed:', error.message);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting checkout validation tests...\n');

  // Test server connection
  const serverOk = await testServerConnection();
  if (!serverOk) {
    console.log('❌ Cannot proceed - server is not responding');
    return;
  }

  console.log(''); // Empty line for readability

  // Step 1: Add item to cart
  console.log('📝 Step 1: Adding item to cart...');
  const cartSuccess = await testAddToCart();
  if (!cartSuccess) {
    console.log('❌ Cannot proceed - failed to add item to cart');
    return;
  }

  console.log(''); // Empty line for readability

  // Step 2: Check cart contents
  console.log('📝 Step 2: Checking cart contents...');
  const cart = await testGetCart();
  if (!cart) {
    console.log('❌ Cannot proceed - failed to retrieve cart');
    return;
  }

  console.log(''); // Empty line for readability

  // Step 3: Create order from cart
  console.log('📝 Step 3: Creating order from cart...');
  await testOrderCreation();
}

// Run the tests
runTests().catch(console.error);
