-- Create import_jobs table
CREATE TABLE IF NOT EXISTS import_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    "sourceType" VARCHAR(50) NOT NULL DEFAULT 'file_upload' CHECK ("sourceType" IN ('file_upload', 'ftp', 'http_url', 'google_sheets', 'api_webhook')),
    "sourceConfig" JSONB,
    schedule VARCHAR(255) NOT NULL,
    timezone VARCHAR(50) NOT NULL DEFAULT 'UTC',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "lastRunAt" TIMESTAMP WITH TIME ZONE,
    "nextRunAt" TIMESTAMP WITH TIME ZONE,
    "lastRunStatus" VARCHAR(50) CHECK ("lastRunStatus" IN ('pending', 'running', 'success', 'failed', 'cancelled')),
    "lastRunResult" JSONB,
    "totalRuns" INTEGER NOT NULL DEFAULT 0,
    "successfulRuns" INTEGER NOT NULL DEFAULT 0,
    "failedRuns" INTEGER NOT NULL DEFAULT 0,
    "createdBy" VARCHAR(255) NOT NULL,
    "updatedBy" VARCHAR(255),
    "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create import_job_logs table
CREATE TABLE IF NOT EXISTS import_job_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "importJobId" UUID NOT NULL REFERENCES import_jobs(id) ON DELETE CASCADE,
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'success', 'failed', 'cancelled')),
    "startedAt" TIMESTAMP WITH TIME ZONE,
    "completedAt" TIMESTAMP WITH TIME ZONE,
    duration INTEGER,
    "recordsProcessed" INTEGER NOT NULL DEFAULT 0,
    "recordsImported" INTEGER NOT NULL DEFAULT 0,
    "recordsFailed" INTEGER NOT NULL DEFAULT 0,
    errors JSONB,
    "sourceInfo" JSONB,
    result JSONB,
    "triggeredBy" VARCHAR(50) NOT NULL DEFAULT 'schedule' CHECK ("triggeredBy" IN ('schedule', 'manual', 'api')),
    "triggeredByUser" VARCHAR(255),
    "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_import_jobs_is_active ON import_jobs("isActive");
CREATE INDEX IF NOT EXISTS idx_import_jobs_next_run_at ON import_jobs("nextRunAt");
CREATE INDEX IF NOT EXISTS idx_import_jobs_created_by ON import_jobs("createdBy");
CREATE INDEX IF NOT EXISTS idx_import_job_logs_import_job_id ON import_job_logs("importJobId");
CREATE INDEX IF NOT EXISTS idx_import_job_logs_status ON import_job_logs(status);
CREATE INDEX IF NOT EXISTS idx_import_job_logs_started_at ON import_job_logs("startedAt");
CREATE INDEX IF NOT EXISTS idx_import_job_logs_triggered_by ON import_job_logs("triggeredBy");

-- Insert a sample import job for testing
INSERT INTO import_jobs (name, description, "sourceType", schedule, "createdBy")
VALUES ('Sample HTTP Import', 'Demo import job from HTTP URL', 'http_url', '0 0 * * *', 'system')
ON CONFLICT DO NOTHING;

-- Verify tables were created
SELECT 'import_jobs table created' as status, COUNT(*) as job_count FROM import_jobs;
SELECT 'import_job_logs table created' as status, COUNT(*) as log_count FROM import_job_logs;
