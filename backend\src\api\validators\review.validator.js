const { body, param } = require('express-validator');
const { validate } = require('../middlewares/validation.middleware');

exports.validateReview = [
  body('productId')
    .notEmpty().withMessage('Product ID is required')
    .isUUID().withMessage('Product ID must be a valid UUID'),
  
  body('rating')
    .notEmpty().withMessage('Rating is required')
    .isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
  
  body('title')
    .optional()
    .isString().withMessage('Title must be a string')
    .isLength({ max: 100 }).withMessage('Title must be at most 100 characters'),
  
  body('comment')
    .notEmpty().withMessage('Comment is required')
    .isString().withMessage('Comment must be a string')
    .isLength({ min: 10, max: 1000 }).withMessage('Comment must be between 10 and 1000 characters'),
  
  body('orderId')
    .optional()
    .isUUID().withMessage('Order ID must be a valid UUID'),
  
  body('orderItemId')
    .optional()
    .isUUID().withMessage('Order item ID must be a valid UUID'),
  
  validate
];

exports.validateReviewUpdate = [
  param('id')
    .isUUID().withMessage('Review ID must be a valid UUID'),
  
  body('rating')
    .optional()
    .isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
  
  body('title')
    .optional()
    .isString().withMessage('Title must be a string')
    .isLength({ max: 100 }).withMessage('Title must be at most 100 characters'),
  
  body('comment')
    .optional()
    .isString().withMessage('Comment must be a string')
    .isLength({ min: 10, max: 1000 }).withMessage('Comment must be between 10 and 1000 characters'),
  
  validate
];
