-- Database Setup Script for TechGear E-commerce Platform
-- Run this script in pgAdmin to set up the database schema

-- Create Users Table
CREATE TABLE IF NOT EXISTS "Users" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "name" VARCHAR(255) NOT NULL,
  "email" VARCHAR(255) NOT NULL UNIQUE,
  "password" VARCHAR(255) NOT NULL,
  "role" VARCHAR(20) NOT NULL DEFAULT 'customer',
  "isActive" BOOLEAN DEFAULT TRUE,
  "lastLogin" TIMESTAMP,
  "phone" VARCHAR(20),
  "address" JSONB,
  "resetPasswordToken" VARCHAR(255),
  "resetPasswordExpires" TIMESTAMP,
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create Categories Table
CREATE TABLE IF NOT EXISTS "Categories" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "name" VARCHAR(255) NOT NULL,
  "description" TEXT,
  "slug" VARCHAR(255) NOT NULL UNIQUE,
  "parentId" UUID REFERENCES "Categories"("id"),
  "imageUrl" VARCHAR(255),
  "displayOrder" INTEGER DEFAULT 0,
  "isActive" BOOLEAN DEFAULT TRUE,
  "metaTitle" VARCHAR(255),
  "metaDescription" TEXT,
  "metaKeywords" VARCHAR(255),
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create Products Table
CREATE TABLE IF NOT EXISTS "Products" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "name" VARCHAR(255) NOT NULL,
  "brand" VARCHAR(255) NOT NULL,
  "model" VARCHAR(255) NOT NULL,
  "description" TEXT,
  "sku" VARCHAR(50) NOT NULL UNIQUE,
  "regularPrice" DECIMAL(10, 2) NOT NULL,
  "salePrice" DECIMAL(10, 2),
  "cost" DECIMAL(10, 2) NOT NULL,
  "stockQuantity" INTEGER NOT NULL DEFAULT 0,
  "minOrderQuantity" INTEGER NOT NULL DEFAULT 1,
  "maxOrderQuantity" INTEGER,
  "weight" DECIMAL(10, 2),
  "dimensions" JSONB,
  "featured" BOOLEAN DEFAULT FALSE,
  "isActive" BOOLEAN DEFAULT TRUE,
  "slug" VARCHAR(255) NOT NULL UNIQUE,
  "metaTitle" VARCHAR(255),
  "metaDescription" TEXT,
  "metaKeywords" VARCHAR(255),
  "averageRating" FLOAT DEFAULT 0,
  "totalReviews" INTEGER DEFAULT 0,
  "totalSales" INTEGER DEFAULT 0,
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create Product Images Table
CREATE TABLE IF NOT EXISTS "ProductImages" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "productId" UUID NOT NULL REFERENCES "Products"("id") ON DELETE CASCADE,
  "imageUrl" VARCHAR(255) NOT NULL,
  "altText" VARCHAR(255),
  "displayOrder" INTEGER DEFAULT 0,
  "isPrimary" BOOLEAN DEFAULT FALSE,
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create Product Categories Junction Table
CREATE TABLE IF NOT EXISTS "ProductCategories" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "productId" UUID NOT NULL REFERENCES "Products"("id") ON DELETE CASCADE,
  "categoryId" UUID NOT NULL REFERENCES "Categories"("id") ON DELETE CASCADE,
  "isPrimary" BOOLEAN DEFAULT FALSE,
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  UNIQUE("productId", "categoryId")
);

-- Create Carts Table
CREATE TABLE IF NOT EXISTS "Carts" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "userId" UUID REFERENCES "Users"("id") ON DELETE CASCADE,
  "sessionId" VARCHAR(255),
  "status" VARCHAR(20) DEFAULT 'active',
  "itemCount" INTEGER DEFAULT 0,
  "totalAmount" DECIMAL(10, 2) DEFAULT 0,
  "expiresAt" TIMESTAMP,
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create Cart Items Table
CREATE TABLE IF NOT EXISTS "CartItems" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "cartId" UUID NOT NULL REFERENCES "Carts"("id") ON DELETE CASCADE,
  "productId" UUID NOT NULL REFERENCES "Products"("id"),
  "quantity" INTEGER NOT NULL DEFAULT 1,
  "price" DECIMAL(10, 2) NOT NULL,
  "totalPrice" DECIMAL(10, 2) NOT NULL,
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  UNIQUE("cartId", "productId")
);

-- Create Wishlists Table
CREATE TABLE IF NOT EXISTS "Wishlists" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "userId" UUID NOT NULL REFERENCES "Users"("id") ON DELETE CASCADE,
  "name" VARCHAR(255) DEFAULT 'Default Wishlist',
  "isPublic" BOOLEAN DEFAULT FALSE,
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create Wishlist Items Table
CREATE TABLE IF NOT EXISTS "WishlistItems" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "wishlistId" UUID NOT NULL REFERENCES "Wishlists"("id") ON DELETE CASCADE,
  "productId" UUID NOT NULL REFERENCES "Products"("id"),
  "addedAt" TIMESTAMP DEFAULT NOW(),
  "notes" TEXT,
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  UNIQUE("wishlistId", "productId")
);

-- Create Orders Table
CREATE TABLE IF NOT EXISTS "Orders" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "userId" UUID REFERENCES "Users"("id"),
  "orderNumber" VARCHAR(50) NOT NULL UNIQUE,
  "status" VARCHAR(20) DEFAULT 'pending',
  "totalAmount" DECIMAL(10, 2) NOT NULL,
  "subtotal" DECIMAL(10, 2) NOT NULL,
  "tax" DECIMAL(10, 2) NOT NULL DEFAULT 0,
  "shippingCost" DECIMAL(10, 2) NOT NULL DEFAULT 0,
  "discount" DECIMAL(10, 2) NOT NULL DEFAULT 0,
  "shippingAddress" JSONB NOT NULL,
  "billingAddress" JSONB NOT NULL,
  "paymentMethod" VARCHAR(50) NOT NULL,
  "paymentStatus" VARCHAR(20) DEFAULT 'pending',
  "paymentDetails" JSONB,
  "notes" TEXT,
  "shippedAt" TIMESTAMP,
  "deliveredAt" TIMESTAMP,
  "cancelledAt" TIMESTAMP,
  "cancelReason" VARCHAR(255),
  "guestEmail" VARCHAR(255),
  "guestPhone" VARCHAR(20),
  "estimatedDeliveryDate" TIMESTAMP,
  "trackingNumber" VARCHAR(100),
  "trackingUrl" VARCHAR(255),
  "couponCode" VARCHAR(50),
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create Order Items Table
CREATE TABLE IF NOT EXISTS "OrderItems" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "orderId" UUID NOT NULL REFERENCES "Orders"("id") ON DELETE CASCADE,
  "productId" UUID NOT NULL REFERENCES "Products"("id"),
  "quantity" INTEGER NOT NULL,
  "price" DECIMAL(10, 2) NOT NULL,
  "totalPrice" DECIMAL(10, 2) NOT NULL,
  "discount" DECIMAL(10, 2) NOT NULL DEFAULT 0,
  "tax" DECIMAL(10, 2) NOT NULL DEFAULT 0,
  "productName" VARCHAR(255) NOT NULL,
  "productSku" VARCHAR(50) NOT NULL,
  "productImage" VARCHAR(255),
  "productOptions" JSONB,
  "isReviewed" BOOLEAN DEFAULT FALSE,
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create Payments Table
CREATE TABLE IF NOT EXISTS "Payments" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "orderId" UUID NOT NULL REFERENCES "Orders"("id"),
  "amount" DECIMAL(10, 2) NOT NULL,
  "currency" VARCHAR(10) NOT NULL DEFAULT 'USD',
  "method" VARCHAR(50) NOT NULL,
  "status" VARCHAR(20) DEFAULT 'pending',
  "transactionId" VARCHAR(100),
  "paymentIntentId" VARCHAR(100),
  "paymentDetails" JSONB,
  "errorMessage" TEXT,
  "refundedAmount" DECIMAL(10, 2),
  "refundedAt" TIMESTAMP,
  "refundReason" VARCHAR(255),
  "billingDetails" JSONB,
  "cardBrand" VARCHAR(50),
  "cardLast4" VARCHAR(4),
  "receiptUrl" VARCHAR(255),
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create Reviews Table
CREATE TABLE IF NOT EXISTS "Reviews" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "productId" UUID NOT NULL REFERENCES "Products"("id") ON DELETE CASCADE,
  "userId" UUID NOT NULL REFERENCES "Users"("id"),
  "orderId" UUID REFERENCES "Orders"("id"),
  "orderItemId" UUID REFERENCES "OrderItems"("id"),
  "rating" INTEGER NOT NULL,
  "title" VARCHAR(255),
  "comment" TEXT NOT NULL,
  "isVerifiedPurchase" BOOLEAN DEFAULT FALSE,
  "isApproved" BOOLEAN DEFAULT TRUE,
  "isHelpful" INTEGER DEFAULT 0,
  "isNotHelpful" INTEGER DEFAULT 0,
  "images" JSONB,
  "adminResponse" TEXT,
  "adminResponseAt" TIMESTAMP,
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create Coupons Table
CREATE TABLE IF NOT EXISTS "Coupons" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "code" VARCHAR(50) NOT NULL UNIQUE,
  "type" VARCHAR(20) NOT NULL,
  "value" DECIMAL(10, 2) NOT NULL DEFAULT 0,
  "minPurchase" DECIMAL(10, 2),
  "maxDiscount" DECIMAL(10, 2),
  "startDate" TIMESTAMP NOT NULL,
  "endDate" TIMESTAMP NOT NULL,
  "isActive" BOOLEAN DEFAULT TRUE,
  "usageLimit" INTEGER,
  "usageCount" INTEGER DEFAULT 0,
  "perUserLimit" INTEGER,
  "description" TEXT,
  "applicableProducts" JSONB,
  "applicableCategories" JSONB,
  "excludedProducts" JSONB,
  "firstTimeOnly" BOOLEAN DEFAULT FALSE,
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create Coupon Usages Table
CREATE TABLE IF NOT EXISTS "CouponUsages" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "couponId" UUID NOT NULL REFERENCES "Coupons"("id") ON DELETE CASCADE,
  "userId" UUID NOT NULL REFERENCES "Users"("id"),
  "orderId" UUID NOT NULL REFERENCES "Orders"("id"),
  "discountAmount" DECIMAL(10, 2) NOT NULL,
  "usedAt" TIMESTAMP DEFAULT NOW(),
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  UNIQUE("couponId", "orderId")
);

-- Create Indexes
CREATE INDEX IF NOT EXISTS "idx_users_email" ON "Users"("email");
CREATE INDEX IF NOT EXISTS "idx_products_slug" ON "Products"("slug");
CREATE INDEX IF NOT EXISTS "idx_products_sku" ON "Products"("sku");
CREATE INDEX IF NOT EXISTS "idx_products_brand" ON "Products"("brand");
CREATE INDEX IF NOT EXISTS "idx_products_featured" ON "Products"("featured");
CREATE INDEX IF NOT EXISTS "idx_product_images_product_id" ON "ProductImages"("productId");
CREATE INDEX IF NOT EXISTS "idx_product_categories_product_id" ON "ProductCategories"("productId");
CREATE INDEX IF NOT EXISTS "idx_product_categories_category_id" ON "ProductCategories"("categoryId");
CREATE INDEX IF NOT EXISTS "idx_carts_user_id" ON "Carts"("userId");
CREATE INDEX IF NOT EXISTS "idx_carts_session_id" ON "Carts"("sessionId");
CREATE INDEX IF NOT EXISTS "idx_cart_items_cart_id" ON "CartItems"("cartId");
CREATE INDEX IF NOT EXISTS "idx_cart_items_product_id" ON "CartItems"("productId");
CREATE INDEX IF NOT EXISTS "idx_wishlists_user_id" ON "Wishlists"("userId");
CREATE INDEX IF NOT EXISTS "idx_wishlist_items_wishlist_id" ON "WishlistItems"("wishlistId");
CREATE INDEX IF NOT EXISTS "idx_wishlist_items_product_id" ON "WishlistItems"("productId");
CREATE INDEX IF NOT EXISTS "idx_orders_user_id" ON "Orders"("userId");
CREATE INDEX IF NOT EXISTS "idx_orders_order_number" ON "Orders"("orderNumber");
CREATE INDEX IF NOT EXISTS "idx_orders_status" ON "Orders"("status");
CREATE INDEX IF NOT EXISTS "idx_orders_payment_status" ON "Orders"("paymentStatus");
CREATE INDEX IF NOT EXISTS "idx_order_items_order_id" ON "OrderItems"("orderId");
CREATE INDEX IF NOT EXISTS "idx_order_items_product_id" ON "OrderItems"("productId");
CREATE INDEX IF NOT EXISTS "idx_payments_order_id" ON "Payments"("orderId");
CREATE INDEX IF NOT EXISTS "idx_payments_transaction_id" ON "Payments"("transactionId");
CREATE INDEX IF NOT EXISTS "idx_payments_status" ON "Payments"("status");
CREATE INDEX IF NOT EXISTS "idx_reviews_product_id" ON "Reviews"("productId");
CREATE INDEX IF NOT EXISTS "idx_reviews_user_id" ON "Reviews"("userId");
CREATE INDEX IF NOT EXISTS "idx_reviews_order_id" ON "Reviews"("orderId");
CREATE INDEX IF NOT EXISTS "idx_coupons_code" ON "Coupons"("code");
CREATE INDEX IF NOT EXISTS "idx_coupons_is_active" ON "Coupons"("isActive");
CREATE INDEX IF NOT EXISTS "idx_coupon_usages_coupon_id" ON "CouponUsages"("couponId");
CREATE INDEX IF NOT EXISTS "idx_coupon_usages_user_id" ON "CouponUsages"("userId");
CREATE INDEX IF NOT EXISTS "idx_coupon_usages_order_id" ON "CouponUsages"("orderId");
