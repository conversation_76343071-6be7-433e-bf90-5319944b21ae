const sequelize = require('../../config/database');

// Import models
const User = require('./User');
const Product = require('./Product');
const Category = require('./Category');
const ProductImage = require('./ProductImage');
const ProductCategory = require('./ProductCategory');
const Cart = require('./Cart');
const CartItem = require('./CartItem');
const Wishlist = require('./Wishlist');
const WishlistItem = require('./WishlistItem');
const Order = require('./Order');
const OrderItem = require('./OrderItem');
const Payment = require('./Payment');
const Review = require('./Review');
const Coupon = require('./Coupon');
const CouponUsage = require('./CouponUsage');
const ImportJob = require('./ImportJob');
const ImportJobLog = require('./ImportJobLog');
const B2CTransaction = require('./B2CTransaction');

// Define associations
Product.hasMany(ProductImage, { foreignKey: 'productId', as: 'images' });
ProductImage.belongsTo(Product, { foreignKey: 'productId' });

// Many-to-many relationship between Product and Category
Product.belongsToMany(Category, {
  through: ProductCategory,
  foreignKey: 'productId',
  otherKey: 'categoryId',
  as: 'categories'
});

Category.belongsToMany(Product, {
  through: ProductCategory,
  foreignKey: 'categoryId',
  otherKey: 'productId',
  as: 'products'
});

// Cart associations
Cart.belongsTo(User, { foreignKey: 'userId' });
User.hasMany(Cart, { foreignKey: 'userId' });

Cart.hasMany(CartItem, { foreignKey: 'cartId', as: 'items' });
CartItem.belongsTo(Cart, { foreignKey: 'cartId' });

CartItem.belongsTo(Product, { foreignKey: 'productId' });
Product.hasMany(CartItem, { foreignKey: 'productId' });

// Wishlist associations
Wishlist.belongsTo(User, { foreignKey: 'userId' });
User.hasMany(Wishlist, { foreignKey: 'userId' });

Wishlist.hasMany(WishlistItem, { foreignKey: 'wishlistId', as: 'items' });
WishlistItem.belongsTo(Wishlist, { foreignKey: 'wishlistId' });

WishlistItem.belongsTo(Product, { foreignKey: 'productId' });
Product.hasMany(WishlistItem, { foreignKey: 'productId' });

// Order associations
Order.belongsTo(User, { foreignKey: 'userId' });
User.hasMany(Order, { foreignKey: 'userId' });

Order.hasMany(OrderItem, { foreignKey: 'orderId', as: 'items' });
OrderItem.belongsTo(Order, { foreignKey: 'orderId' });

OrderItem.belongsTo(Product, { foreignKey: 'productId' });
Product.hasMany(OrderItem, { foreignKey: 'productId' });

// Payment associations
Payment.belongsTo(Order, { foreignKey: 'orderId' });
Order.hasMany(Payment, { foreignKey: 'orderId', as: 'payments' });

// Review associations
Review.belongsTo(User, { foreignKey: 'userId' });
User.hasMany(Review, { foreignKey: 'userId', as: 'userReviews' });

Review.belongsTo(Product, { foreignKey: 'productId' });
Product.hasMany(Review, { foreignKey: 'productId', as: 'reviews' });

Review.belongsTo(Order, { foreignKey: 'orderId' });
Order.hasMany(Review, { foreignKey: 'orderId' });

Review.belongsTo(OrderItem, { foreignKey: 'orderItemId' });
OrderItem.hasOne(Review, { foreignKey: 'orderItemId' });

// Coupon associations
Coupon.hasMany(CouponUsage, { foreignKey: 'couponId', as: 'usages' });
CouponUsage.belongsTo(Coupon, { foreignKey: 'couponId' });

CouponUsage.belongsTo(User, { foreignKey: 'userId' });
User.hasMany(CouponUsage, { foreignKey: 'userId' });

CouponUsage.belongsTo(Order, { foreignKey: 'orderId' });
Order.hasMany(CouponUsage, { foreignKey: 'orderId', as: 'coupons' });

// Import Job associations
ImportJob.hasMany(ImportJobLog, { foreignKey: 'importJobId', as: 'logs' });
ImportJobLog.belongsTo(ImportJob, { foreignKey: 'importJobId' });

// B2C Transaction associations
B2CTransaction.belongsTo(Order, { foreignKey: 'orderId', as: 'order' });
Order.hasMany(B2CTransaction, { foreignKey: 'orderId', as: 'b2cTransactions' });

// Export models and sequelize instance
module.exports = {
  sequelize,
  User,
  Product,
  Category,
  ProductImage,
  ProductCategory,
  Cart,
  CartItem,
  Wishlist,
  WishlistItem,
  Order,
  OrderItem,
  Payment,
  Review,
  Coupon,
  CouponUsage,
  ImportJob,
  ImportJobLog,
  B2CTransaction
};
