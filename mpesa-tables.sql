-- M-Pesa Tables Creation Script
-- Run this in pgAdmin to create the missing M-Pesa tables

-- Create ENUM types first
CREATE TYPE "enum_mpesa_transactions_transactionType" AS ENUM (
  'STK_PUSH',
  'C2B', 
  'B2C_REFUND',
  'B2B',
  'REVERSAL'
);

CREATE TYPE "enum_mpesa_transactions_status" AS ENUM (
  'PENDING',
  'SUCCESS',
  'FAILED', 
  'CANCELLED',
  'TIMEOUT'
);

CREATE TYPE "enum_mpesa_transactions_environment" AS ENUM (
  'sandbox',
  'production'
);

CREATE TYPE "enum_mpesa_config_environment" AS ENUM (
  'sandbox',
  'production'
);

-- Create mpesa_transactions table
CREATE TABLE "mpesa_transactions" (
  "id" UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  "orderId" UUID NOT NULL REFERENCES "Orders"("id") ON UPDATE CASCADE ON DELETE RESTRICT,
  "merchantRequestId" VARCHAR(255),
  "checkoutRequestId" VARCHAR(255) UNIQUE,
  "mpesaReceiptNumber" VARCHAR(255) UNIQUE,
  "transactionDate" TIMESTAMP WITH TIME ZONE,
  "phoneNumber" VARCHAR(15) NOT NULL,
  "amount" DECIMAL(10,2) NOT NULL,
  "transactionType" "enum_mpesa_transactions_transactionType" NOT NULL DEFAULT 'STK_PUSH',
  "status" "enum_mpesa_transactions_status" NOT NULL DEFAULT 'PENDING',
  "resultCode" INTEGER,
  "resultDesc" TEXT,
  "callbackData" JSONB,
  "conversationId" VARCHAR(255),
  "originatorConversationId" VARCHAR(255),
  "accountReference" VARCHAR(255),
  "transactionDesc" VARCHAR(255),
  "initiatorName" VARCHAR(255),
  "remarks" TEXT,
  "retryCount" INTEGER DEFAULT 0,
  "lastRetryAt" TIMESTAMP WITH TIME ZONE,
  "processedAt" TIMESTAMP WITH TIME ZONE,
  "isReconciled" BOOLEAN DEFAULT false,
  "reconciledAt" TIMESTAMP WITH TIME ZONE,
  "environment" "enum_mpesa_transactions_environment" NOT NULL DEFAULT 'sandbox',
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create mpesa_config table
CREATE TABLE "mpesa_config" (
  "id" SERIAL PRIMARY KEY,
  "environment" "enum_mpesa_config_environment" NOT NULL,
  "consumerKey" VARCHAR(255) NOT NULL,
  "consumerSecret" VARCHAR(255) NOT NULL,
  "businessShortCode" VARCHAR(10) NOT NULL,
  "lipaNameMpesaOnlinePasskey" TEXT NOT NULL,
  "callbackUrl" VARCHAR(255) NOT NULL,
  "confirmationUrl" VARCHAR(255),
  "validationUrl" VARCHAR(255),
  "initiatorName" VARCHAR(255),
  "securityCredential" TEXT,
  "isActive" BOOLEAN DEFAULT false,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for mpesa_transactions
CREATE INDEX "idx_mpesa_transactions_order_id" ON "mpesa_transactions" ("orderId");
CREATE UNIQUE INDEX "idx_mpesa_transactions_checkout_request_id" ON "mpesa_transactions" ("checkoutRequestId") WHERE "checkoutRequestId" IS NOT NULL;
CREATE UNIQUE INDEX "idx_mpesa_transactions_receipt_number" ON "mpesa_transactions" ("mpesaReceiptNumber") WHERE "mpesaReceiptNumber" IS NOT NULL;
CREATE INDEX "idx_mpesa_transactions_phone_number" ON "mpesa_transactions" ("phoneNumber");
CREATE INDEX "idx_mpesa_transactions_status" ON "mpesa_transactions" ("status");
CREATE INDEX "idx_mpesa_transactions_type" ON "mpesa_transactions" ("transactionType");
CREATE INDEX "idx_mpesa_transactions_date" ON "mpesa_transactions" ("transactionDate");
CREATE INDEX "idx_mpesa_transactions_created_at" ON "mpesa_transactions" ("createdAt");
CREATE INDEX "idx_mpesa_transactions_environment" ON "mpesa_transactions" ("environment");

-- Create indexes for mpesa_config
CREATE INDEX "idx_mpesa_config_environment" ON "mpesa_config" ("environment");
CREATE INDEX "idx_mpesa_config_is_active" ON "mpesa_config" ("isActive");
CREATE UNIQUE INDEX "idx_mpesa_config_environment_active" ON "mpesa_config" ("environment", "isActive") WHERE "isActive" = true;

-- Insert SequelizeMeta record to mark migration as completed
INSERT INTO "SequelizeMeta" ("name") VALUES ('20241201000000-create-mpesa-tables.js');

COMMENT ON TABLE "mpesa_transactions" IS 'M-Pesa transaction records';
COMMENT ON TABLE "mpesa_config" IS 'M-Pesa configuration settings';

SELECT 'M-Pesa tables created successfully!' AS result;
