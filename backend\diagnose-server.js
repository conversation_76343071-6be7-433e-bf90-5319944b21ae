const net = require('net');

console.log('🔍 Server Diagnostics\n');

// Check if ports are in use
function checkPort(port) {
  return new Promise((resolve) => {
    const server = net.createServer();
    
    server.listen(port, () => {
      server.close();
      console.log(`✅ Port ${port}: Available`);
      resolve(false); // Port is available (not in use)
    });
    
    server.on('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        console.log(`🟡 Port ${port}: In use (server might be running here)`);
        resolve(true); // Port is in use
      } else {
        console.log(`❌ Port ${port}: Error - ${err.message}`);
        resolve(false);
      }
    });
  });
}

async function diagnose() {
  console.log('Checking common ports...\n');
  
  const ports = [3000, 5000, 8000, 3001, 5001];
  const portsInUse = [];
  
  for (const port of ports) {
    const inUse = await checkPort(port);
    if (inUse) {
      portsInUse.push(port);
    }
  }
  
  console.log('\n📊 Summary:');
  if (portsInUse.length > 0) {
    console.log(`🟢 Servers detected on ports: ${portsInUse.join(', ')}`);
    console.log('\n💡 Try accessing your frontend and check which port it\'s calling in the browser network tab');
  } else {
    console.log('🔴 No servers detected on common ports');
    console.log('\n💡 Make sure your server is running with: npm start or npm run dev');
  }
  
  console.log('\n🔧 Next steps:');
  console.log('1. Check your server terminal for any error messages');
  console.log('2. Verify the PORT in your .env file');
  console.log('3. Make sure your frontend is pointing to the correct API URL');
}

diagnose();
