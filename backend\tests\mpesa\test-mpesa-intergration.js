/**
 * Test script to debug M-Pesa payment initiation
 * Run this with: node test-mpesa.js
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';
const TEST_SESSION_ID = 'test-session-mpesa';

// Test M-Pesa payment data
const testMpesaPayment = {
  orderId: "4cb4c5a0-24c5-44b6-a052-90f231210224", // Use the order ID from previous test
  phoneNumber: "************",
  amount: 74.88, // Amount from cart (should be properly formatted)
  accountReference: "G20-4cb4c5a0", // Short reference
  transactionDesc: "G20Shop Order Payment"
};

async function testMpesaPayment() {
  try {
    console.log('💳 Testing M-Pesa payment initiation...');
    console.log('📤 Sending payment data:', JSON.stringify(testMpesaPayment, null, 2));
    
    const response = await axios.post(
      `${API_BASE_URL}/mpesa/initiate-payment`,
      testMpesaPayment,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-session-id': TEST_SESSION_ID
        }
      }
    );
    
    console.log('✅ M-Pesa payment initiated successfully!');
    console.log('📥 Response:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('❌ M-Pesa payment initiation failed!');
    
    if (error.response) {
      console.log('📊 Status:', error.response.status);
      console.log('📋 Error data:', JSON.stringify(error.response.data, null, 2));
      
      // Check for validation errors
      if (error.response.data.errors) {
        console.log('\n🔍 Validation errors:');
        error.response.data.errors.forEach((err, index) => {
          console.log(`  ${index + 1}. Field: ${err.field || 'unknown'}`);
          console.log(`     Message: ${err.message}`);
          console.log(`     Value: ${err.value || 'undefined'}`);
        });
      }
    } else if (error.request) {
      console.log('🌐 Network error - no response received');
    } else {
      console.log('⚠️ Error:', error.message);
    }
  }
}

// Test server connectivity first
async function testServerConnection() {
  try {
    console.log('🔗 Testing server connection...');
    const response = await axios.get(`${API_BASE_URL.replace('/api', '')}/health`);
    console.log('✅ Server is responding:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Server connection failed:', error.message);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting M-Pesa payment tests...\n');
  
  // Test server connection
  const serverOk = await testServerConnection();
  if (!serverOk) {
    console.log('❌ Cannot proceed - server is not responding');
    return;
  }
  
  console.log(''); // Empty line for readability
  
  // Test M-Pesa payment
  await testMpesaPayment();
}

// Run the tests
runTests().catch(console.error);
