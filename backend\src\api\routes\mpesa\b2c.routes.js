const express = require('express');
const router = express.Router();
const b2cController = require('../../controllers/mpesa/b2c.controller');
const { authenticateWithClerk, authenticateAdmin } = require('../../middlewares/clerk-auth.middleware');
const { validateB2CPayment, validateB2CRefund, validateB2CSalary, validateB2CPromotion } = require('../../validators/mpesa/b2c.validator');
const { paymentLimiter } = require('../../middlewares/rateLimiter.middleware');

/**
 * M-Pesa B2C (Business to Customer) Routes
 * Handles all B2C payment operations including refunds, salary payments, and promotional payments
 */

// Rate limiting for B2C endpoints
const rateLimit = require('express-rate-limit');

const b2cRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10, // Limit each IP to 10 B2C requests per windowMs
  skipSuccessfulRequests: false,
  message: {
    success: false,
    message: 'Too many B2C requests, please try again later.',
    error: 'Rate limit exceeded'
  }
});

const callbackRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // Allow more callbacks from Safaricom
  skipSuccessfulRequests: true,
  message: {
    success: false,
    message: 'Too many callback requests, please try again later.',
    error: 'Rate limit exceeded'
  }
});

// ============================================================================
// ADMIN ENDPOINTS (Require admin authentication)
// ============================================================================

/**
 * @route   POST /api/mpesa/b2c/payment
 * @desc    Process general B2C payment (Admin only)
 * @access  Private (Admin only)
 * @body    { phoneNumber, amount, transactionType, remarks?, occasion?, orderId?, metadata? }
 */
router.post(
  '/payment',
  b2cRateLimit,
  authenticateAdmin,
  validateB2CPayment,
  b2cController.processPayment
);

/**
 * @route   POST /api/mpesa/b2c/refund
 * @desc    Process refund to customer (Admin only)
 * @access  Private (Admin only)
 * @body    { orderId, phoneNumber, amount, reason? }
 */
router.post(
  '/refund',
  b2cRateLimit,
  authenticateAdmin,
  validateB2CRefund,
  b2cController.processRefund
);

/**
 * @route   POST /api/mpesa/b2c/salary
 * @desc    Process salary payment (Admin only)
 * @access  Private (Admin only)
 * @body    { employeePhone, amount, employeeId, payrollPeriod }
 */
router.post(
  '/salary',
  b2cRateLimit,
  authenticateAdmin,
  validateB2CSalary,
  b2cController.processSalaryPayment
);

/**
 * @route   POST /api/mpesa/b2c/promotion
 * @desc    Process promotional payment (Admin only)
 * @access  Private (Admin only)
 * @body    { customerPhone, amount, promotionCode, campaignId }
 */
router.post(
  '/promotion',
  b2cRateLimit,
  authenticateAdmin,
  validateB2CPromotion,
  b2cController.processPromotionalPayment
);

/**
 * @route   GET /api/mpesa/b2c/transactions
 * @desc    Get B2C transaction history with filtering (Admin only)
 * @access  Private (Admin only)
 * @query   { status?, transactionType?, phoneNumber?, startDate?, endDate?, page?, limit? }
 */
router.get(
  '/transactions',
  authenticateAdmin,
  b2cController.getTransactionHistory
);

/**
 * @route   GET /api/mpesa/b2c/transaction/:id
 * @desc    Get specific B2C transaction status (Admin only)
 * @access  Private (Admin only)
 */
router.get(
  '/transaction/:id',
  authenticateAdmin,
  b2cController.getTransactionStatus
);

/**
 * @route   GET /api/mpesa/b2c/stats
 * @desc    Get B2C transaction statistics (Admin only)
 * @access  Private (Admin only)
 * @query   { startDate?, endDate? }
 */
router.get(
  '/stats',
  authenticateAdmin,
  async (req, res) => {
    try {
      const { B2CTransaction } = require('../../../db/models');
      const { startDate, endDate } = req.query;

      const stats = await B2CTransaction.getTransactionStats(
        startDate ? new Date(startDate) : null,
        endDate ? new Date(endDate) : null
      );

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('❌ B2C stats query failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve B2C statistics'
      });
    }
  }
);

// ============================================================================
// USER ENDPOINTS (Require user authentication)
// ============================================================================

/**
 * @route   GET /api/mpesa/b2c/my-transactions
 * @desc    Get user's B2C transactions (refunds, etc.)
 * @access  Private (Authenticated users)
 * @query   { status?, transactionType?, page?, limit? }
 */
router.get(
  '/my-transactions',
  authenticateWithClerk,
  async (req, res) => {
    try {
      const { B2CTransaction } = require('../../../db/models');
      const userId = req.user.id;
      const { status, transactionType, page = 1, limit = 20 } = req.query;

      const where = { userId };
      if (status) where.status = status;
      if (transactionType) where.transactionType = transactionType;

      const offset = (page - 1) * limit;

      const transactions = await B2CTransaction.findAndCountAll({
        where,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['initiatedAt', 'DESC']],
        attributes: [
          'id', 'conversationId', 'amount', 'transactionType', 'status',
          'remarks', 'mpesaReceiptNumber', 'initiatedAt', 'completedAt'
        ]
      });

      res.json({
        success: true,
        data: {
          transactions: transactions.rows,
          pagination: {
            total: transactions.count,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(transactions.count / limit)
          }
        }
      });
    } catch (error) {
      console.error('❌ User B2C transactions query failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve your transactions'
      });
    }
  }
);

/**
 * @route   GET /api/mpesa/b2c/my-transaction/:id
 * @desc    Get specific user's B2C transaction
 * @access  Private (Authenticated users - own transactions only)
 */
router.get(
  '/my-transaction/:id',
  authenticateWithClerk,
  async (req, res) => {
    try {
      const { B2CTransaction } = require('../../../db/models');
      const userId = req.user.id;
      const { id } = req.params;

      const transaction = await B2CTransaction.findOne({
        where: { id, userId },
        attributes: [
          'id', 'conversationId', 'amount', 'transactionType', 'status',
          'remarks', 'occasion', 'mpesaReceiptNumber', 'resultDescription',
          'initiatedAt', 'completedAt'
        ]
      });

      if (!transaction) {
        return res.status(404).json({
          success: false,
          message: 'Transaction not found'
        });
      }

      res.json({
        success: true,
        data: transaction
      });
    } catch (error) {
      console.error('❌ User B2C transaction query failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve transaction'
      });
    }
  }
);

/**
 * @route   GET /api/mpesa/b2c/my-refunds
 * @desc    Get user's refund transactions
 * @access  Private (Authenticated users)
 * @query   { status?, page?, limit? }
 */
router.get(
  '/my-refunds',
  authenticateWithClerk,
  async (req, res) => {
    try {
      const { B2CTransaction } = require('../../../db/models');
      const userId = req.user.id;
      const { status, page = 1, limit = 20 } = req.query;

      const where = {
        userId,
        transactionType: 'REFUND'
      };
      if (status) where.status = status;

      const offset = (page - 1) * limit;

      const refunds = await B2CTransaction.findAndCountAll({
        where,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['initiatedAt', 'DESC']],
        attributes: [
          'id', 'conversationId', 'amount', 'status', 'remarks',
          'mpesaReceiptNumber', 'initiatedAt', 'completedAt', 'orderId'
        ]
      });

      res.json({
        success: true,
        data: {
          refunds: refunds.rows,
          pagination: {
            total: refunds.count,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(refunds.count / limit)
          }
        }
      });
    } catch (error) {
      console.error('❌ User refunds query failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve your refunds'
      });
    }
  }
);

/**
 * @route   GET /api/mpesa/b2c/user/transaction-summary
 * @desc    Get user's B2C transaction summary
 * @access  Private (Authenticated users)
 */
router.get(
  '/user/transaction-summary',
  authenticateWithClerk,
  async (req, res) => {
    try {
      const { B2CTransaction } = require('../../../db/models');
      const { Op, fn, col } = require('sequelize');
      const userId = req.user.id;

      // Get summary statistics
      const summary = await B2CTransaction.findAll({
        where: { userId },
        attributes: [
          'transactionType',
          'status',
          [fn('COUNT', col('id')), 'count'],
          [fn('SUM', col('amount')), 'totalAmount']
        ],
        group: ['transactionType', 'status'],
        raw: true
      });

      // Get recent transactions
      const recentTransactions = await B2CTransaction.findAll({
        where: { userId },
        limit: 5,
        order: [['initiatedAt', 'DESC']],
        attributes: [
          'id', 'conversationId', 'amount', 'transactionType', 'status',
          'initiatedAt', 'mpesaReceiptNumber'
        ]
      });

      res.json({
        success: true,
        data: {
          summary,
          recentTransactions
        }
      });
    } catch (error) {
      console.error('❌ User transaction summary query failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve transaction summary'
      });
    }
  }
);

// ============================================================================
// WEBHOOK ENDPOINTS (No authentication - called by Safaricom)
// ============================================================================

/**
 * @route   POST /api/mpesa/b2c/callback/result/:transactionId
 * @desc    Handle B2C result callback from Safaricom
 * @access  Public (Safaricom webhook)
 */
router.post(
  '/callback/result/:transactionId',
  callbackRateLimit,
  b2cController.handleResultCallback
);

/**
 * @route   POST /api/mpesa/b2c/callback/timeout/:transactionId
 * @desc    Handle B2C timeout callback from Safaricom
 * @access  Public (Safaricom webhook)
 */
router.post(
  '/callback/timeout/:transactionId',
  callbackRateLimit,
  b2cController.handleTimeoutCallback
);

// ============================================================================
// UTILITY ENDPOINTS
// ============================================================================

/**
 * @route   GET /api/mpesa/b2c/health
 * @desc    Health check for B2C service
 * @access  Public
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'M-Pesa B2C service is healthy',
    timestamp: new Date().toISOString(),
    environment: process.env.MPESA_ENVIRONMENT || 'sandbox',
    features: {
      refunds: true,
      salaryPayments: true,
      promotionalPayments: true,
      generalPayments: true
    }
  });
});

/**
 * @route   GET /api/mpesa/b2c/transaction-types
 * @desc    Get available B2C transaction types
 * @access  Public
 */
router.get('/transaction-types', (req, res) => {
  res.json({
    success: true,
    data: {
      transactionTypes: [
        {
          code: 'REFUND',
          name: 'Refund',
          description: 'Refund payment to customer',
          commandId: 'BusinessPayment'
        },
        {
          code: 'SALARY',
          name: 'Salary Payment',
          description: 'Salary payment to employee',
          commandId: 'SalaryPayment'
        },
        {
          code: 'PROMOTION',
          name: 'Promotional Payment',
          description: 'Promotional payment to customer',
          commandId: 'PromotionPayment'
        },
        {
          code: 'GENERAL',
          name: 'General Payment',
          description: 'General business payment',
          commandId: 'BusinessPayment'
        }
      ]
    }
  });
});

module.exports = router;
