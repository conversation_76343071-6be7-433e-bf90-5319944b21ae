# Dependencies
node_modules
/.pnp
.pnp.js

# Testing
/coverage

# Production
/build
/dist
/dist-ssr
*.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Backend specific
/backend/uploads/*
!/backend/uploads/.gitkeep
/backend/logs/*
!/backend/logs/.gitkeep

# Frontend specific
/frontend/.cache
/frontend/public/build

# Misc
.cache
.temp
.tmp
*.bak
*.tmp
*~