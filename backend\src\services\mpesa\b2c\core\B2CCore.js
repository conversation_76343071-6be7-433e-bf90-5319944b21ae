/**
 * M-Pesa B2C Core Service
 * Core functionality for B2C operations
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-01-15
 */

const axios = require('axios');
const moment = require('moment');
const logger = require('../../../../utils/logger');
const B2CConfig = require('../config/B2CConfig');
const B2CUtils = require('../utils/B2CUtils');
const B2CValidator = require('../validators/B2CValidator');

class B2CCore {
  constructor() {
    this.config = new B2CConfig();
    this.accessToken = null;
    this.tokenExpiry = null;
  }

  /**
   * Generate access token for M-Pesa API
   * @returns {Promise<string>} Access token
   */
  async generateAccessToken() {
    try {
      // Check if we have a valid cached token
      if (this.accessToken && this.tokenExpiry && moment().isBefore(this.tokenExpiry)) {
        return this.accessToken;
      }

      const auth = Buffer.from(`${this.config.consumerKey}:${this.config.consumerSecret}`).toString('base64');
      
      const response = await axios.get(
        `${this.config.baseUrl}/oauth/v1/generate?grant_type=client_credentials`,
        {
          headers: {
            'Authorization': `Basic ${auth}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      this.accessToken = response.data.access_token;
      // Set expiry to 5 minutes before actual expiry for safety
      this.tokenExpiry = moment().add(response.data.expires_in - 300, 'seconds');
      
      logger.info('✅ M-Pesa B2C access token generated successfully', {
        expiresIn: response.data.expires_in,
        environment: this.config.environment
      });

      return this.accessToken;
    } catch (error) {
      logger.error('❌ Failed to generate M-Pesa B2C access token:', {
        error: error.response?.data || error.message,
        environment: this.config.environment
      });
      throw new Error('Failed to authenticate with M-Pesa API');
    }
  }

  /**
   * Make B2C payment request to M-Pesa API
   * @param {Object} paymentData - Payment data
   * @returns {Promise<Object>} API response
   */
  async makeB2CRequest(paymentData) {
    try {
      // Validate payment data
      const validation = B2CValidator.validatePaymentData(paymentData);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      const accessToken = await this.generateAccessToken();
      
      const requestBody = {
        InitiatorName: this.config.initiatorName,
        SecurityCredential: this.config.securityCredential,
        CommandID: paymentData.commandId,
        Amount: Math.round(paymentData.amount),
        PartyA: this.config.businessShortCode,
        PartyB: B2CUtils.formatPhoneNumber(paymentData.phoneNumber),
        Remarks: paymentData.remarks || 'G20Shop Payment',
        QueueTimeOutURL: `${this.config.queueTimeoutUrl}/${paymentData.transactionId}`,
        ResultURL: `${this.config.resultUrl}/${paymentData.transactionId}`,
        Occasion: paymentData.occasion || 'Payment Processing'
      };

      logger.info('🚀 Making B2C payment request:', {
        transactionId: paymentData.transactionId,
        phone: B2CUtils.maskPhoneNumber(paymentData.phoneNumber),
        amount: paymentData.amount,
        commandId: paymentData.commandId,
        environment: this.config.environment
      });

      const response = await axios.post(
        `${this.config.baseUrl}/mpesa/b2c/v1/paymentrequest`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          timeout: 60000 // 60 seconds timeout
        }
      );

      logger.info('✅ B2C payment request successful:', {
        transactionId: paymentData.transactionId,
        conversationId: response.data.ConversationID,
        responseCode: response.data.ResponseCode,
        responseDescription: response.data.ResponseDescription
      });

      return {
        success: true,
        data: {
          conversationId: response.data.ConversationID,
          originatorConversationId: response.data.OriginatorConversationID,
          responseCode: response.data.ResponseCode,
          responseDescription: response.data.ResponseDescription
        }
      };

    } catch (error) {
      logger.error('❌ B2C payment request failed:', {
        transactionId: paymentData.transactionId,
        error: error.response?.data || error.message,
        environment: this.config.environment
      });

      return {
        success: false,
        error: error.response?.data || error.message,
        message: 'Failed to process B2C payment request'
      };
    }
  }

  /**
   * Query account balance
   * @param {string} identifierType - Identifier type (1=MSISDN, 2=Till, 4=Shortcode)
   * @param {string} identifier - Account identifier
   * @returns {Promise<Object>} Balance information
   */
  async queryAccountBalance(identifierType = '4', identifier = null) {
    try {
      const accessToken = await this.generateAccessToken();
      const accountIdentifier = identifier || this.config.businessShortCode;
      
      const requestBody = {
        Initiator: this.config.initiatorName,
        SecurityCredential: this.config.securityCredential,
        CommandID: 'AccountBalance',
        PartyA: this.config.businessShortCode,
        IdentifierType: identifierType,
        Remarks: 'Account balance query',
        QueueTimeOutURL: this.config.queueTimeoutUrl,
        ResultURL: this.config.resultUrl
      };

      const response = await axios.post(
        `${this.config.baseUrl}/mpesa/accountbalance/v1/query`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      logger.info('✅ Account balance query successful:', {
        conversationId: response.data.ConversationID,
        responseCode: response.data.ResponseCode
      });

      return {
        success: true,
        data: response.data
      };

    } catch (error) {
      logger.error('❌ Account balance query failed:', {
        error: error.response?.data || error.message
      });

      return {
        success: false,
        error: error.response?.data || error.message,
        message: 'Failed to query account balance'
      };
    }
  }

  /**
   * Query transaction status
   * @param {string} transactionId - Transaction ID to query
   * @param {string} originatorConversationId - Originator conversation ID
   * @returns {Promise<Object>} Transaction status
   */
  async queryTransactionStatus(transactionId, originatorConversationId) {
    try {
      const accessToken = await this.generateAccessToken();
      
      const requestBody = {
        Initiator: this.config.initiatorName,
        SecurityCredential: this.config.securityCredential,
        CommandID: 'TransactionStatusQuery',
        TransactionID: transactionId,
        OriginatorConversationID: originatorConversationId,
        PartyA: this.config.businessShortCode,
        IdentifierType: '4',
        ResultURL: this.config.resultUrl,
        QueueTimeOutURL: this.config.queueTimeoutUrl,
        Remarks: 'Transaction status query',
        Occasion: 'Status Check'
      };

      const response = await axios.post(
        `${this.config.baseUrl}/mpesa/transactionstatus/v1/query`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      logger.info('✅ Transaction status query successful:', {
        transactionId,
        conversationId: response.data.ConversationID,
        responseCode: response.data.ResponseCode
      });

      return {
        success: true,
        data: response.data
      };

    } catch (error) {
      logger.error('❌ Transaction status query failed:', {
        transactionId,
        error: error.response?.data || error.message
      });

      return {
        success: false,
        error: error.response?.data || error.message,
        message: 'Failed to query transaction status'
      };
    }
  }

  /**
   * Reverse a transaction
   * @param {Object} reverseData - Reverse transaction data
   * @returns {Promise<Object>} Reverse response
   */
  async reverseTransaction(reverseData) {
    try {
      const accessToken = await this.generateAccessToken();
      
      const requestBody = {
        Initiator: this.config.initiatorName,
        SecurityCredential: this.config.securityCredential,
        CommandID: 'TransactionReversal',
        TransactionID: reverseData.transactionId,
        Amount: Math.round(reverseData.amount),
        ReceiverParty: this.config.businessShortCode,
        RecieverIdentifierType: '11',
        ResultURL: this.config.resultUrl,
        QueueTimeOutURL: this.config.queueTimeoutUrl,
        Remarks: reverseData.remarks || 'Transaction reversal',
        Occasion: reverseData.occasion || 'Reversal'
      };

      const response = await axios.post(
        `${this.config.baseUrl}/mpesa/reversal/v1/request`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      logger.info('✅ Transaction reversal successful:', {
        transactionId: reverseData.transactionId,
        conversationId: response.data.ConversationID,
        responseCode: response.data.ResponseCode
      });

      return {
        success: true,
        data: response.data
      };

    } catch (error) {
      logger.error('❌ Transaction reversal failed:', {
        transactionId: reverseData.transactionId,
        error: error.response?.data || error.message
      });

      return {
        success: false,
        error: error.response?.data || error.message,
        message: 'Failed to reverse transaction'
      };
    }
  }

  /**
   * Get service health status
   * @returns {Object} Health status
   */
  getHealthStatus() {
    return {
      service: 'M-Pesa B2C Core',
      status: 'healthy',
      environment: this.config.environment,
      timestamp: new Date().toISOString(),
      features: {
        payments: true,
        refunds: true,
        salaryPayments: true,
        promotionalPayments: true,
        accountBalance: true,
        transactionStatus: true,
        reversal: true
      },
      limits: {
        minAmount: 1,
        maxAmount: 150000,
        currency: 'KES'
      }
    };
  }
}

module.exports = B2CCore;
