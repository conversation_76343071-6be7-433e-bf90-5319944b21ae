/**
 * M-Pesa B2C Controller
 * Enhanced controller for B2C operations with improved structure
 *
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 2024-01-15
 */

const B2CService = require('../../../services/mpesa/b2c/B2CService');
const { B2CTransaction } = require('../../../db/models');
const logger = require('../../../utils/logger');
const B2CUtils = require('../../../services/mpesa/b2c/utils/B2CUtils');

/**
 * M-Pesa B2C Controller
 * Handles HTTP requests for B2C operations
 */
class B2CController {

  /**
   * Process general B2C payment
   * @route POST /api/mpesa/b2c/payment
   */
  async processPayment(req, res) {
    try {
      const {
        phoneNumber,
        amount,
        transactionType,
        remarks,
        occasion,
        orderId,
        metadata
      } = req.body;

      const userId = req.user?.id;

      const result = await B2CService.processB2CPayment({
        phoneNumber,
        amount,
        transactionType,
        remarks,
        occasion,
        orderId,
        userId,
        metadata
      });

      if (result.success) {
        res.status(200).json({
          success: true,
          message: result.message,
          data: result.data
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.message,
          errors: result.errors,
          error: result.error
        });
      }
    } catch (error) {
      logger.error('❌ B2C payment controller error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Process refund
   * @route POST /api/mpesa/b2c/refund
   */
  async processRefund(req, res) {
    try {
      const {
        orderId,
        phoneNumber,
        amount,
        reason
      } = req.body;

      // Validate that user has permission to refund this order
      // This should be implemented based on your business logic

      const result = await B2CService.processRefund({
        orderId,
        phoneNumber,
        amount,
        reason
      });

      if (result.success) {
        res.status(200).json({
          success: true,
          message: 'Refund initiated successfully',
          data: result.data
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.message,
          errors: result.errors,
          error: result.error
        });
      }
    } catch (error) {
      logger.error('❌ B2C refund controller error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to process refund',
        error: error.message
      });
    }
  }

  /**
   * Process salary payment
   * @route POST /api/mpesa/b2c/salary
   */
  async processSalaryPayment(req, res) {
    try {
      const {
        employeePhone,
        amount,
        employeeId,
        payrollPeriod
      } = req.body;

      const result = await B2CService.processSalaryPayment({
        employeePhone,
        amount,
        employeeId,
        payrollPeriod
      });

      if (result.success) {
        res.status(200).json({
          success: true,
          message: 'Salary payment initiated successfully',
          data: result.data
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.message,
          errors: result.errors,
          error: result.error
        });
      }
    } catch (error) {
      logger.error('❌ B2C salary payment controller error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to process salary payment',
        error: error.message
      });
    }
  }

  /**
   * Process promotional payment
   * @route POST /api/mpesa/b2c/promotion
   */
  async processPromotionalPayment(req, res) {
    try {
      const {
        customerPhone,
        amount,
        promotionCode,
        campaignId
      } = req.body;

      const result = await B2CService.processPromotionalPayment({
        customerPhone,
        amount,
        promotionCode,
        campaignId
      });

      if (result.success) {
        res.status(200).json({
          success: true,
          message: 'Promotional payment initiated successfully',
          data: result.data
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.message,
          errors: result.errors,
          error: result.error
        });
      }
    } catch (error) {
      logger.error('❌ B2C promotional payment controller error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to process promotional payment',
        error: error.message
      });
    }
  }

  /**
   * Get transaction status
   * @route GET /api/mpesa/b2c/transaction/:id
   */
  async getTransactionStatus(req, res) {
    try {
      const { id } = req.params;

      const result = await B2CService.getTransactionStatus(id);

      if (result.success) {
        res.status(200).json({
          success: true,
          data: result.data
        });
      } else {
        res.status(404).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      logger.error('❌ B2C transaction status controller error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get transaction status',
        error: error.message
      });
    }
  }

  /**
   * Get transaction history
   * @route GET /api/mpesa/b2c/transactions
   */
  async getTransactionHistory(req, res) {
    try {
      const filters = req.query;

      const result = await B2CService.getTransactionHistory(filters);

      if (result.success) {
        res.status(200).json({
          success: true,
          data: result.data
        });
      } else {
        res.status(500).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      logger.error('❌ B2C transaction history controller error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get transaction history',
        error: error.message
      });
    }
  }

  /**
   * Handle B2C result callback from Safaricom
   * @route POST /api/mpesa/b2c/callback/result/:transactionId
   */
  async handleResultCallback(req, res) {
    try {
      const { transactionId } = req.params;
      const callbackData = req.body;

      logger.info('📞 B2C Result callback received:', {
        transactionId,
        data: callbackData
      });

      // Find the transaction
      const transaction = await B2CTransaction.findByPk(transactionId);
      if (!transaction) {
        logger.error('❌ Transaction not found for callback:', transactionId);
        return res.status(404).json({
          ResultCode: 1,
          ResultDesc: 'Transaction not found'
        });
      }

      // Process the callback
      const result = callbackData.Result;
      const resultCode = result.ResultCode;
      const resultDesc = result.ResultDesc;

      let updateData = {
        resultCode,
        resultDescription: resultDesc,
        completedAt: new Date(),
        callbackData
      };

      if (resultCode === 0) {
        // Success
        updateData.status = 'COMPLETED';

        // Extract additional data from callback
        if (result.ResultParameters && result.ResultParameters.ResultParameter) {
          const params = result.ResultParameters.ResultParameter;
          const receiptParam = params.find(p => p.Key === 'ReceiptNo');
          const transactionAmountParam = params.find(p => p.Key === 'TransactionAmount');
          const transactionDateParam = params.find(p => p.Key === 'TransactionCompletedDateTime');
          const b2cChargesParam = params.find(p => p.Key === 'B2CChargesPaidAccountAvailableFunds');
          const receiverPartyParam = params.find(p => p.Key === 'ReceiverPartyPublicName');
          const b2cUtilityParam = params.find(p => p.Key === 'B2CUtilityAccountAvailableFunds');
          const b2cWorkingParam = params.find(p => p.Key === 'B2CWorkingAccountAvailableFunds');

          if (receiptParam) updateData.mpesaReceiptNumber = receiptParam.Value;
          if (transactionAmountParam) updateData.transactionAmount = parseFloat(transactionAmountParam.Value);
          if (transactionDateParam) updateData.transactionDate = new Date(transactionDateParam.Value);
          if (b2cChargesParam) updateData.b2cCharges = parseFloat(b2cChargesParam.Value);
          if (receiverPartyParam) updateData.receiverPartyName = receiverPartyParam.Value;
          if (b2cUtilityParam) updateData.b2cUtilityBalance = parseFloat(b2cUtilityParam.Value);
          if (b2cWorkingParam) updateData.b2cWorkingBalance = parseFloat(b2cWorkingParam.Value);
        }

        logger.info('✅ B2C transaction completed successfully:', {
          transactionId,
          receiptNumber: updateData.mpesaReceiptNumber
        });
      } else {
        // Failed
        updateData.status = 'FAILED';
        logger.error('❌ B2C transaction failed:', {
          transactionId,
          resultCode,
          resultDesc
        });
      }

      // Update transaction
      await transaction.update(updateData);

      // Send success response to Safaricom
      res.status(200).json({
        ResultCode: 0,
        ResultDesc: 'Callback processed successfully'
      });

    } catch (error) {
      logger.error('❌ B2C result callback error:', error);
      res.status(500).json({
        ResultCode: 1,
        ResultDesc: 'Internal server error'
      });
    }
  }

  /**
   * Handle B2C timeout callback from Safaricom
   * @route POST /api/mpesa/b2c/callback/timeout/:transactionId
   */
  async handleTimeoutCallback(req, res) {
    try {
      const { transactionId } = req.params;
      const callbackData = req.body;

      logger.info('📞 B2C Timeout callback received:', {
        transactionId,
        data: callbackData
      });

      // Find and update the transaction
      const transaction = await B2CTransaction.findByPk(transactionId);
      if (transaction) {
        await transaction.update({
          status: 'TIMEOUT',
          resultCode: 1,
          resultDescription: 'Transaction timed out',
          completedAt: new Date(),
          callbackData
        });

        logger.warn('⏰ B2C transaction timed out:', transactionId);
      }

      res.status(200).json({
        ResultCode: 0,
        ResultDesc: 'Timeout processed successfully'
      });

    } catch (error) {
      logger.error('❌ B2C timeout callback error:', error);
      res.status(500).json({
        ResultCode: 1,
        ResultDesc: 'Internal server error'
      });
    }
  }
}

module.exports = new B2CController();
