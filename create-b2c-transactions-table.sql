-- Create B2C Transactions Table for G20Shop M-Pesa Integration
-- Run this SQL script in your PostgreSQL database (pgAdmin or psql)

-- First, create the ENUM types for B2C transactions
CREATE TYPE enum_b2c_transactions_transactiontype AS ENUM ('REFUND', 'SALARY', 'PROMOTION', 'GENERAL');
CREATE TYPE enum_b2c_transactions_commandid AS ENUM ('BusinessPayment', 'SalaryPayment', 'PromotionPayment');
CREATE TYPE enum_b2c_transactions_status AS ENUM ('PENDING', 'SUBMITTED', 'COMPLETED', 'FAILED', 'TIMEOUT', 'CANCELLED');

-- Create the b2c_transactions table
CREATE TABLE b2c_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "conversationId" VARCHAR(255) NOT NULL UNIQUE,
    "mpesaConversationId" VARCHAR(255),
    "originatorConversationId" VARCHAR(255),
    "phoneNumber" VARCHAR(255) NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    "transactionType" enum_b2c_transactions_transactiontype NOT NULL DEFAULT 'GENERAL',
    "commandId" enum_b2c_transactions_commandid NOT NULL DEFAULT 'BusinessPayment',
    remarks VARCHAR(255) NOT NULL DEFAULT 'G20Shop Payment',
    occasion VARCHAR(255) NOT NULL DEFAULT 'Payment Processing',
    status enum_b2c_transactions_status NOT NULL DEFAULT 'PENDING',
    "responseCode" VARCHAR(255),
    "responseDescription" VARCHAR(255),
    "resultCode" INTEGER,
    "resultDescription" VARCHAR(255),
    "mpesaReceiptNumber" VARCHAR(255),
    "transactionAmount" DECIMAL(10, 2),
    "transactionDate" TIMESTAMP WITH TIME ZONE,
    "b2cCharges" DECIMAL(10, 2),
    "receiverPartyName" VARCHAR(255),
    "b2cUtilityBalance" DECIMAL(15, 2),
    "b2cWorkingBalance" DECIMAL(15, 2),
    "orderId" UUID REFERENCES orders(id) ON UPDATE CASCADE ON DELETE SET NULL,
    "userId" VARCHAR(255),
    metadata JSONB,
    "callbackData" JSONB,
    "initiatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "completedAt" TIMESTAMP WITH TIME ZONE,
    "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add comments to the table and columns
COMMENT ON TABLE b2c_transactions IS 'M-Pesa B2C (Business to Customer) transactions for refunds, salary payments, etc.';
COMMENT ON COLUMN b2c_transactions."conversationId" IS 'Our internal conversation ID';
COMMENT ON COLUMN b2c_transactions."mpesaConversationId" IS 'M-Pesa conversation ID from API response';
COMMENT ON COLUMN b2c_transactions."originatorConversationId" IS 'M-Pesa originator conversation ID';
COMMENT ON COLUMN b2c_transactions."phoneNumber" IS 'Customer phone number in 254XXXXXXXXX format';
COMMENT ON COLUMN b2c_transactions.amount IS 'Transaction amount in KES';
COMMENT ON COLUMN b2c_transactions."transactionType" IS 'Type of B2C transaction';
COMMENT ON COLUMN b2c_transactions."commandId" IS 'M-Pesa command ID for the transaction';
COMMENT ON COLUMN b2c_transactions.remarks IS 'Transaction remarks';
COMMENT ON COLUMN b2c_transactions.occasion IS 'Transaction occasion';
COMMENT ON COLUMN b2c_transactions.status IS 'Transaction status';
COMMENT ON COLUMN b2c_transactions."responseCode" IS 'Initial API response code';
COMMENT ON COLUMN b2c_transactions."responseDescription" IS 'Initial API response description';
COMMENT ON COLUMN b2c_transactions."resultCode" IS 'Final result code from callback';
COMMENT ON COLUMN b2c_transactions."resultDescription" IS 'Final result description from callback';
COMMENT ON COLUMN b2c_transactions."mpesaReceiptNumber" IS 'M-Pesa receipt number for successful transactions';
COMMENT ON COLUMN b2c_transactions."transactionAmount" IS 'Actual transaction amount from M-Pesa callback';
COMMENT ON COLUMN b2c_transactions."transactionDate" IS 'Transaction completion date from M-Pesa';
COMMENT ON COLUMN b2c_transactions."b2cCharges" IS 'B2C charges deducted';
COMMENT ON COLUMN b2c_transactions."receiverPartyName" IS 'Name of the receiver from M-Pesa';
COMMENT ON COLUMN b2c_transactions."b2cUtilityBalance" IS 'B2C utility account balance after transaction';
COMMENT ON COLUMN b2c_transactions."b2cWorkingBalance" IS 'B2C working account balance after transaction';
COMMENT ON COLUMN b2c_transactions."orderId" IS 'Associated order ID for refunds';
COMMENT ON COLUMN b2c_transactions."userId" IS 'User ID who initiated the transaction';
COMMENT ON COLUMN b2c_transactions.metadata IS 'Additional transaction metadata';
COMMENT ON COLUMN b2c_transactions."callbackData" IS 'Full callback data from M-Pesa';
COMMENT ON COLUMN b2c_transactions."initiatedAt" IS 'When the transaction was initiated';
COMMENT ON COLUMN b2c_transactions."completedAt" IS 'When the transaction was completed';

-- Create indexes for better performance
CREATE UNIQUE INDEX idx_b2c_transactions_conversation_id ON b2c_transactions ("conversationId");
CREATE INDEX idx_b2c_transactions_mpesa_conversation_id ON b2c_transactions ("mpesaConversationId");
CREATE INDEX idx_b2c_transactions_phone_number ON b2c_transactions ("phoneNumber");
CREATE INDEX idx_b2c_transactions_status ON b2c_transactions (status);
CREATE INDEX idx_b2c_transactions_type ON b2c_transactions ("transactionType");
CREATE INDEX idx_b2c_transactions_order_id ON b2c_transactions ("orderId");
CREATE INDEX idx_b2c_transactions_user_id ON b2c_transactions ("userId");
CREATE INDEX idx_b2c_transactions_initiated_at ON b2c_transactions ("initiatedAt");
CREATE UNIQUE INDEX idx_b2c_transactions_receipt_number ON b2c_transactions ("mpesaReceiptNumber") WHERE "mpesaReceiptNumber" IS NOT NULL;

-- Create a trigger to automatically update the updatedAt column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_b2c_transactions_updated_at 
    BEFORE UPDATE ON b2c_transactions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert a record into the SequelizeMeta table to track this migration
INSERT INTO "SequelizeMeta" (name) VALUES ('20241201000001-create-b2c-transactions.js')
ON CONFLICT (name) DO NOTHING;

-- Display success message
SELECT 'B2C Transactions table created successfully! ✅' AS result;

-- Show table structure
\d b2c_transactions;
