import { User } from '../models';
import { UserRole } from '../types';
import { logger } from '../utils/logger';
import config from './index';

/**
 * Initialize database with default data
 */
export const initializeDB = async (): Promise<void> => {
  try {
    // Check if admin user exists
    const adminCount = await User.countDocuments({ role: UserRole.ADMIN });

    if (adminCount === 0) {
      logger.info('No admin user found. Creating default admin user...');
      
      // Create default admin user
      await User.create({
        name: 'Admin User',
        email: process.env.ADMIN_EMAIL || '<EMAIL>',
        password: process.env.ADMIN_PASSWORD || 'Admin@123',
        role: UserRole.ADMIN,
        isActive: true
      });
      
      logger.info('Default admin user created successfully');
    } else {
      logger.info('Admin user already exists. Skipping creation.');
    }

    // Add more initialization logic here if needed
    // For example: creating default product categories, etc.

    logger.info('Database initialization completed successfully');
  } catch (error: any) {
    logger.error(`Database initialization failed: ${error.message}`);
    // Don't exit the process here, just log the error
  }
};

/**
 * Run database initialization
 * This is the default export to be called from server.ts
 */
const runDatabaseInit = async (): Promise<void> => {
  try {
    logger.info('Starting database initialization...');
    await initializeDB();
  } catch (error: any) {
    logger.error(`Error during database initialization: ${error.message}`);
  }
};

export default runDatabaseInit;

