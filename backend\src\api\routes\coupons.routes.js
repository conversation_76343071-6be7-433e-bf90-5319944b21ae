const express = require('express');
const router = express.Router();
const couponsController = require('../controllers/coupons.controller');
const { authenticate, authorize, optionalAuth } = require('../middlewares/auth.middleware');
const { validateCoupon, validateCouponUpdate } = require('../validators/coupon.validator');

// Public routes (with optional auth)
router.post(
  '/validate',
  optionalAuth,
  couponsController.validateCoupon
);

// Admin routes
router.post(
  '/',
  authenticate,
  authorize(['admin']),
  validateCoupon,
  couponsController.createCoupon
);

router.get(
  '/',
  authenticate,
  authorize(['admin']),
  couponsController.getAllCoupons
);

router.get(
  '/:id',
  authenticate,
  authorize(['admin']),
  couponsController.getCoupon
);

router.put(
  '/:id',
  authenticate,
  authorize(['admin']),
  validateCouponUpdate,
  couponsController.updateCoupon
);

router.delete(
  '/:id',
  authenticate,
  authorize(['admin']),
  couponsController.deleteCoupon
);

module.exports = router;
