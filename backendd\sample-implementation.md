# Sample Implementation Files

## 1. Database Models

### `src/db/models/Product.js`

```javascript
const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const Category = require('./Category');

const Product = sequelize.define('Product', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT
  },
  sku: {
    type: DataTypes.STRING,
    unique: true,
    allowNull: false
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  imageUrl: {
    type: DataTypes.STRING
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  }
}, {
  timestamps: true
});

// Define relationships
Product.belongsTo(Category);
Category.hasMany(Product);

module.exports = Product;
```

### `src/db/models/RepairTicket.js`

```javascript
const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const Customer = require('./Customer');
const User = require('./User');

const RepairTicket = sequelize.define('RepairTicket', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  ticketNumber: {
    type: DataTypes.STRING,
    unique: true,
    allowNull: false
  },
  deviceType: {
    type: DataTypes.STRING,
    allowNull: false
  },
  brand: {
    type: DataTypes.STRING,
    allowNull: false
  },
  model: {
    type: DataTypes.STRING,
    allowNull: false
  },
  serialNumber: {
    type: DataTypes.STRING
  },
  issueDescription: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('received', 'diagnosing', 'awaiting_parts', 'in_progress', 'completed', 'delivered'),
    defaultValue: 'received'
  },
  estimatedCompletionDate: {
    type: DataTypes.DATE
  },
  actualCompletionDate: {
    type: DataTypes.DATE
  },
  cost: {
    type: DataTypes.DECIMAL(10, 2)
  },
  notes: {
    type: DataTypes.TEXT
  }
}, {
  timestamps: true
});

// Define relationships
RepairTicket.belongsTo(Customer);
Customer.hasMany(RepairTicket);

RepairTicket.belongsTo(User, { as: 'technician' });
RepairTicket.belongsTo(User, { as: 'createdBy' });

module.exports = RepairTicket;
```

## 2. Controllers

### `src/api/controllers/repairs.controller.js`

```javascript
const repairService = require('../../services/repair.service');
const { NotFoundError, ValidationError } = require('../../utils/errors');

// Get all repair tickets with pagination
exports.getAllRepairs = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, status, customerId } = req.query;
    const repairs = await repairService.getAllRepairs({ page, limit, status, customerId });
    res.status(200).json(repairs);
  } catch (error) {
    next(error);
  }
};

// Get repair ticket by ID
exports.getRepairById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const repair = await repairService.getRepairById(id);
    
    if (!repair) {
      throw new NotFoundError('Repair ticket not found');
    }
    
    res.status(200).json(repair);
  } catch (error) {
    next(error);
  }
};

// Create new repair ticket
exports.createRepair = async (req, res, next) => {
  try {
    const repairData = req.body;
    repairData.createdById = req.user.id; // Set the user who created the ticket
    
    const newRepair = await repairService.createRepair(repairData);
    res.status(201).json(newRepair);
  } catch (error) {
    next(error);
  }
};

// Update repair ticket
exports.updateRepair = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    const updatedRepair = await repairService.updateRepair(id, updateData);
    
    if (!updatedRepair) {
      throw new NotFoundError('Repair ticket not found');
    }
    
    res.status(200).json(updatedRepair);
  } catch (error) {
    next(error);
  }
};

// Update repair status
exports.updateRepairStatus = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status, notes } = req.body;
    
    if (!status) {
      throw new ValidationError('Status is required');
    }
    
    const updatedRepair = await repairService.updateRepairStatus(id, status, notes, req.user.id);
    
    if (!updatedRepair) {
      throw new NotFoundError('Repair ticket not found');
    }
    
    res.status(200).json(updatedRepair);
  } catch (error) {
    next(error);
  }
};

// Delete repair ticket
exports.deleteRepair = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const result = await repairService.deleteRepair(id);
    
    if (!result) {
      throw new NotFoundError('Repair ticket not found');
    }
    
    res.status(204).send();
  } catch (error) {
    next(error);
  }
};
```

## 3. Services

### `src/services/repair.service.js`

```javascript
const { Op } = require('sequelize');
const RepairTicket = require('../db/models/RepairTicket');
const Customer = require('../db/models/Customer');
const User = require('../db/models/User');
const RepairStatus = require('../db/models/RepairStatus');
const emailService = require('./email.service');
const { generateTicketNumber } = require('../utils/helpers');

// Get all repair tickets with filtering and pagination
exports.getAllRepairs = async ({ page, limit, status, customerId }) => {
  const offset = (page - 1) * limit;
  const where = {};
  
  if (status) {
    where.status = status;
  }
  
  if (customerId) {
    where.customerId = customerId;
  }
  
  const { count, rows } = await RepairTicket.findAndCountAll({
    where,
    limit,
    offset,
    include: [
      { model: Customer, attributes: ['id', 'name', 'email', 'phone'] },
      { model: User, as: 'technician', attributes: ['id', 'name'] },
      { model: User, as: 'createdBy', attributes: ['id', 'name'] }
    ],
    order: [['createdAt', 'DESC']]
  });
  
  return {
    repairs: rows,
    totalItems: count,
    totalPages: Math.ceil(count / limit),
    currentPage: page
  };
};

// Get repair ticket by ID
exports.getRepairById = async (id) => {
  return RepairTicket.findByPk(id, {
    include: [
      { model: Customer, attributes: ['id', 'name', 'email', 'phone'] },
      { model: User, as: 'technician', attributes: ['id', 'name'] },
      { model: User, as: 'createdBy', attributes: ['id', 'name'] },
      { model: RepairStatus, order: [['createdAt', 'DESC']] }
    ]
  });
};

// Create new repair ticket
exports.createRepair = async (repairData) => {
  // Generate a unique ticket number
  repairData.ticketNumber = await generateTicketNumber();
  
  const newRepair = await RepairTicket.create(repairData);
  
  // Create initial status entry
  await RepairStatus.create({
    repairTicketId: newRepair.id,
    status: 'received',
    notes: 'Device received for repair',
    userId: repairData.createdById
  });
  
  // Get the full repair ticket with associations
  const repair = await this.getRepairById(newRepair.id);
  
  // Send email notification to customer
  await emailService.sendRepairReceivedEmail(repair);
  
  return repair;
};

// Update repair ticket
exports.updateRepair = async (id, updateData) => {
  const repair = await RepairTicket.findByPk(id);
  
  if (!repair) {
    return null;
  }
  
  await repair.update(updateData);
  
  return this.getRepairById(id);
};

// Update repair status
exports.updateRepairStatus = async (id, status, notes, userId) => {
  const repair = await RepairTicket.findByPk(id, {
    include: [{ model: Customer }]
  });
  
  if (!repair) {
    return null;
  }
  
  // Update the repair ticket status
  await repair.update({ status });
  
  // Create a status history entry
  await RepairStatus.create({
    repairTicketId: id,
    status,
    notes,
    userId
  });
  
  // If status is completed, set the actual completion date
  if (status === 'completed') {
    await repair.update({ actualCompletionDate: new Date() });
    
    // Send completion email to customer
    await emailService.sendRepairCompletedEmail(repair);
  }
  
  return this.getRepairById(id);
};

// Delete repair ticket
exports.deleteRepair = async (id) => {
  const repair = await RepairTicket.findByPk(id);
  
  if (!repair) {
    return false;
  }
  
  await repair.destroy();
  return true;
};
```

## 4. Routes

### `src/api/routes/repairs.routes.js`

```javascript
const express = require('express');
const router = express.Router();
const repairsController = require('../controllers/repairs.controller');
const { authenticate, authorize } = require('../middlewares/auth.middleware');
const { validateRepairCreate, validateRepairUpdate, validateStatusUpdate } = require('../validators/repair.validator');

// Get all repair tickets (requires authentication)
router.get('/', authenticate, repairsController.getAllRepairs);

// Get repair ticket by ID
router.get('/:id', authenticate, repairsController.getRepairById);

// Create new repair ticket (requires authentication)
router.post('/', authenticate, validateRepairCreate, repairsController.createRepair);

// Update repair ticket (requires authentication)
router.put('/:id', authenticate, validateRepairUpdate, repairsController.updateRepair);

// Update repair status (requires authentication and technician role)
router.patch('/:id/status', 
  authenticate, 
  authorize(['admin', 'technician']), 
  validateStatusUpdate, 
  repairsController.updateRepairStatus
);

// Delete repair ticket (requires admin role)
router.delete('/:id', authenticate, authorize(['admin']), repairsController.deleteRepair);

module.exports = router;
```

## 5. Middleware

### `src/api/middlewares/auth.middleware.js`

```javascript
const jwt = require('jsonwebtoken');
const { AuthenticationError, AuthorizationError } = require('../../utils/errors');
const User = require('../../db/models/User');
const config = require('../../config');

// Authenticate user middleware
exports.authenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AuthenticationError('No token provided');
    }
    
    const token = authHeader.split(' ')[1];
    
    try {
      const decoded = jwt.verify(token, config.jwt.secret);
      
      // Get user from database
      const user = await User.findByPk(decoded.id, {
        attributes: { exclude: ['password'] }
      });
      
      if (!user) {
        throw new AuthenticationError('User not found');
      }
      
      // Attach user to request object
      req.user = user;
      next();
    } catch (error) {
      throw new AuthenticationError('Invalid token');
    }
  } catch (error) {
    next(error);
  }
};

// Authorize roles middleware
exports.authorize = (roles = []) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        throw new AuthenticationError('User not authenticated');
      }
      
      if (roles.length && !roles.includes(req.user.role)) {
        throw new AuthorizationError('Insufficient permissions');
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
};
```
