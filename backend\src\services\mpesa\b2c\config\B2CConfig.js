/**
 * M-Pesa B2C Configuration
 * Centralized configuration management for B2C operations
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-01-15
 */

const logger = require('../../../../utils/logger');

class B2CConfig {
  constructor() {
    this.environment = process.env.MPESA_ENVIRONMENT || 'sandbox';
    this.consumerKey = process.env.MPESA_CONSUMER_KEY;
    this.consumerSecret = process.env.MPESA_CONSUMER_SECRET;
    this.businessShortCode = process.env.MPESA_BUSINESS_SHORT_CODE;
    this.initiatorName = process.env.MPESA_INITIATOR_NAME;
    this.securityCredential = process.env.MPESA_SECURITY_CREDENTIAL;
    this.queueTimeoutUrl = process.env.MPESA_B2C_QUEUE_TIMEOUT_URL;
    this.resultUrl = process.env.MPESA_B2C_RESULT_URL;
    
    // Set base URL based on environment
    this.baseUrl = this.environment === 'production' 
      ? 'https://api.safaricom.co.ke'
      : 'https://sandbox.safaricom.co.ke';

    // B2C Command IDs
    this.COMMAND_IDS = {
      BUSINESS_PAYMENT: 'BusinessPayment',      // General business payments
      SALARY_PAYMENT: 'SalaryPayment',         // Salary payments
      PROMOTION_PAYMENT: 'PromotionPayment'    // Promotional payments
    };

    // Transaction types for our system
    this.TRANSACTION_TYPES = {
      REFUND: 'REFUND',
      SALARY: 'SALARY',
      PROMOTION: 'PROMOTION',
      GENERAL: 'GENERAL'
    };

    // Transaction status types
    this.TRANSACTION_STATUS = {
      PENDING: 'PENDING',
      SUBMITTED: 'SUBMITTED',
      COMPLETED: 'COMPLETED',
      FAILED: 'FAILED',
      TIMEOUT: 'TIMEOUT',
      CANCELLED: 'CANCELLED'
    };

    // Business rules and limits
    this.LIMITS = {
      MIN_AMOUNT: 1,
      MAX_AMOUNT: 150000,
      MAX_DAILY_AMOUNT: parseInt(process.env.MPESA_B2C_DAILY_LIMIT) || 500000,
      MAX_SINGLE_TRANSACTION: parseInt(process.env.MPESA_B2C_SINGLE_TRANSACTION_LIMIT) || 150000,
      RATE_LIMIT_WINDOW: 5 * 60 * 1000, // 5 minutes
      RATE_LIMIT_MAX_REQUESTS: 10
    };

    // Timeout configurations
    this.TIMEOUTS = {
      TOKEN_REQUEST: 30000,      // 30 seconds
      PAYMENT_REQUEST: 60000,    // 60 seconds
      STATUS_QUERY: 30000,       // 30 seconds
      BALANCE_QUERY: 30000,      // 30 seconds
      REVERSAL_REQUEST: 30000    // 30 seconds
    };

    // Retry configurations
    this.RETRY = {
      MAX_ATTEMPTS: 3,
      DELAY_MS: 1000,
      BACKOFF_MULTIPLIER: 2
    };

    // Validate configuration on initialization
    this.validateConfig();
  }

  /**
   * Validate required configuration
   * @throws {Error} If required configuration is missing
   */
  validateConfig() {
    const requiredFields = [
      'consumerKey',
      'consumerSecret',
      'businessShortCode',
      'initiatorName',
      'securityCredential'
    ];

    const missingFields = requiredFields.filter(field => !this[field]);

    if (missingFields.length > 0) {
      const error = `Missing required M-Pesa B2C configuration: ${missingFields.join(', ')}`;
      logger.error('❌ B2C Configuration Error:', { missingFields, environment: this.environment });
      throw new Error(error);
    }

    // Validate callback URLs for production
    if (this.environment === 'production') {
      if (!this.resultUrl || !this.queueTimeoutUrl) {
        const error = 'Result URL and Queue Timeout URL are required for production environment';
        logger.error('❌ B2C Production Configuration Error:', { 
          hasResultUrl: !!this.resultUrl,
          hasQueueTimeoutUrl: !!this.queueTimeoutUrl
        });
        throw new Error(error);
      }

      // Validate URLs are HTTPS in production
      if (!this.resultUrl.startsWith('https://') || !this.queueTimeoutUrl.startsWith('https://')) {
        const error = 'Callback URLs must use HTTPS in production environment';
        logger.error('❌ B2C HTTPS Configuration Error:', {
          resultUrl: this.resultUrl,
          queueTimeoutUrl: this.queueTimeoutUrl
        });
        throw new Error(error);
      }
    }

    logger.info('✅ B2C Configuration validated successfully', {
      environment: this.environment,
      businessShortCode: this.businessShortCode,
      initiatorName: this.initiatorName,
      hasCallbackUrls: !!(this.resultUrl && this.queueTimeoutUrl)
    });
  }

  /**
   * Get command ID based on transaction type
   * @param {string} transactionType - Transaction type
   * @returns {string} Command ID
   */
  getCommandId(transactionType) {
    switch (transactionType) {
      case this.TRANSACTION_TYPES.SALARY:
        return this.COMMAND_IDS.SALARY_PAYMENT;
      case this.TRANSACTION_TYPES.PROMOTION:
        return this.COMMAND_IDS.PROMOTION_PAYMENT;
      case this.TRANSACTION_TYPES.REFUND:
      case this.TRANSACTION_TYPES.GENERAL:
      default:
        return this.COMMAND_IDS.BUSINESS_PAYMENT;
    }
  }

  /**
   * Get transaction type description
   * @param {string} type - Transaction type
   * @returns {string} Description
   */
  getTransactionTypeDescription(type) {
    const descriptions = {
      [this.TRANSACTION_TYPES.REFUND]: 'Customer refund',
      [this.TRANSACTION_TYPES.SALARY]: 'Employee salary payment',
      [this.TRANSACTION_TYPES.PROMOTION]: 'Promotional payment',
      [this.TRANSACTION_TYPES.GENERAL]: 'General business payment'
    };
    
    return descriptions[type] || 'Unknown transaction type';
  }

  /**
   * Get status description
   * @param {string} status - Transaction status
   * @returns {string} Description
   */
  getStatusDescription(status) {
    const descriptions = {
      [this.TRANSACTION_STATUS.PENDING]: 'Transaction is pending initiation',
      [this.TRANSACTION_STATUS.SUBMITTED]: 'Transaction has been submitted to M-Pesa',
      [this.TRANSACTION_STATUS.COMPLETED]: 'Transaction completed successfully',
      [this.TRANSACTION_STATUS.FAILED]: 'Transaction failed',
      [this.TRANSACTION_STATUS.TIMEOUT]: 'Transaction timed out',
      [this.TRANSACTION_STATUS.CANCELLED]: 'Transaction was cancelled'
    };
    
    return descriptions[status] || 'Unknown status';
  }

  /**
   * Check if amount is within limits
   * @param {number} amount - Amount to check
   * @param {string} transactionType - Transaction type
   * @returns {Object} Validation result
   */
  validateAmount(amount, transactionType = null) {
    const errors = [];

    if (amount < this.LIMITS.MIN_AMOUNT) {
      errors.push(`Amount must be at least KES ${this.LIMITS.MIN_AMOUNT}`);
    }

    if (amount > this.LIMITS.MAX_AMOUNT) {
      errors.push(`Amount cannot exceed KES ${this.LIMITS.MAX_AMOUNT}`);
    }

    // Transaction type specific limits
    if (transactionType === this.TRANSACTION_TYPES.SALARY && amount > 100000) {
      errors.push('Salary payments cannot exceed KES 100,000');
    }

    if (transactionType === this.TRANSACTION_TYPES.PROMOTION && amount > 10000) {
      errors.push('Promotional payments cannot exceed KES 10,000');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check if transaction is within business hours
   * @param {Date} date - Date to check
   * @returns {boolean} True if within business hours
   */
  isWithinBusinessHours(date = new Date()) {
    const hour = date.getHours();
    const day = date.getDay(); // 0 = Sunday, 6 = Saturday
    
    // M-Pesa operates 24/7, but you might want to restrict certain operations
    // Business hours: 6 AM to 10 PM daily
    return hour >= 6 && hour <= 22;
  }

  /**
   * Get environment-specific test data
   * @returns {Object} Test data
   */
  getTestData() {
    if (this.environment === 'sandbox') {
      return {
        testPhoneNumbers: [
          '254708374149',
          '254711000000',
          '254733000000'
        ],
        testAmounts: [1, 100, 1000, 5000, 10000],
        testShortCode: '600000'
      };
    }

    return {
      testPhoneNumbers: [],
      testAmounts: [],
      testShortCode: null,
      note: 'No test data available for production environment'
    };
  }

  /**
   * Get configuration summary (safe for logging)
   * @returns {Object} Configuration summary
   */
  getSummary() {
    return {
      environment: this.environment,
      baseUrl: this.baseUrl,
      businessShortCode: this.businessShortCode,
      initiatorName: this.initiatorName,
      hasCredentials: !!(this.consumerKey && this.consumerSecret && this.securityCredential),
      hasCallbackUrls: !!(this.resultUrl && this.queueTimeoutUrl),
      limits: this.LIMITS,
      timeouts: this.TIMEOUTS,
      commandIds: this.COMMAND_IDS,
      transactionTypes: this.TRANSACTION_TYPES,
      transactionStatus: this.TRANSACTION_STATUS
    };
  }

  /**
   * Update configuration at runtime (for testing)
   * @param {Object} updates - Configuration updates
   */
  updateConfig(updates) {
    Object.keys(updates).forEach(key => {
      if (this.hasOwnProperty(key)) {
        this[key] = updates[key];
        logger.info(`✅ B2C Config updated: ${key}`, { 
          newValue: key.includes('secret') || key.includes('credential') ? '[HIDDEN]' : updates[key]
        });
      }
    });

    // Re-validate after updates
    this.validateConfig();
  }

  /**
   * Reset to default configuration
   */
  resetToDefaults() {
    logger.info('🔄 Resetting B2C configuration to defaults');
    
    // Reload from environment variables
    this.environment = process.env.MPESA_ENVIRONMENT || 'sandbox';
    this.consumerKey = process.env.MPESA_CONSUMER_KEY;
    this.consumerSecret = process.env.MPESA_CONSUMER_SECRET;
    this.businessShortCode = process.env.MPESA_BUSINESS_SHORT_CODE;
    this.initiatorName = process.env.MPESA_INITIATOR_NAME;
    this.securityCredential = process.env.MPESA_SECURITY_CREDENTIAL;
    this.queueTimeoutUrl = process.env.MPESA_B2C_QUEUE_TIMEOUT_URL;
    this.resultUrl = process.env.MPESA_B2C_RESULT_URL;
    
    this.baseUrl = this.environment === 'production' 
      ? 'https://api.safaricom.co.ke'
      : 'https://sandbox.safaricom.co.ke';

    this.validateConfig();
  }
}

module.exports = B2CConfig;
