'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create import_jobs table
    await queryInterface.createTable('import_jobs', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      sourceType: {
        type: Sequelize.ENUM('file_upload', 'ftp', 'http_url', 'google_sheets', 'api_webhook'),
        allowNull: false,
        defaultValue: 'file_upload'
      },
      sourceConfig: {
        type: Sequelize.JSON,
        allowNull: true
      },
      schedule: {
        type: Sequelize.STRING,
        allowNull: false
      },
      timezone: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'UTC'
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      lastRunAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      nextRunAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      lastRunStatus: {
        type: Sequelize.ENUM('pending', 'running', 'success', 'failed', 'cancelled'),
        allowNull: true
      },
      lastRunResult: {
        type: Sequelize.JSON,
        allowNull: true
      },
      totalRuns: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      successfulRuns: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      failedRuns: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      createdBy: {
        type: Sequelize.STRING,
        allowNull: false
      },
      updatedBy: {
        type: Sequelize.STRING,
        allowNull: true
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false
      }
    });

    // Create import_job_logs table
    await queryInterface.createTable('import_job_logs', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      importJobId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'import_jobs',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      status: {
        type: Sequelize.ENUM('pending', 'running', 'success', 'failed', 'cancelled'),
        allowNull: false,
        defaultValue: 'pending'
      },
      startedAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      completedAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      duration: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      recordsProcessed: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      recordsImported: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      recordsFailed: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      errors: {
        type: Sequelize.JSON,
        allowNull: true
      },
      sourceInfo: {
        type: Sequelize.JSON,
        allowNull: true
      },
      result: {
        type: Sequelize.JSON,
        allowNull: true
      },
      triggeredBy: {
        type: Sequelize.ENUM('schedule', 'manual', 'api'),
        allowNull: false,
        defaultValue: 'schedule'
      },
      triggeredByUser: {
        type: Sequelize.STRING,
        allowNull: true
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false
      }
    });

    // Add indexes
    await queryInterface.addIndex('import_jobs', ['isActive']);
    await queryInterface.addIndex('import_jobs', ['nextRunAt']);
    await queryInterface.addIndex('import_jobs', ['createdBy']);
    await queryInterface.addIndex('import_job_logs', ['importJobId']);
    await queryInterface.addIndex('import_job_logs', ['status']);
    await queryInterface.addIndex('import_job_logs', ['startedAt']);
    await queryInterface.addIndex('import_job_logs', ['triggeredBy']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('import_job_logs');
    await queryInterface.dropTable('import_jobs');
  }
};
