# TechGear Backend Testing Guide

This guide provides comprehensive instructions for testing the TechGear e-commerce backend.

## Table of Contents

1. [Setting Up the Test Environment](#setting-up-the-test-environment)
2. [Running the Tests](#running-the-tests)
3. [Understanding Test Results](#understanding-test-results)
4. [Manual Testing](#manual-testing)
5. [Troubleshooting](#troubleshooting)

## Setting Up the Test Environment

### Prerequisites

- Node.js (v14 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

### Step 1: Install Dependencies

```bash
cd backend
npm install
```

### Step 2: Configure Test Environment

Create a `.env.test` file in the backend directory with the following content:

```
# Server Configuration
NODE_ENV=test
PORT=3001

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=techgear_test
DB_USER=postgres
DB_PASSWORD=your_password

# JWT Configuration
JWT_SECRET=test_jwt_secret
JWT_EXPIRES_IN=1d
JWT_REFRESH_EXPIRES_IN=7d
```

### Step 3: Create Test Database

```bash
# Using psql
psql -U postgres -c "CREATE DATABASE techgear_test;"

# Or using sequelize-cli
npx sequelize-cli db:create --env test
```

### Step 4: Run Migrations

```bash
npm run db:migrate
```

### Step 5: Seed Test Data

```bash
npm run db:seed
```

## Running the Tests

### Automated Tests

#### Run All Tests

```bash
npm run test:all
```

This will run both model tests and API tests in sequence.

#### Run Model Tests Only

```bash
npm run test:models
```

These tests verify the database models and their relationships.

#### Run API Tests Only

```bash
npm run test:api
```

These tests verify the API endpoints.

### Starting the Test Server

To manually test the API endpoints, you can start the server in test mode:

```bash
npm run start:test
```

This will start the server using the test database on port 3001 (or the port specified in your `.env.test` file).

## Understanding Test Results

All test results are saved in the `test-results` directory as JSON files:

- Success files: `<test-name>.json`
- Error files: `<test-name>-error.json`

### Example Test Results

#### Successful Test

```json
{
  "user": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Test User",
    "email": "<EMAIL>",
    "role": "customer",
    "createdAt": "2023-05-01T12:00:00.000Z",
    "updatedAt": "2023-05-01T12:00:00.000Z"
  }
}
```

#### Failed Test

```json
{
  "message": "Validation error: Email already in use",
  "response": {
    "status": 400,
    "data": {
      "status": "error",
      "message": "Validation error: Email already in use"
    }
  }
}
```

## Manual Testing

### Using Postman

1. Import the Postman collection from `docs/postman-collection.json`
2. Set the environment variables:
   - `baseUrl`: `http://localhost:3001/api`
   - `token`: (leave empty, will be set after login)

3. Run the authentication request first to get a token
4. Other requests will use the token automatically

### Using cURL

Example cURL commands for testing the API:

#### Register a User

```bash
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "password": "Test@123",
    "phone": "1234567890"
  }'
```

#### Login

```bash
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test@123"
  }'
```

#### Get Products

```bash
curl -X GET http://localhost:3001/api/products
```

#### Get Product by ID

```bash
curl -X GET http://localhost:3001/api/products/{product_id}
```

#### Add to Cart

```bash
curl -X POST http://localhost:3001/api/cart/items \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "productId": "{product_id}",
    "quantity": 1
  }'
```

## Troubleshooting

### Common Issues

#### Database Connection Errors

```
Error: SequelizeConnectionError: connect ECONNREFUSED 127.0.0.1:5432
```

**Solution**: Make sure PostgreSQL is running and the credentials in `.env.test` are correct.

#### Missing Tables

```
Error: SequelizeConnectionError: relation "Users" does not exist
```

**Solution**: Run the migrations:

```bash
npm run db:migrate
```

#### API Server Not Running

```
Error: connect ECONNREFUSED 127.0.0.1:3001
```

**Solution**: Start the test server:

```bash
npm run start:test
```

#### Authentication Errors

```
Error: Authentication failed
```

**Solution**: Make sure you're using a valid token. Login again to get a new token.

### Getting Help

If you encounter issues not covered in this guide:

1. Check the server logs for detailed error messages
2. Review the test result files in the `test-results` directory
3. Verify your database configuration
4. Ensure all dependencies are installed correctly

## Conclusion

This testing guide provides a comprehensive approach to testing the TechGear backend. By following these instructions, you can verify that all components of the backend are functioning correctly before deploying to production.
