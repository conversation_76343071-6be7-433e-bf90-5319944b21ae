'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('Users', 'clerkId', {
      type: Sequelize.STRING,
      allowNull: true,
      unique: true,
      comment: 'Clerk user ID for authentication'
    });

    await queryInterface.addColumn('Users', 'authProvider', {
      type: Sequelize.ENUM('local', 'clerk'),
      defaultValue: 'local',
      comment: 'Authentication provider used'
    });

    // Make password optional for Clerk users
    await queryInterface.changeColumn('Users', 'password', {
      type: Sequelize.STRING,
      allowNull: true,
      comment: 'Password hash - optional when using Clerk'
    });

    // Add index for clerkId
    await queryInterface.addIndex('Users', ['clerkId'], {
      name: 'users_clerk_id_index',
      unique: true
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeIndex('Users', 'users_clerk_id_index');
    await queryInterface.removeColumn('Users', 'clerkId');
    await queryInterface.removeColumn('Users', 'authProvider');
    
    // Revert password to required
    await queryInterface.changeColumn('Users', 'password', {
      type: Sequelize.STRING,
      allowNull: false
    });
  }
};
