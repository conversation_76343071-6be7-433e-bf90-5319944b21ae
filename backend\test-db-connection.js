const { sequelize } = require('./src/db/models');

async function testConnection() {
  try {
    console.log('Testing database connection...');
    await sequelize.authenticate();
    console.log('✅ Database connection has been established successfully.');
    
    // Get the database name
    const [results] = await sequelize.query('SELECT current_database() as db_name');
    console.log(`Connected to database: ${results[0].db_name}`);
    
    // List all tables
    const [tables] = await sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    if (tables.length === 0) {
      console.log('No tables found in the database. You need to create the tables first.');
      console.log('Please follow the instructions in DATABASE-SETUP-GUIDE.md');
    } else {
      console.log(`Found ${tables.length} tables in the database:`);
      tables.forEach((table, index) => {
        console.log(`${index + 1}. ${table.table_name}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Unable to connect to the database:', error);
  } finally {
    await sequelize.close();
  }
}

testConnection();
