const { body, param } = require('express-validator');
const { validate } = require('../middlewares/validation.middleware');

exports.validatePaymentIntent = [
  body('orderId')
    .notEmpty().withMessage('Order ID is required')
    .isUUID().withMessage('Order ID must be a valid UUID'),
  
  validate
];

exports.validateRefund = [
  param('orderId')
    .isUUID().withMessage('Order ID must be a valid UUID'),
  
  body('amount')
    .notEmpty().withMessage('Refund amount is required')
    .isFloat({ min: 0.01 }).withMessage('Refund amount must be greater than 0'),
  
  body('reason')
    .optional()
    .isString().withMessage('Reason must be a string'),
  
  validate
];
