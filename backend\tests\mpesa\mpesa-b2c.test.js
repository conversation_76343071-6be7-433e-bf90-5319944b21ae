/**
 * M-Pesa B2C Backend Tests
 * Comprehensive test suite for M-Pesa B2C integration
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-01-15
 */

const request = require('supertest');
const app = require('../../src/app');
const { sequelize } = require('../../src/config/database');

describe('M-Pesa B2C Backend Tests', () => {
  let authToken;
  let adminToken;
  let testTransactionId;
  let testOrderId;

  beforeAll(async () => {
    // Setup test database
    await sequelize.sync({ force: true });
    
    // Create test users and get tokens
    const userResponse = await request(app)
      .post('/api/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
        phoneNumber: '254708374149'
      });
    
    authToken = userResponse.body.token;

    const adminResponse = await request(app)
      .post('/api/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Admin',
        lastName: 'User',
        phoneNumber: '254708374150',
        role: 'admin'
      });
    
    adminToken = adminResponse.body.token;
    testOrderId = `ORD-${Date.now()}`;
  });

  afterAll(async () => {
    await sequelize.close();
  });

  describe('🔐 Authentication & Authorization', () => {
    test('should require authentication for B2C endpoints', async () => {
      const response = await request(app)
        .post('/api/mpesa/b2c/admin/payment')
        .send({
          phoneNumber: '254708374149',
          amount: 100
        });

      expect(response.status).toBe(401);
      expect(response.body.message).toContain('authentication');
    });

    test('should require admin role for admin endpoints', async () => {
      const response = await request(app)
        .post('/api/mpesa/b2c/admin/payment')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          phoneNumber: '254708374149',
          amount: 100
        });

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('admin');
    });
  });

  describe('📱 Phone Number Validation', () => {
    test('should validate Kenyan phone numbers correctly', async () => {
      const testCases = [
        { phone: '254708374149', valid: true },
        { phone: '0708374149', valid: true },
        { phone: '+254708374149', valid: true },
        { phone: '708374149', valid: true },
        { phone: '1234567890', valid: false },
        { phone: '254123456', valid: false },
        { phone: '', valid: false }
      ];

      for (const testCase of testCases) {
        const response = await request(app)
          .post('/api/mpesa/b2c/utility/validate-phone')
          .send({ phoneNumber: testCase.phone });

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.data.isValid).toBe(testCase.valid);
        
        if (testCase.valid) {
          expect(response.body.data.formatted).toMatch(/^254\d{9}$/);
        }
      }
    });

    test('should provide masked phone numbers', async () => {
      const response = await request(app)
        .post('/api/mpesa/b2c/utility/validate-phone')
        .send({ phoneNumber: '254708374149' });

      expect(response.status).toBe(200);
      expect(response.body.data.masked).toBe('****4149');
    });
  });

  describe('💰 Amount Validation', () => {
    test('should validate transaction amounts', async () => {
      const testCases = [
        { amount: 10, valid: true },
        { amount: 50000, valid: true },
        { amount: 5, valid: false }, // Below minimum
        { amount: 300000, valid: false }, // Above maximum
        { amount: -100, valid: false }, // Negative
        { amount: 0, valid: false } // Zero
      ];

      for (const testCase of testCases) {
        const response = await request(app)
          .post('/api/mpesa/b2c/utility/validate-amount')
          .send({ amount: testCase.amount });

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.data.isValid).toBe(testCase.valid);
      }
    });

    test('should calculate transaction fees correctly', async () => {
      const response = await request(app)
        .get('/api/mpesa/b2c/utility/fee-calculator/1000');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('fee');
      expect(response.body.data).toHaveProperty('total');
      expect(typeof response.body.data.fee).toBe('number');
    });
  });

  describe('🏪 Business Hours & Service Health', () => {
    test('should check business hours', async () => {
      const response = await request(app)
        .get('/api/mpesa/b2c/utility/business-hours');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('isOpen');
      expect(response.body.data).toHaveProperty('currentTime');
      expect(response.body.data).toHaveProperty('businessHours');
    });

    test('should check service health', async () => {
      const response = await request(app)
        .get('/api/mpesa/b2c/utility/health');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('status');
      expect(response.body.data).toHaveProperty('timestamp');
    });
  });

  describe('💳 Admin Payment Processing', () => {
    test('should process admin payment successfully', async () => {
      const paymentData = {
        phoneNumber: '254708374149',
        amount: 1000,
        transactionType: 'GENERAL',
        remarks: 'Test payment',
        occasion: 'Testing',
        orderId: testOrderId
      };

      const response = await request(app)
        .post('/api/mpesa/b2c/admin/payment')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(paymentData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('transactionId');
      expect(response.body.data).toHaveProperty('conversationId');
      expect(response.body.data.status).toBe('SUBMITTED');

      testTransactionId = response.body.data.transactionId;
    });

    test('should validate payment data', async () => {
      const invalidData = {
        phoneNumber: 'invalid',
        amount: -100
      };

      const response = await request(app)
        .post('/api/mpesa/b2c/admin/payment')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });
  });

  describe('💸 Refund Processing', () => {
    test('should process refund successfully', async () => {
      const refundData = {
        orderId: testOrderId,
        phoneNumber: '254708374149',
        amount: 500,
        reason: 'Customer request'
      };

      const response = await request(app)
        .post('/api/mpesa/b2c/admin/refund')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(refundData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('transactionId');
      expect(response.body.data.type).toBe('REFUND');
    });

    test('should prevent duplicate refunds', async () => {
      const refundData = {
        orderId: testOrderId,
        phoneNumber: '254708374149',
        amount: 500,
        reason: 'Duplicate test'
      };

      const response = await request(app)
        .post('/api/mpesa/b2c/admin/refund')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(refundData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('already processed');
    });
  });

  describe('📊 User Transaction Management', () => {
    test('should get user transactions', async () => {
      const response = await request(app)
        .get('/api/mpesa/b2c/user/my-transactions')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('transactions');
      expect(response.body.data).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data.transactions)).toBe(true);
    });

    test('should filter transactions by status', async () => {
      const response = await request(app)
        .get('/api/mpesa/b2c/user/my-transactions?status=SUBMITTED')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      
      if (response.body.data.transactions.length > 0) {
        response.body.data.transactions.forEach(transaction => {
          expect(transaction.status).toBe('SUBMITTED');
        });
      }
    });

    test('should get specific transaction', async () => {
      if (testTransactionId) {
        const response = await request(app)
          .get(`/api/mpesa/b2c/user/my-transaction/${testTransactionId}`)
          .set('Authorization', `Bearer ${authToken}`);

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.data.id).toBe(testTransactionId);
      }
    });

    test('should get user refunds', async () => {
      const response = await request(app)
        .get('/api/mpesa/b2c/user/my-refunds')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('refunds');
      expect(Array.isArray(response.body.data.refunds)).toBe(true);
    });

    test('should get transaction summary', async () => {
      const response = await request(app)
        .get('/api/mpesa/b2c/user/transaction-summary')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('totalTransactions');
      expect(response.body.data).toHaveProperty('completedTransactions');
      expect(response.body.data).toHaveProperty('totalAmount');
    });
  });

  describe('🔧 Utility Functions', () => {
    test('should get transaction types', async () => {
      const response = await request(app)
        .get('/api/mpesa/b2c/utility/transaction-types');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data).toContain('REFUND');
      expect(response.body.data).toContain('GENERAL');
    });

    test('should get status types', async () => {
      const response = await request(app)
        .get('/api/mpesa/b2c/utility/status-types');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data).toContain('SUBMITTED');
      expect(response.body.data).toContain('COMPLETED');
    });
  });

  describe('🚫 Error Handling', () => {
    test('should handle invalid transaction ID', async () => {
      const response = await request(app)
        .get('/api/mpesa/b2c/user/my-transaction/invalid-id')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });

    test('should handle malformed requests', async () => {
      const response = await request(app)
        .post('/api/mpesa/b2c/admin/payment')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ invalid: 'data' });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });

    test('should handle unauthorized access to other user data', async () => {
      // Create another user
      const otherUserResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'Other',
          lastName: 'User',
          phoneNumber: '254708374151'
        });

      const otherToken = otherUserResponse.body.token;

      // Try to access first user's transaction with other user's token
      if (testTransactionId) {
        const response = await request(app)
          .get(`/api/mpesa/b2c/user/my-transaction/${testTransactionId}`)
          .set('Authorization', `Bearer ${otherToken}`);

        expect(response.status).toBe(404);
      }
    });
  });

  describe('📈 Performance & Pagination', () => {
    test('should handle pagination correctly', async () => {
      const response = await request(app)
        .get('/api/mpesa/b2c/user/my-transactions?page=1&limit=5')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.pagination).toHaveProperty('page');
      expect(response.body.data.pagination).toHaveProperty('limit');
      expect(response.body.data.pagination).toHaveProperty('total');
      expect(response.body.data.pagination).toHaveProperty('totalPages');
      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(5);
    });

    test('should handle large page numbers gracefully', async () => {
      const response = await request(app)
        .get('/api/mpesa/b2c/user/my-transactions?page=999&limit=10')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.transactions).toHaveLength(0);
    });
  });
});
