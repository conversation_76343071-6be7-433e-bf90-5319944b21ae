/**
 * Test script to debug checkout validation issues
 * Run this with: node test-checkout.js
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

// Test order data that matches the frontend structure
const testOrderData = {
  items: [
    {
      productId: "1", // Using a simple ID for testing
      quantity: 1,
      price: 1000,
      productName: "Test Laptop",
      productSku: "TEST-001",
      productImage: "test.jpg"
    }
  ],
  shippingAddress: {
    fullName: "<PERSON> Do<PERSON>",
    email: "<EMAIL>",
    phone: "0712345678",
    addressLine1: "123 Test Street",
    city: "Nairobi",
    state: "Nairobi",
    postalCode: "00100",
    country: "Kenya"
  },
  paymentMethod: "mpesa",
  subtotal: 1000,
  tax: 160,
  shippingCost: 0,
  totalAmount: 1160,
  email: "<EMAIL>", // Root level email
  phone: "0712345678"
};

async function testOrderCreation() {
  try {
    console.log('🧪 Testing order creation...');
    console.log('📤 Sending order data:', JSON.stringify(testOrderData, null, 2));
    
    const response = await axios.post(
      `${API_BASE_URL}/orders`,
      testOrderData,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-session-id': 'test-session-123' // For guest checkout
        }
      }
    );
    
    console.log('✅ Order created successfully!');
    console.log('📥 Response:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('❌ Order creation failed!');
    
    if (error.response) {
      console.log('📊 Status:', error.response.status);
      console.log('📋 Error data:', JSON.stringify(error.response.data, null, 2));
      
      // Check for validation errors
      if (error.response.data.errors) {
        console.log('\n🔍 Validation errors:');
        error.response.data.errors.forEach((err, index) => {
          console.log(`  ${index + 1}. Field: ${err.field || 'unknown'}`);
          console.log(`     Message: ${err.message}`);
          console.log(`     Value: ${err.value || 'undefined'}`);
        });
      }
    } else if (error.request) {
      console.log('🌐 Network error - no response received');
      console.log('Request details:', error.request);
    } else {
      console.log('⚠️ Error:', error.message);
    }
  }
}

// Test server connectivity first
async function testServerConnection() {
  try {
    console.log('🔗 Testing server connection...');
    const response = await axios.get(`${API_BASE_URL.replace('/api', '')}/health`);
    console.log('✅ Server is responding:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Server connection failed:', error.message);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting checkout validation tests...\n');
  
  // Test server connection
  const serverOk = await testServerConnection();
  if (!serverOk) {
    console.log('❌ Cannot proceed - server is not responding');
    return;
  }
  
  console.log(''); // Empty line for readability
  
  // Test order creation
  await testOrderCreation();
}

// Run the tests
runTests().catch(console.error);
