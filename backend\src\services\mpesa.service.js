const axios = require('axios');
const crypto = require('crypto');
const moment = require('moment');

/**
 * M-Pesa Daraja API Service
 * Handles all M-Pesa payment operations for G20Shop
 */
class MpesaService {
  constructor() {
    this.environment = process.env.MPESA_ENVIRONMENT || 'sandbox';
    this.consumerKey = process.env.MPESA_CONSUMER_KEY;
    this.consumerSecret = process.env.MPESA_CONSUMER_SECRET;
    this.businessShortCode = process.env.MPESA_BUSINESS_SHORT_CODE;
    this.passkey = process.env.MPESA_LIPA_NA_MPESA_ONLINE_PASSKEY;
    this.callbackUrl = process.env.MPESA_CALLBACK_URL;
    this.confirmationUrl = process.env.MPESA_CONFIRMATION_URL;
    this.validationUrl = process.env.MPESA_VALIDATION_URL;

    // Set base URL based on environment
    this.baseUrl = this.environment === 'production'
      ? 'https://api.safaricom.co.ke'
      : 'https://sandbox.safaricom.co.ke';

    // Token cache
    this.accessToken = null;
    this.tokenExpiry = null;
  }

  /**
   * Generate access token for API authentication
   */
  async generateAccessToken() {
    try {
      // Check if we have a valid cached token
      if (this.accessToken && this.tokenExpiry && moment().isBefore(this.tokenExpiry)) {
        return this.accessToken;
      }

      const auth = Buffer.from(`${this.consumerKey}:${this.consumerSecret}`).toString('base64');

      const response = await axios.get(
        `${this.baseUrl}/oauth/v1/generate?grant_type=client_credentials`,
        {
          headers: {
            'Authorization': `Basic ${auth}`,
            'Content-Type': 'application/json'
          }
        }
      );

      this.accessToken = response.data.access_token;
      // Set expiry to 5 minutes before actual expiry for safety
      this.tokenExpiry = moment().add(response.data.expires_in - 300, 'seconds');

      console.log('✅ M-Pesa access token generated successfully');
      return this.accessToken;
    } catch (error) {
      console.error('❌ Failed to generate M-Pesa access token:', error.response?.data || error.message);
      throw new Error('Failed to authenticate with M-Pesa API');
    }
  }

  /**
   * Generate password for STK Push
   */
  generatePassword() {
    const timestamp = moment().format('YYYYMMDDHHmmss');
    const password = Buffer.from(`${this.businessShortCode}${this.passkey}${timestamp}`).toString('base64');
    return { password, timestamp };
  }

  /**
   * Mock STK Push for development/testing
   */
  mockSTKPush(phoneNumber, amount, accountReference, transactionDesc, orderId) {
    console.log('🧪 Mock STK Push:', {
      phone: phoneNumber,
      amount,
      reference: accountReference,
      orderId
    });

    // Generate mock response similar to real M-Pesa response
    const mockMerchantRequestId = `mock-merchant-${Date.now()}`;
    const mockCheckoutRequestId = `mock-checkout-${Date.now()}`;

    return {
      success: true,
      data: {
        MerchantRequestID: mockMerchantRequestId,
        CheckoutRequestID: mockCheckoutRequestId,
        ResponseCode: "0",
        ResponseDescription: "Success. Request accepted for processing",
        CustomerMessage: "Success. Request accepted for processing"
      },
      merchantRequestId: mockMerchantRequestId,
      checkoutRequestId: mockCheckoutRequestId,
      responseCode: "0",
      responseDescription: "Success. Request accepted for processing",
      customerMessage: "Success. Request accepted for processing"
    };
  }

  /**
   * Initiate STK Push payment request
   */
  async initiateSTKPush(phoneNumber, amount, accountReference, transactionDesc, orderId) {
    try {
      // Check if we're in development mode and use mock for testing
      if (process.env.NODE_ENV === 'development' && process.env.MPESA_USE_MOCK === 'true') {
        console.log('🧪 Using mock M-Pesa service for development');
        return this.mockSTKPush(phoneNumber, amount, accountReference, transactionDesc, orderId);
      }

      const accessToken = await this.generateAccessToken();
      const { password, timestamp } = this.generatePassword();

      // Format phone number (remove leading 0 and add 254)
      const formattedPhone = phoneNumber.startsWith('0')
        ? `254${phoneNumber.substring(1)}`
        : phoneNumber.startsWith('254')
        ? phoneNumber
        : `254${phoneNumber}`;

      const requestBody = {
        BusinessShortCode: this.businessShortCode,
        Password: password,
        Timestamp: timestamp,
        TransactionType: 'CustomerPayBillOnline',
        Amount: Math.round(amount), // Ensure amount is integer
        PartyA: formattedPhone,
        PartyB: this.businessShortCode,
        PhoneNumber: formattedPhone,
        CallBackURL: `${this.callbackUrl}/${orderId}`,
        AccountReference: accountReference || `G20SHOP-${orderId}`,
        TransactionDesc: transactionDesc || `Payment for G20Shop Order ${orderId}`
      };

      console.log('🚀 Initiating STK Push:', {
        phone: formattedPhone,
        amount,
        reference: requestBody.AccountReference
      });

      const response = await axios.post(
        `${this.baseUrl}/mpesa/stkpush/v1/processrequest`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('✅ STK Push initiated successfully:', response.data);
      return {
        success: true,
        data: response.data,
        merchantRequestId: response.data.MerchantRequestID,
        checkoutRequestId: response.data.CheckoutRequestID,
        responseCode: response.data.ResponseCode,
        responseDescription: response.data.ResponseDescription,
        customerMessage: response.data.CustomerMessage
      };
    } catch (error) {
      console.error('❌ STK Push failed - Full error details:');
      console.error('Error message:', error.message);
      console.error('Error response status:', error.response?.status);
      console.error('Error response headers:', error.response?.headers);
      console.error('Error response data:', JSON.stringify(error.response?.data, null, 2));
      console.error('Request config:', JSON.stringify({
        url: error.config?.url,
        method: error.config?.method,
        headers: error.config?.headers,
        data: error.config?.data
      }, null, 2));

      return {
        success: false,
        error: error.response?.data || error.message,
        message: 'Failed to initiate payment. Please try again.',
        details: {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        }
      };
    }
  }

  /**
   * Query STK Push status
   */
  async querySTKPushStatus(checkoutRequestId) {
    try {
      const accessToken = await this.generateAccessToken();
      const { password, timestamp } = this.generatePassword();

      const requestBody = {
        BusinessShortCode: this.businessShortCode,
        Password: password,
        Timestamp: timestamp,
        CheckoutRequestID: checkoutRequestId
      };

      const response = await axios.post(
        `${this.baseUrl}/mpesa/stkpushquery/v1/query`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        success: true,
        data: response.data,
        resultCode: response.data.ResultCode,
        resultDesc: response.data.ResultDesc
      };
    } catch (error) {
      console.error('❌ STK Push query failed:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data || error.message
      };
    }
  }

  /**
   * Register C2B URLs
   */
  async registerC2BUrls() {
    try {
      const accessToken = await this.generateAccessToken();

      const requestBody = {
        ShortCode: this.businessShortCode,
        ResponseType: 'Completed', // or 'Cancelled'
        ConfirmationURL: this.confirmationUrl,
        ValidationURL: this.validationUrl
      };

      const response = await axios.post(
        `${this.baseUrl}/mpesa/c2b/v1/registerurl`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('✅ C2B URLs registered successfully:', response.data);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('❌ C2B URL registration failed:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data || error.message
      };
    }
  }

  /**
   * Simulate C2B payment (sandbox only)
   */
  async simulateC2BPayment(phoneNumber, amount, billRefNumber) {
    try {
      if (this.environment !== 'sandbox') {
        throw new Error('C2B simulation is only available in sandbox environment');
      }

      const accessToken = await this.generateAccessToken();

      const formattedPhone = phoneNumber.startsWith('0')
        ? `254${phoneNumber.substring(1)}`
        : phoneNumber;

      const requestBody = {
        ShortCode: this.businessShortCode,
        CommandID: 'CustomerPayBillOnline',
        Amount: Math.round(amount),
        Msisdn: formattedPhone,
        BillRefNumber: billRefNumber || `G20SHOP-${Date.now()}`
      };

      const response = await axios.post(
        `${this.baseUrl}/mpesa/c2b/v1/simulate`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('❌ C2B simulation failed:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data || error.message
      };
    }
  }

  /**
   * Process B2C payment (for refunds)
   */
  async processB2CPayment(phoneNumber, amount, remarks, occasion) {
    try {
      const accessToken = await this.generateAccessToken();

      const formattedPhone = phoneNumber.startsWith('0')
        ? `254${phoneNumber.substring(1)}`
        : phoneNumber;

      const requestBody = {
        InitiatorName: process.env.MPESA_INITIATOR_NAME,
        SecurityCredential: process.env.MPESA_SECURITY_CREDENTIAL,
        CommandID: 'BusinessPayment', // or 'SalaryPayment', 'PromotionPayment'
        Amount: Math.round(amount),
        PartyA: this.businessShortCode,
        PartyB: formattedPhone,
        Remarks: remarks || 'G20Shop Refund',
        QueueTimeOutURL: `${this.callbackUrl}/b2c/timeout`,
        ResultURL: `${this.callbackUrl}/b2c/result`,
        Occasion: occasion || 'Refund Processing'
      };

      const response = await axios.post(
        `${this.baseUrl}/mpesa/b2c/v1/paymentrequest`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        success: true,
        data: response.data,
        conversationId: response.data.ConversationID,
        originatorConversationId: response.data.OriginatorConversationID
      };
    } catch (error) {
      console.error('❌ B2C payment failed:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data || error.message
      };
    }
  }

  /**
   * Query transaction status
   */
  async queryTransactionStatus(transactionId) {
    try {
      const accessToken = await this.generateAccessToken();

      const requestBody = {
        Initiator: process.env.MPESA_INITIATOR_NAME,
        SecurityCredential: process.env.MPESA_SECURITY_CREDENTIAL,
        CommandID: 'TransactionStatusQuery',
        TransactionID: transactionId,
        PartyA: this.businessShortCode,
        IdentifierType: '4', // Organization short code
        ResultURL: `${this.callbackUrl}/transaction-status`,
        QueueTimeOutURL: `${this.callbackUrl}/transaction-status/timeout`,
        Remarks: 'Transaction Status Query',
        Occasion: 'Transaction Status Check'
      };

      const response = await axios.post(
        `${this.baseUrl}/mpesa/transactionstatus/v1/query`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('❌ Transaction status query failed:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data || error.message
      };
    }
  }

  /**
   * Validate callback authenticity
   */
  validateCallback(callbackData, expectedOrderId) {
    try {
      // Basic validation - you might want to add more security checks
      if (!callbackData || !callbackData.Body) {
        return { valid: false, reason: 'Invalid callback structure' };
      }

      // Check if this is an STK Push callback
      if (callbackData.Body.stkCallback) {
        const stkCallback = callbackData.Body.stkCallback;
        return {
          valid: true,
          type: 'STK_PUSH',
          data: stkCallback,
          resultCode: stkCallback.ResultCode,
          resultDesc: stkCallback.ResultDesc,
          merchantRequestId: stkCallback.MerchantRequestID,
          checkoutRequestId: stkCallback.CheckoutRequestID
        };
      }

      // Check if this is a C2B callback
      if (callbackData.TransactionType) {
        return {
          valid: true,
          type: 'C2B',
          data: callbackData
        };
      }

      return { valid: false, reason: 'Unknown callback type' };
    } catch (error) {
      console.error('❌ Callback validation failed:', error);
      return { valid: false, reason: 'Validation error' };
    }
  }
}

module.exports = new MpesaService();
