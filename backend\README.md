# TechGear Backend API

Backend API for TechGear e-commerce platform, specializing in laptop and PC accessories with repair and maintenance services.

## Features

- Product management with categories, images, and inventory tracking
- Shopping cart functionality for both guest and authenticated users
- Wishlist management for authenticated users
- User authentication and authorization
- Order processing and management
- Repair ticket tracking system

## Tech Stack

- Node.js
- Express.js
- PostgreSQL
- Sequelize ORM
- JWT Authentication

## Directory Structure

```
backend/
├── src/                      # Source code
│   ├── api/                  # API routes and controllers
│   │   ├── controllers/      # Route controllers
│   │   ├── middlewares/      # Custom middlewares
│   │   ├── routes/           # Route definitions
│   │   └── validators/       # Request validators
│   ├── config/               # Configuration files
│   ├── db/                   # Database related files
│   │   ├── models/           # Database models
│   │   ├── migrations/       # Database migrations
│   │   ├── seeders/          # Database seeders
│   │   └── repositories/     # Data access layer
│   ├── services/             # Business logic services
│   ├── utils/                # Utility functions
│   ├── app.js                # Express app setup
│   └── server.js             # Server entry point
├── tests/                    # Test files
├── .env                      # Environment variables
├── package.json              # Backend dependencies
└── ...
```

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Navigate to the backend directory:
   ```
   cd backend
   ```
3. Install dependencies:
   ```
   npm install
   ```
4. Create a `.env` file based on `.env.example`:
   ```
   cp .env.example .env
   ```
5. Update the `.env` file with your database credentials and other configuration

### Database Setup

There are two ways to set up the database:

#### Option 1: Using pgAdmin (Recommended)

1. Open pgAdmin and create a new database named `techgear`
2. Open the Query Tool for the `techgear` database
3. Open the file `database-setup.sql` from this project
4. Execute the SQL script to create all tables and indexes
5. For detailed instructions, see `DATABASE-SETUP-GUIDE.md`

#### Option 2: Using Sequelize Migrations

If you prefer to use Sequelize migrations:

1. Run database migrations:
   ```
   npx sequelize-cli db:migrate
   ```
2. (Optional) Seed the database with initial data:
   ```
   npm run db:seed
   ```

### Testing the Database Connection

To verify that your database is properly configured:

```
npm run test:db
```

This will test the connection to the database and list all available tables.

### Running the Server

Development mode:
```
npm run dev
```

Production mode:
```
npm start
```

## API Documentation

### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login and get access token
- `POST /api/auth/refresh-token` - Refresh access token
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile
- `POST /api/auth/change-password` - Change password

### Products

- `GET /api/products` - Get all products (with filtering and pagination)
- `GET /api/products/:identifier` - Get product by ID or slug
- `GET /api/products/:id/related` - Get related products
- `POST /api/products` - Create a new product (admin only)
- `PUT /api/products/:id` - Update a product (admin only)
- `DELETE /api/products/:id` - Delete a product (admin only)
- `POST /api/products/:id/images` - Upload product images (admin only)

### Categories

- `GET /api/products/categories` - Get all product categories
- `GET /api/products/categories/:id` - Get category by ID
- `POST /api/products/categories` - Create a new category (admin only)
- `PUT /api/products/categories/:id` - Update a category (admin only)
- `DELETE /api/products/categories/:id` - Delete a category (admin only)

### Cart

- `GET /api/cart` - Get user's cart
- `POST /api/cart/items` - Add item to cart
- `PUT /api/cart/items/:id` - Update cart item quantity
- `DELETE /api/cart/items/:id` - Remove item from cart
- `DELETE /api/cart` - Clear cart

### Wishlist

- `GET /api/wishlist` - Get user's wishlists
- `GET /api/wishlist/:id` - Get a specific wishlist
- `POST /api/wishlist` - Create a new wishlist
- `PUT /api/wishlist/:id` - Update a wishlist
- `DELETE /api/wishlist/:id` - Delete a wishlist
- `POST /api/wishlist/:id/items` - Add item to wishlist
- `DELETE /api/wishlist/:id/items/:itemId` - Remove item from wishlist

### Orders

- `POST /api/orders` - Create a new order
- `GET /api/orders/user` - Get user's orders
- `GET /api/orders/:id` - Get order by ID
- `POST /api/orders/:id/cancel` - Cancel an order
- `PATCH /api/orders/:id/status` - Update order status (admin only)
- `GET /api/orders` - Get all orders (admin only)

### Payments

- `POST /api/payments/create-intent` - Create payment intent
- `POST /api/payments/webhook` - Process payment webhook
- `POST /api/payments/refund/:orderId` - Process refund (admin only)

### Reviews

- `POST /api/reviews` - Create a new review
- `GET /api/reviews/:id` - Get review by ID
- `PUT /api/reviews/:id` - Update a review
- `DELETE /api/reviews/:id` - Delete a review
- `GET /api/reviews/product/:productId` - Get reviews for a product
- `GET /api/reviews/user/me` - Get reviews by current user
- `POST /api/reviews/:id/helpful` - Mark review as helpful
- `PATCH /api/reviews/:id/moderate` - Moderate a review (admin only)

### Coupons

- `POST /api/coupons/validate` - Validate a coupon code
- `POST /api/coupons` - Create a new coupon (admin only)
- `GET /api/coupons` - Get all coupons (admin only)
- `GET /api/coupons/:id` - Get coupon by ID (admin only)
- `PUT /api/coupons/:id` - Update a coupon (admin only)
- `DELETE /api/coupons/:id` - Delete a coupon (admin only)

## Testing

### Automated Tests

Run all tests:
```bash
npm run test:all
```

Run API tests only:
```bash
npm run test:api
```

Run model tests only:
```bash
npm run test:models
```

Run database connection test:
```bash
npm run test:db
```

### Manual Testing

For detailed instructions on how to test the backend manually, see `TESTING.md`.

## Code Style

- Use consistent indentation (2 spaces)
- Follow the ESLint configuration
- Add meaningful comments for complex logic
- Use async/await for asynchronous operations

## License

This project is licensed under the MIT License.
