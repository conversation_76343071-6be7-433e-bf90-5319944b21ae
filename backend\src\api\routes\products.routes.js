const express = require('express');
const router = express.Router();
const productsController = require('../controllers/products.controller');

// Public routes - no authentication required
router.get('/', productsController.getAllProducts);
router.get('/categories', productsController.getAllCategories);
router.get('/:identifier', productsController.getProduct);
router.get('/:id/related', productsController.getRelatedProducts);

// Note: Admin product management routes are now in /admin/inventory

module.exports = router;
