{"name": "techgear-backend", "version": "1.0.0", "description": "Backend API for TechGear e-commerce shop", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "start:test": "node start-test-server.js", "lint": "eslint .", "test": "jest", "test:api": "node test-api.js", "test:models": "node test-models.js", "test:all": "node run-tests.js", "test:db": "node test-db-connection.js", "db:seed": "node src/db/seeders/seed-test-data.js", "db:seed:products": "node src/db/seeders/add-test-products.js", "db:check": "node scripts/check-products.js", "db:migrate": "sequelize-cli db:migrate", "db:migrate:undo": "sequelize-cli db:migrate:undo", "db:reset": "sequelize-cli db:drop && sequelize-cli db:create && npm run db:migrate && npm run db:seed"}, "dependencies": {"@clerk/clerk-sdk-node": "^4.13.14", "axios": "^1.9.0", "bcrypt": "^5.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.0.1", "helmet": "^6.1.5", "jsonwebtoken": "^9.0.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^4.0.7", "pg": "^8.16.0", "pg-hstore": "^2.3.4", "sequelize": "^6.31.0", "slugify": "^1.6.6", "sqlite3": "^5.1.7", "stripe": "^18.1.1", "uuid": "^9.0.0", "winston": "^3.8.2"}, "devDependencies": {"eslint": "^8.39.0", "jest": "^29.5.0", "nodemon": "^2.0.22", "sequelize-cli": "^6.6.0", "supertest": "^6.3.3"}}