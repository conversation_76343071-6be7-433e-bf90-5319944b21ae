const {
  Order,
  OrderItem,
  Product,
  User,
  Cart,
  CartItem,
  Payment,
  Coupon,
  CouponUsage,
  ProductImage
} = require('../db/models');
const { NotFoundError, ValidationError } = require('../utils/errors');
const cartService = require('./cart.service');
const couponService = require('./coupon.service');
const { sequelize } = require('../db/models');

/**
 * Create a new order from cart
 */
exports.createOrder = async (orderData, userId, sessionId) => {
  // Start a transaction
  const transaction = await sequelize.transaction();

  try {
    // Get cart with items
    const cart = await cartService.getCart(userId, sessionId);

    if (!cart || !cart.items || cart.items.length === 0) {
      throw new ValidationError('Cart is empty');
    }

    // Validate stock availability for all items
    for (const item of cart.items) {
      const product = item.Product;

      if (!product.isActive) {
        throw new ValidationError(`Product "${product.name}" is no longer available`);
      }

      if (product.stockQuantity < item.quantity) {
        throw new ValidationError(`Not enough stock available for "${product.name}"`);
      }
    }

    // Apply coupon if provided
    let discount = 0;
    let coupon = null;

    if (orderData.couponCode) {
      coupon = await couponService.validateCoupon(
        orderData.couponCode,
        cart.totalAmount,
        userId,
        cart.items.map(item => item.productId)
      );

      discount = coupon.calculatedDiscount;
    }

    // Calculate order totals
    const subtotal = orderData.subtotal || parseFloat(cart.totalAmount);
    const shippingCost = orderData.shippingCost || 0;
    const tax = orderData.tax || 0;
    const totalAmount = orderData.totalAmount || (subtotal + shippingCost + tax - discount);

    // Generate order number
    const generateOrderNumber = () => {
      const date = new Date();
      const year = date.getFullYear().toString().substr(-2);
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
      return `ORD-${year}${month}${day}-${random}`;
    };

    // Create order
    const order = await Order.create({
      userId,
      orderNumber: generateOrderNumber(),
      totalAmount,
      subtotal,
      tax,
      shippingCost,
      discount,
      shippingAddress: orderData.shippingAddress,
      billingAddress: orderData.billingAddress || orderData.shippingAddress,
      paymentMethod: orderData.paymentMethod,
      notes: orderData.notes,
      guestEmail: !userId ? orderData.email : null,
      guestPhone: !userId ? orderData.phone : null,
      couponCode: coupon ? coupon.code : null
    }, { transaction });

    // Create order items
    await Promise.all(
      cart.items.map(async (item) => {
        const product = item.Product;
        const primaryImage = product.images && product.images.length > 0
          ? product.images[0].imageUrl
          : null;

        // Create order item
        await OrderItem.create({
          orderId: order.id,
          productId: product.id,
          quantity: item.quantity,
          price: item.price,
          totalPrice: item.totalPrice,
          productName: product.name,
          productSku: product.sku,
          productImage: primaryImage
        }, { transaction });

        // Update product stock
        await Product.update(
          { stockQuantity: product.stockQuantity - item.quantity },
          { where: { id: product.id }, transaction }
        );
      })
    );

    // Record coupon usage if applicable
    if (coupon) {
      await CouponUsage.create({
        couponId: coupon.id,
        userId: userId || null,
        orderId: order.id,
        discountAmount: discount,
      }, { transaction });

      // Update coupon usage count
      await Coupon.increment('usageCount', {
        where: { id: coupon.id },
        transaction
      });
    }

    // Don't clear cart yet - wait for payment confirmation
    // Cart will be cleared when payment is successful

    // Commit transaction
    await transaction.commit();

    // Return the complete order
    return this.getOrderById(order.id);
  } catch (error) {
    // Rollback transaction on error
    await transaction.rollback();
    throw error;
  }
};

/**
 * Get order by ID
 */
exports.getOrderById = async (id, userId = null) => {
  const order = await Order.findByPk(id, {
    include: [
      {
        model: OrderItem,
        as: 'items',
        include: [
          {
            model: Product,
            include: [
              {
                model: ProductImage,
                as: 'images',
                where: { isPrimary: true },
                required: false,
                limit: 1
              }
            ]
          }
        ]
      },
      {
        model: Payment,
        as: 'payments'
      },
      {
        model: User,
        attributes: ['id', 'name', 'email', 'phone']
      }
    ]
  });

  if (!order) {
    throw new NotFoundError('Order not found');
  }

  // If userId is provided, verify ownership
  if (userId && order.userId !== userId) {
    throw new NotFoundError('Order not found');
  }

  return order;
};

/**
 * Get orders for a user
 */
exports.getUserOrders = async (userId, { page = 1, limit = 10, status }) => {
  const offset = (page - 1) * limit;
  const where = { userId };

  if (status) {
    where.status = status;
  }

  const { count, rows } = await Order.findAndCountAll({
    where,
    include: [
      {
        model: OrderItem,
        as: 'items',
        include: [
          {
            model: Product,
            attributes: ['id', 'name', 'slug'],
            include: [
              {
                model: ProductImage,
                as: 'images',
                where: { isPrimary: true },
                required: false,
                limit: 1,
                attributes: ['imageUrl']
              }
            ]
          }
        ]
      }
    ],
    order: [['createdAt', 'DESC']],
    limit,
    offset
  });

  return {
    orders: rows,
    totalItems: count,
    totalPages: Math.ceil(count / limit),
    currentPage: page
  };
};

/**
 * Update order status
 */
exports.updateOrderStatus = async (id, status, userId = null) => {
  const order = await Order.findByPk(id);

  if (!order) {
    throw new NotFoundError('Order not found');
  }

  // If userId is provided, verify ownership
  if (userId && order.userId !== userId) {
    throw new NotFoundError('Order not found');
  }

  // Update status and related timestamps
  const updateData = { status };

  if (status === 'shipped') {
    updateData.shippedAt = new Date();
  } else if (status === 'delivered') {
    updateData.deliveredAt = new Date();
  } else if (status === 'cancelled') {
    updateData.cancelledAt = new Date();

    // Restore product stock for cancelled orders
    const orderItems = await OrderItem.findAll({
      where: { orderId: id }
    });

    await Promise.all(
      orderItems.map(async (item) => {
        await Product.increment('stockQuantity', {
          by: item.quantity,
          where: { id: item.productId }
        });
      })
    );
  }

  await order.update(updateData);

  return this.getOrderById(id);
};

/**
 * Clear cart after successful payment
 */
exports.clearCartAfterPayment = async (orderId) => {
  try {
    // Get order details
    const order = await Order.findByPk(orderId);
    if (!order) {
      throw new Error('Order not found');
    }

    // Clear the cart for this user
    await cartService.clearCart(order.userId, null);

    console.log(`✅ Cart cleared for user ${order.userId} after successful payment for order ${orderId}`);
    return true;
  } catch (error) {
    console.error('❌ Error clearing cart after payment:', error);
    throw error;
  }
};

/**
 * Get all orders (admin)
 */
exports.getAllOrders = async ({ page = 1, limit = 10, status, startDate, endDate }) => {
  const offset = (page - 1) * limit;
  const where = {};

  if (status) {
    where.status = status;
  }

  if (startDate && endDate) {
    where.createdAt = {
      [Op.between]: [new Date(startDate), new Date(endDate)]
    };
  } else if (startDate) {
    where.createdAt = {
      [Op.gte]: new Date(startDate)
    };
  } else if (endDate) {
    where.createdAt = {
      [Op.lte]: new Date(endDate)
    };
  }

  const { count, rows } = await Order.findAndCountAll({
    where,
    include: [
      {
        model: User,
        attributes: ['id', 'name', 'email']
      }
    ],
    order: [['createdAt', 'DESC']],
    limit,
    offset
  });

  return {
    orders: rows,
    totalItems: count,
    totalPages: Math.ceil(count / limit),
    currentPage: page
  };
};
