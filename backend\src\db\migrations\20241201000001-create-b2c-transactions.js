'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create b2c_transactions table
    await queryInterface.createTable('b2c_transactions', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      conversationId: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
        comment: 'Our internal conversation ID'
      },
      mpesaConversationId: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'M-Pesa conversation ID from API response'
      },
      originatorConversationId: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'M-Pesa originator conversation ID'
      },
      phoneNumber: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'Customer phone number in 254XXXXXXXXX format'
      },
      amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        comment: 'Transaction amount in KES'
      },
      transactionType: {
        type: Sequelize.ENUM('REFUND', 'SALARY', 'PROMOTION', 'GENERAL'),
        allowNull: false,
        defaultValue: 'GENERAL',
        comment: 'Type of B2C transaction'
      },
      commandId: {
        type: Sequelize.ENUM('BusinessPayment', 'SalaryPayment', 'PromotionPayment'),
        allowNull: false,
        defaultValue: 'BusinessPayment',
        comment: 'M-Pesa command ID for the transaction'
      },
      remarks: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'G20Shop Payment',
        comment: 'Transaction remarks'
      },
      occasion: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'Payment Processing',
        comment: 'Transaction occasion'
      },
      status: {
        type: Sequelize.ENUM('PENDING', 'SUBMITTED', 'COMPLETED', 'FAILED', 'TIMEOUT', 'CANCELLED'),
        allowNull: false,
        defaultValue: 'PENDING',
        comment: 'Transaction status'
      },
      responseCode: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Initial API response code'
      },
      responseDescription: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Initial API response description'
      },
      resultCode: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'Final result code from callback'
      },
      resultDescription: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Final result description from callback'
      },
      mpesaReceiptNumber: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'M-Pesa receipt number for successful transactions'
      },
      transactionAmount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
        comment: 'Actual transaction amount from M-Pesa callback'
      },
      transactionDate: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Transaction completion date from M-Pesa'
      },
      b2cCharges: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
        comment: 'B2C charges deducted'
      },
      receiverPartyName: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Name of the receiver from M-Pesa'
      },
      b2cUtilityBalance: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
        comment: 'B2C utility account balance after transaction'
      },
      b2cWorkingBalance: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
        comment: 'B2C working account balance after transaction'
      },
      orderId: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'orders',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: 'Associated order ID for refunds'
      },
      userId: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'User ID who initiated the transaction'
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Additional transaction metadata'
      },
      callbackData: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Full callback data from M-Pesa'
      },
      initiatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        comment: 'When the transaction was initiated'
      },
      completedAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'When the transaction was completed'
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes for b2c_transactions
    await queryInterface.addIndex('b2c_transactions', ['conversationId'], {
      name: 'idx_b2c_transactions_conversation_id',
      unique: true
    });

    await queryInterface.addIndex('b2c_transactions', ['mpesaConversationId'], {
      name: 'idx_b2c_transactions_mpesa_conversation_id'
    });

    await queryInterface.addIndex('b2c_transactions', ['phoneNumber'], {
      name: 'idx_b2c_transactions_phone_number'
    });

    await queryInterface.addIndex('b2c_transactions', ['status'], {
      name: 'idx_b2c_transactions_status'
    });

    await queryInterface.addIndex('b2c_transactions', ['transactionType'], {
      name: 'idx_b2c_transactions_type'
    });

    await queryInterface.addIndex('b2c_transactions', ['orderId'], {
      name: 'idx_b2c_transactions_order_id'
    });

    await queryInterface.addIndex('b2c_transactions', ['userId'], {
      name: 'idx_b2c_transactions_user_id'
    });

    await queryInterface.addIndex('b2c_transactions', ['initiatedAt'], {
      name: 'idx_b2c_transactions_initiated_at'
    });

    await queryInterface.addIndex('b2c_transactions', ['mpesaReceiptNumber'], {
      name: 'idx_b2c_transactions_receipt_number',
      unique: true,
      where: {
        mpesaReceiptNumber: {
          [Sequelize.Op.ne]: null
        }
      }
    });

    console.log('✅ B2C transactions table created successfully');
  },

  async down(queryInterface, Sequelize) {
    // Drop indexes first
    await queryInterface.removeIndex('b2c_transactions', 'idx_b2c_transactions_conversation_id');
    await queryInterface.removeIndex('b2c_transactions', 'idx_b2c_transactions_mpesa_conversation_id');
    await queryInterface.removeIndex('b2c_transactions', 'idx_b2c_transactions_phone_number');
    await queryInterface.removeIndex('b2c_transactions', 'idx_b2c_transactions_status');
    await queryInterface.removeIndex('b2c_transactions', 'idx_b2c_transactions_type');
    await queryInterface.removeIndex('b2c_transactions', 'idx_b2c_transactions_order_id');
    await queryInterface.removeIndex('b2c_transactions', 'idx_b2c_transactions_user_id');
    await queryInterface.removeIndex('b2c_transactions', 'idx_b2c_transactions_initiated_at');
    await queryInterface.removeIndex('b2c_transactions', 'idx_b2c_transactions_receipt_number');

    // Drop table
    await queryInterface.dropTable('b2c_transactions');

    // Drop ENUMs
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_b2c_transactions_transactionType";');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_b2c_transactions_commandId";');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_b2c_transactions_status";');

    console.log('✅ B2C transactions table dropped successfully');
  }
};
