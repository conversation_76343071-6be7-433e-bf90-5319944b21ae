/**
 * M-Pesa B2C Admin Routes
 * Admin-only routes for B2C operations
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 2024-01-15
 */

const express = require('express');
const router = express.Router();
const b2cController = require('../../../controllers/mpesa/b2c.controller');
const { authenticateAdmin } = require('../../../middlewares/clerk-auth.middleware');
const { validateB2CPayment, validateB2CRefund, validateB2CSalary, validateB2CPromotion } = require('../../../validators/mpesa/b2c.validator');

// Rate limiting for admin B2C endpoints
const rateLimit = require('express-rate-limit');

const adminB2CRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 20, // Allow more requests for admins
  skipSuccessfulRequests: false,
  message: {
    success: false,
    message: 'Too many B2C admin requests, please try again later.',
    error: 'Rate limit exceeded'
  }
});

// Apply admin authentication to all routes
router.use(authenticateAdmin);
router.use(adminB2CRateLimit);

/**
 * @route   POST /api/mpesa/b2c/admin/payment
 * @desc    Process general B2C payment
 * @access  Private (Admin only)
 * @body    { phoneNumber, amount, transactionType, remarks?, occasion?, orderId?, metadata? }
 */
router.post('/payment', validateB2CPayment, b2cController.processPayment);

/**
 * @route   POST /api/mpesa/b2c/admin/refund
 * @desc    Process refund to customer
 * @access  Private (Admin only)
 * @body    { orderId, phoneNumber, amount, reason? }
 */
router.post('/refund', validateB2CRefund, b2cController.processRefund);

/**
 * @route   POST /api/mpesa/b2c/admin/salary
 * @desc    Process salary payment
 * @access  Private (Admin only)
 * @body    { employeePhone, amount, employeeId, payrollPeriod }
 */
router.post('/salary', validateB2CSalary, b2cController.processSalaryPayment);

/**
 * @route   POST /api/mpesa/b2c/admin/promotion
 * @desc    Process promotional payment
 * @access  Private (Admin only)
 * @body    { customerPhone, amount, promotionCode, campaignId }
 */
router.post('/promotion', validateB2CPromotion, b2cController.processPromotionalPayment);

/**
 * @route   GET /api/mpesa/b2c/admin/transactions
 * @desc    Get B2C transaction history with filtering
 * @access  Private (Admin only)
 * @query   { status?, transactionType?, phoneNumber?, startDate?, endDate?, page?, limit? }
 */
router.get('/transactions', b2cController.getTransactionHistory);

/**
 * @route   GET /api/mpesa/b2c/admin/transaction/:id
 * @desc    Get specific B2C transaction details
 * @access  Private (Admin only)
 */
router.get('/transaction/:id', b2cController.getTransactionStatus);

/**
 * @route   GET /api/mpesa/b2c/admin/stats
 * @desc    Get B2C transaction statistics
 * @access  Private (Admin only)
 * @query   { startDate?, endDate? }
 */
router.get('/stats', async (req, res) => {
  try {
    const { B2CTransaction } = require('../../../../db/models');
    const { startDate, endDate } = req.query;

    const stats = await B2CTransaction.getTransactionStats(
      startDate ? new Date(startDate) : null,
      endDate ? new Date(endDate) : null
    );

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('❌ B2C admin stats query failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve B2C statistics'
    });
  }
});

/**
 * @route   POST /api/mpesa/b2c/admin/bulk-payment
 * @desc    Process bulk B2C payments
 * @access  Private (Admin only)
 * @body    { payments: [{ phoneNumber, amount, transactionType?, remarks? }], batchId? }
 */
router.post('/bulk-payment', async (req, res) => {
  try {
    const { payments, batchId } = req.body;
    const B2CService = require('../../../../services/mpesa/b2c/B2CService');
    const B2CValidator = require('../../../../services/mpesa/b2c/validators/B2CValidator');
    const B2CUtils = require('../../../../services/mpesa/b2c/utils/B2CUtils');

    // Validate bulk payment data
    const validation = B2CValidator.validateBulkPaymentData({ payments, batchId });
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        errors: validation.errors,
        message: 'Bulk payment validation failed'
      });
    }

    const actualBatchId = batchId || B2CUtils.generateBatchId();
    const results = [];

    // Process each payment
    for (let i = 0; i < payments.length; i++) {
      const payment = payments[i];
      const result = await B2CService.processB2CPayment({
        ...payment,
        userId: req.user.id,
        metadata: {
          ...payment.metadata,
          batchId: actualBatchId,
          batchIndex: i + 1,
          batchTotal: payments.length
        }
      });

      results.push({
        index: i + 1,
        phoneNumber: B2CUtils.maskPhoneNumber(payment.phoneNumber),
        amount: payment.amount,
        success: result.success,
        transactionId: result.data?.transactionId,
        message: result.message,
        errors: result.errors
      });

      // Add delay between requests to avoid overwhelming the API
      if (i < payments.length - 1) {
        await B2CUtils.sleep(1000); // 1 second delay
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    res.json({
      success: true,
      data: {
        batchId: actualBatchId,
        totalPayments: payments.length,
        successfulPayments: successCount,
        failedPayments: failureCount,
        results
      },
      message: `Bulk payment processed: ${successCount} successful, ${failureCount} failed`
    });

  } catch (error) {
    console.error('❌ Bulk payment processing failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process bulk payments',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/mpesa/b2c/admin/account-balance
 * @desc    Query M-Pesa account balance
 * @access  Private (Admin only)
 */
router.get('/account-balance', async (req, res) => {
  try {
    const B2CCore = require('../../../../services/mpesa/b2c/core/B2CCore');
    const core = new B2CCore();

    const result = await core.queryAccountBalance();

    if (result.success) {
      res.json({
        success: true,
        data: result.data,
        message: 'Account balance query initiated'
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message,
        error: result.error
      });
    }
  } catch (error) {
    console.error('❌ Account balance query failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to query account balance',
      error: error.message
    });
  }
});

/**
 * @route   POST /api/mpesa/b2c/admin/query-status
 * @desc    Query transaction status from M-Pesa
 * @access  Private (Admin only)
 * @body    { transactionId, originatorConversationId }
 */
router.post('/query-status', async (req, res) => {
  try {
    const { transactionId, originatorConversationId } = req.body;
    const B2CCore = require('../../../../services/mpesa/b2c/core/B2CCore');
    const core = new B2CCore();

    if (!transactionId || !originatorConversationId) {
      return res.status(400).json({
        success: false,
        message: 'Transaction ID and Originator Conversation ID are required'
      });
    }

    const result = await core.queryTransactionStatus(transactionId, originatorConversationId);

    if (result.success) {
      res.json({
        success: true,
        data: result.data,
        message: 'Transaction status query initiated'
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message,
        error: result.error
      });
    }
  } catch (error) {
    console.error('❌ Transaction status query failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to query transaction status',
      error: error.message
    });
  }
});

module.exports = router;
