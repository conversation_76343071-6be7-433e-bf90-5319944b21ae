/**
 * Test M-Pesa API Endpoints
 * Quick test script to verify M-Pesa endpoints are working
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testEndpoint(method, url, data = null, headers = {}) {
  try {
    console.log(`\n🔍 Testing ${method.toUpperCase()} ${url}`);
    
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    console.log(`✅ Success: ${response.status} ${response.statusText}`);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    if (error.response) {
      console.log(`❌ Error: ${error.response.status} ${error.response.statusText}`);
      console.log('Error Response:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log('❌ Network Error: No response received');
      console.log('Request:', error.request);
    } else {
      console.log('❌ Error:', error.message);
    }
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting M-Pesa API Endpoint Tests');
  console.log('=====================================');

  // Test 1: Health check
  await testEndpoint('GET', '/mpesa/health');

  // Test 2: B2C Health check
  await testEndpoint('GET', '/mpesa/b2c/health');

  // Test 3: Utility endpoints
  await testEndpoint('GET', '/mpesa/b2c/utility/health');
  await testEndpoint('GET', '/mpesa/b2c/utility/transaction-types');
  await testEndpoint('GET', '/mpesa/b2c/utility/status-types');
  await testEndpoint('GET', '/mpesa/b2c/utility/business-hours');

  // Test 4: Phone validation
  await testEndpoint('POST', '/mpesa/b2c/utility/validate-phone', {
    phoneNumber: '**********'
  });

  // Test 5: Amount validation
  await testEndpoint('POST', '/mpesa/b2c/utility/validate-amount', {
    amount: 1000,
    transactionType: 'REFUND'
  });

  // Test 6: Fee calculator
  await testEndpoint('GET', '/mpesa/b2c/utility/fee-calculator/1000');

  // Test 7: Test data (sandbox only)
  await testEndpoint('GET', '/mpesa/b2c/utility/test-data');

  // Test 8: Config summary
  await testEndpoint('GET', '/mpesa/b2c/utility/config-summary');

  // Test 9: Generate reference
  await testEndpoint('POST', '/mpesa/b2c/utility/generate-reference', {
    type: 'REFUND',
    orderId: 'test-order-123'
  });

  console.log('\n🏁 Tests completed!');
  console.log('=====================================');
}

// Run the tests
runTests().catch(console.error);
