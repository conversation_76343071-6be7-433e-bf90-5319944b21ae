const { DataTypes } = require('sequelize');
const sequelize = require('../../config/database');

const ImportJobLog = sequelize.define('ImportJobLog', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    importJobId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'import_jobs',
        key: 'id'
      },
      onDelete: 'CASCADE'
    },
    status: {
      type: DataTypes.ENUM('pending', 'running', 'success', 'failed', 'cancelled'),
      allowNull: false,
      defaultValue: 'pending'
    },
    startedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    completedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    duration: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Duration in milliseconds'
    },
    recordsProcessed: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    recordsImported: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    recordsFailed: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    errors: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Array of error messages and details'
    },
    sourceInfo: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Information about the source (file name, URL, etc.)'
    },
    result: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Detailed results of the import operation'
    },
    triggeredBy: {
      type: DataTypes.ENUM('schedule', 'manual', 'api'),
      allowNull: false,
      defaultValue: 'schedule'
    },
    triggeredByUser: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'User ID if triggered manually'
    }
  }, {
    tableName: 'import_job_logs',
    timestamps: true,
    indexes: [
      {
        fields: ['importJobId']
      },
      {
        fields: ['status']
      },
      {
        fields: ['startedAt']
      },
      {
        fields: ['triggeredBy']
      }
    ]
  });

module.exports = ImportJobLog;
