'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create mpesa_transactions table
    await queryInterface.createTable('mpesa_transactions', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      orderId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Orders',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT',
        comment: 'Reference to the order this transaction belongs to'
      },
      merchantRequestId: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Merchant request ID from M-Pesa STK Push'
      },
      checkoutRequestId: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: true,
        comment: 'Checkout request ID from M-Pesa STK Push'
      },
      mpesaReceiptNumber: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: true,
        comment: 'M-Pesa receipt number for successful transactions'
      },
      transactionDate: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Date and time when the transaction was completed on M-Pesa'
      },
      phoneNumber: {
        type: Sequelize.STRING(15),
        allowNull: false,
        comment: 'Customer phone number in format 254XXXXXXXXX'
      },
      amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        comment: 'Transaction amount in KES'
      },
      transactionType: {
        type: Sequelize.ENUM(
          'STK_PUSH',
          'C2B',
          'B2C_REFUND',
          'B2B',
          'REVERSAL'
        ),
        allowNull: false,
        defaultValue: 'STK_PUSH',
        comment: 'Type of M-Pesa transaction'
      },
      status: {
        type: Sequelize.ENUM(
          'PENDING',
          'SUCCESS',
          'FAILED',
          'CANCELLED',
          'TIMEOUT'
        ),
        allowNull: false,
        defaultValue: 'PENDING',
        comment: 'Current status of the transaction'
      },
      resultCode: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'Result code from M-Pesa callback (0 = success)'
      },
      resultDesc: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Result description from M-Pesa callback'
      },
      callbackData: {
        type: Sequelize.JSONB,
        allowNull: true,
        comment: 'Full callback data from M-Pesa for audit purposes'
      },
      conversationId: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Conversation ID for B2C and other async transactions'
      },
      originatorConversationId: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Originator conversation ID for tracking'
      },
      accountReference: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Account reference used in the transaction'
      },
      transactionDesc: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Transaction description'
      },
      initiatorName: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Name of the transaction initiator (for B2C, B2B)'
      },
      remarks: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Additional remarks or notes about the transaction'
      },
      retryCount: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        comment: 'Number of retry attempts for failed transactions'
      },
      lastRetryAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Timestamp of last retry attempt'
      },
      processedAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Timestamp when transaction was fully processed'
      },
      isReconciled: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        comment: 'Whether this transaction has been reconciled with M-Pesa statements'
      },
      reconciledAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Timestamp when transaction was reconciled'
      },
      environment: {
        type: Sequelize.ENUM('sandbox', 'production'),
        allowNull: false,
        defaultValue: 'sandbox',
        comment: 'Environment where transaction was processed'
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Create mpesa_config table for storing M-Pesa configuration
    await queryInterface.createTable('mpesa_config', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      environment: {
        type: Sequelize.ENUM('sandbox', 'production'),
        allowNull: false,
        comment: 'Environment configuration'
      },
      consumerKey: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'M-Pesa consumer key'
      },
      consumerSecret: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'M-Pesa consumer secret'
      },
      businessShortCode: {
        type: Sequelize.STRING(10),
        allowNull: false,
        comment: 'Business short code'
      },
      lipaNameMpesaOnlinePasskey: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: 'Lipa Na M-Pesa Online passkey'
      },
      callbackUrl: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'Callback URL for STK Push'
      },
      confirmationUrl: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Confirmation URL for C2B'
      },
      validationUrl: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Validation URL for C2B'
      },
      initiatorName: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Initiator name for B2C and other operations'
      },
      securityCredential: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Security credential for B2C and other operations'
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        comment: 'Whether this configuration is active'
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes for mpesa_transactions
    await queryInterface.addIndex('mpesa_transactions', ['orderId'], {
      name: 'idx_mpesa_transactions_order_id'
    });

    await queryInterface.addIndex('mpesa_transactions', ['checkoutRequestId'], {
      name: 'idx_mpesa_transactions_checkout_request_id',
      unique: true,
      where: {
        checkoutRequestId: {
          [Sequelize.Op.ne]: null
        }
      }
    });

    await queryInterface.addIndex('mpesa_transactions', ['mpesaReceiptNumber'], {
      name: 'idx_mpesa_transactions_receipt_number',
      unique: true,
      where: {
        mpesaReceiptNumber: {
          [Sequelize.Op.ne]: null
        }
      }
    });

    await queryInterface.addIndex('mpesa_transactions', ['phoneNumber'], {
      name: 'idx_mpesa_transactions_phone_number'
    });

    await queryInterface.addIndex('mpesa_transactions', ['status'], {
      name: 'idx_mpesa_transactions_status'
    });

    await queryInterface.addIndex('mpesa_transactions', ['transactionType'], {
      name: 'idx_mpesa_transactions_type'
    });

    await queryInterface.addIndex('mpesa_transactions', ['transactionDate'], {
      name: 'idx_mpesa_transactions_date'
    });

    await queryInterface.addIndex('mpesa_transactions', ['createdAt'], {
      name: 'idx_mpesa_transactions_created_at'
    });

    await queryInterface.addIndex('mpesa_transactions', ['environment'], {
      name: 'idx_mpesa_transactions_environment'
    });

    // Add indexes for mpesa_config
    await queryInterface.addIndex('mpesa_config', ['environment'], {
      name: 'idx_mpesa_config_environment'
    });

    await queryInterface.addIndex('mpesa_config', ['isActive'], {
      name: 'idx_mpesa_config_is_active'
    });

    // Add unique constraint for active configuration per environment
    await queryInterface.addIndex('mpesa_config', ['environment', 'isActive'], {
      name: 'idx_mpesa_config_environment_active',
      unique: true,
      where: {
        isActive: true
      }
    });

    console.log('✅ M-Pesa tables created successfully');
  },

  async down(queryInterface, Sequelize) {
    // Drop indexes first
    await queryInterface.removeIndex('mpesa_transactions', 'idx_mpesa_transactions_order_id');
    await queryInterface.removeIndex('mpesa_transactions', 'idx_mpesa_transactions_checkout_request_id');
    await queryInterface.removeIndex('mpesa_transactions', 'idx_mpesa_transactions_receipt_number');
    await queryInterface.removeIndex('mpesa_transactions', 'idx_mpesa_transactions_phone_number');
    await queryInterface.removeIndex('mpesa_transactions', 'idx_mpesa_transactions_status');
    await queryInterface.removeIndex('mpesa_transactions', 'idx_mpesa_transactions_type');
    await queryInterface.removeIndex('mpesa_transactions', 'idx_mpesa_transactions_date');
    await queryInterface.removeIndex('mpesa_transactions', 'idx_mpesa_transactions_created_at');
    await queryInterface.removeIndex('mpesa_transactions', 'idx_mpesa_transactions_environment');
    
    await queryInterface.removeIndex('mpesa_config', 'idx_mpesa_config_environment');
    await queryInterface.removeIndex('mpesa_config', 'idx_mpesa_config_is_active');
    await queryInterface.removeIndex('mpesa_config', 'idx_mpesa_config_environment_active');

    // Drop tables
    await queryInterface.dropTable('mpesa_transactions');
    await queryInterface.dropTable('mpesa_config');

    // Drop ENUMs
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_mpesa_transactions_transactionType";');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_mpesa_transactions_status";');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_mpesa_transactions_environment";');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_mpesa_config_environment";');

    console.log('✅ M-Pesa tables dropped successfully');
  }
};
