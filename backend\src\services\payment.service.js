const { Payment, Order } = require('../db/models');
const { NotFoundError, ValidationError } = require('../utils/errors');
const config = require('../config');
const { sequelize } = require('../db/models');

// Initialize Stripe only if API key is available
let stripe;
try {
  if (process.env.STRIPE_SECRET_KEY) {
    stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
  } else {
    console.warn('STRIPE_SECRET_KEY not found in environment variables. Payment features will be disabled.');
  }
} catch (error) {
  console.error('Failed to initialize Stripe:', error.message);
}

/**
 * Create a payment intent with Stripe
 */
exports.createPaymentIntent = async (orderId, userId = null) => {
  // Check if Stripe is initialized
  if (!stripe) {
    throw new Error('Stripe is not initialized. Payment features are disabled.');
  }

  // Find the order
  const order = await Order.findByPk(orderId);

  if (!order) {
    throw new NotFoundError('Order not found');
  }

  // If userId is provided, verify ownership
  if (userId && order.userId !== userId) {
    throw new NotFoundError('Order not found');
  }

  // Check if order is already paid
  if (order.paymentStatus === 'paid') {
    throw new ValidationError('Order is already paid');
  }

  // Create payment intent with Stripe
  const paymentIntent = await stripe.paymentIntents.create({
    amount: Math.round(order.totalAmount * 100), // Convert to cents
    currency: 'usd',
    metadata: {
      orderId: order.id,
      orderNumber: order.orderNumber
    },
    description: `Payment for order ${order.orderNumber}`
  });

  // Create payment record
  const payment = await Payment.create({
    orderId: order.id,
    amount: order.totalAmount,
    method: 'credit_card',
    status: 'pending',
    paymentIntentId: paymentIntent.id
  });

  // Update order payment status
  await order.update({
    paymentStatus: 'pending'
  });

  return {
    clientSecret: paymentIntent.client_secret,
    paymentId: payment.id
  };
};

/**
 * Process payment webhook from Stripe
 */
exports.processPaymentWebhook = async (event) => {
  // Check if Stripe is initialized
  if (!stripe) {
    throw new Error('Stripe is not initialized. Payment features are disabled.');
  }

  const transaction = await sequelize.transaction();

  try {
    if (event.type === 'payment_intent.succeeded') {
      const paymentIntent = event.data.object;
      const { orderId } = paymentIntent.metadata;

      // Find payment by payment intent ID
      const payment = await Payment.findOne({
        where: { paymentIntentId: paymentIntent.id },
        transaction
      });

      if (!payment) {
        throw new NotFoundError('Payment not found');
      }

      // Update payment details
      await payment.update({
        status: 'completed',
        transactionId: paymentIntent.id,
        paymentDetails: paymentIntent,
        cardBrand: paymentIntent.charges.data[0]?.payment_method_details?.card?.brand,
        cardLast4: paymentIntent.charges.data[0]?.payment_method_details?.card?.last4,
        receiptUrl: paymentIntent.charges.data[0]?.receipt_url
      }, { transaction });

      // Update order payment status
      const order = await Order.findByPk(orderId, { transaction });

      if (order) {
        await order.update({
          paymentStatus: 'paid',
          status: 'processing'
        }, { transaction });
      }

      await transaction.commit();
      return { success: true, orderId };
    } else if (event.type === 'payment_intent.payment_failed') {
      const paymentIntent = event.data.object;
      const { orderId } = paymentIntent.metadata;

      // Find payment by payment intent ID
      const payment = await Payment.findOne({
        where: { paymentIntentId: paymentIntent.id },
        transaction
      });

      if (payment) {
        // Update payment details
        await payment.update({
          status: 'failed',
          errorMessage: paymentIntent.last_payment_error?.message || 'Payment failed',
          paymentDetails: paymentIntent
        }, { transaction });
      }

      // Update order payment status
      const order = await Order.findByPk(orderId, { transaction });

      if (order) {
        await order.update({
          paymentStatus: 'failed'
        }, { transaction });
      }

      await transaction.commit();
      return { success: false, orderId };
    }

    await transaction.commit();
    return { success: true };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Process refund
 */
exports.processRefund = async (orderId, amount, reason, adminId) => {
  // Check if Stripe is initialized
  if (!stripe) {
    throw new Error('Stripe is not initialized. Payment features are disabled.');
  }

  const transaction = await sequelize.transaction();

  try {
    // Find the order
    const order = await Order.findByPk(orderId, { transaction });

    if (!order) {
      throw new NotFoundError('Order not found');
    }

    // Find the payment
    const payment = await Payment.findOne({
      where: {
        orderId: order.id,
        status: 'completed'
      },
      transaction
    });

    if (!payment) {
      throw new NotFoundError('No completed payment found for this order');
    }

    // Validate refund amount
    const maxRefundAmount = parseFloat(payment.amount) - parseFloat(payment.refundedAmount || 0);

    if (amount > maxRefundAmount) {
      throw new ValidationError(`Refund amount cannot exceed ${maxRefundAmount}`);
    }

    // Process refund with Stripe
    const refund = await stripe.refunds.create({
      payment_intent: payment.paymentIntentId,
      amount: Math.round(amount * 100), // Convert to cents
      reason: reason === 'requested_by_customer' ? 'requested_by_customer' : 'other'
    });

    // Update payment record
    const newRefundedAmount = parseFloat(payment.refundedAmount || 0) + amount;
    const isFullRefund = newRefundedAmount >= parseFloat(payment.amount);

    await payment.update({
      status: isFullRefund ? 'refunded' : 'partially_refunded',
      refundedAmount: newRefundedAmount,
      refundedAt: new Date(),
      refundReason: reason
    }, { transaction });

    // Update order status
    await order.update({
      status: isFullRefund ? 'refunded' : order.status,
      paymentStatus: isFullRefund ? 'refunded' : 'partially_refunded'
    }, { transaction });

    await transaction.commit();

    return {
      success: true,
      refundId: refund.id,
      amount,
      isFullRefund
    };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};
