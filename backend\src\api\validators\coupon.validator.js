const { body } = require('express-validator');
const { validateRequest } = require('../../utils/validator');

/**
 * Validate coupon creation
 */
exports.validateCoupon = [
  body('code')
    .notEmpty().withMessage('Coupon code is required')
    .isString().withMessage('Coupon code must be a string')
    .isLength({ min: 3, max: 20 }).withMessage('Coupon code must be between 3 and 20 characters')
    .matches(/^[A-Z0-9_-]+$/).withMessage('Coupon code can only contain uppercase letters, numbers, underscores and hyphens'),
  
  body('type')
    .notEmpty().withMessage('Coupon type is required')
    .isIn(['percentage', 'fixed']).withMessage('Coupon type must be either percentage or fixed'),
  
  body('value')
    .notEmpty().withMessage('Coupon value is required')
    .isFloat({ min: 0 }).withMessage('Coupon value must be a positive number'),
  
  body('minPurchase')
    .optional()
    .isFloat({ min: 0 }).withMessage('Minimum purchase must be a positive number'),
  
  body('maxDiscount')
    .optional()
    .isFloat({ min: 0 }).withMessage('Maximum discount must be a positive number'),
  
  body('startDate')
    .optional()
    .isISO8601().withMessage('Start date must be a valid date'),
  
  body('endDate')
    .optional()
    .isISO8601().withMessage('End date must be a valid date')
    .custom((value, { req }) => {
      if (req.body.startDate && new Date(value) <= new Date(req.body.startDate)) {
        throw new Error('End date must be after start date');
      }
      return true;
    }),
  
  body('usageLimit')
    .optional()
    .isInt({ min: 1 }).withMessage('Usage limit must be a positive integer'),
  
  body('perUserLimit')
    .optional()
    .isInt({ min: 1 }).withMessage('Per user limit must be a positive integer'),
  
  body('isActive')
    .optional()
    .isBoolean().withMessage('isActive must be a boolean'),
  
  body('categories')
    .optional()
    .isArray().withMessage('Categories must be an array'),
  
  body('products')
    .optional()
    .isArray().withMessage('Products must be an array'),
  
  validateRequest
];

/**
 * Validate coupon update
 */
exports.validateCouponUpdate = [
  body('code')
    .optional()
    .isString().withMessage('Coupon code must be a string')
    .isLength({ min: 3, max: 20 }).withMessage('Coupon code must be between 3 and 20 characters')
    .matches(/^[A-Z0-9_-]+$/).withMessage('Coupon code can only contain uppercase letters, numbers, underscores and hyphens'),
  
  body('type')
    .optional()
    .isIn(['percentage', 'fixed']).withMessage('Coupon type must be either percentage or fixed'),
  
  body('value')
    .optional()
    .isFloat({ min: 0 }).withMessage('Coupon value must be a positive number'),
  
  body('minPurchase')
    .optional()
    .isFloat({ min: 0 }).withMessage('Minimum purchase must be a positive number'),
  
  body('maxDiscount')
    .optional()
    .isFloat({ min: 0 }).withMessage('Maximum discount must be a positive number'),
  
  body('startDate')
    .optional()
    .isISO8601().withMessage('Start date must be a valid date'),
  
  body('endDate')
    .optional()
    .isISO8601().withMessage('End date must be a valid date')
    .custom((value, { req }) => {
      if (req.body.startDate && new Date(value) <= new Date(req.body.startDate)) {
        throw new Error('End date must be after start date');
      }
      return true;
    }),
  
  body('usageLimit')
    .optional()
    .isInt({ min: 1 }).withMessage('Usage limit must be a positive integer'),
  
  body('perUserLimit')
    .optional()
    .isInt({ min: 1 }).withMessage('Per user limit must be a positive integer'),
  
  body('isActive')
    .optional()
    .isBoolean().withMessage('isActive must be a boolean'),
  
  body('categories')
    .optional()
    .isArray().withMessage('Categories must be an array'),
  
  body('products')
    .optional()
    .isArray().withMessage('Products must be an array'),
  
  validateRequest
];
