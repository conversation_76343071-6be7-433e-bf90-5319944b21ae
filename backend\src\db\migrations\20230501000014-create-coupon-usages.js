'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('CouponUsages', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      couponId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Coupons',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        }
      },
      orderId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Orders',
          key: 'id'
        }
      },
      discountAmount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      usedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false
      }
    });

    // Add indexes
    await queryInterface.addIndex('CouponUsages', ['couponId']);
    await queryInterface.addIndex('CouponUsages', ['userId']);
    await queryInterface.addIndex('CouponUsages', ['orderId']);
    await queryInterface.addIndex('CouponUsages', ['couponId', 'orderId'], {
      unique: true
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('CouponUsages');
  }
};
