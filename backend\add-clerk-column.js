require('dotenv').config();
const sequelize = require('./src/config/database');

async function addClerkColumn() {
  try {
    console.log('🔍 Starting script...');
    console.log('🔍 Environment:', {
      DB_NAME: process.env.DB_NAME,
      DB_HOST: process.env.DB_HOST,
      DB_PORT: process.env.DB_PORT,
      DB_USER: process.env.DB_USER
    });

    console.log('🔍 Connecting to database...');
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    console.log('🔍 Adding clerkId column...');

    // Add clerkId column
    await sequelize.query(`
      ALTER TABLE "Users"
      ADD COLUMN IF NOT EXISTS "clerkId" VARCHAR(255) UNIQUE;
    `);
    console.log('✅ clerkId column added');

    // Add authProvider column
    await sequelize.query(`
      DO $$ BEGIN
        CREATE TYPE user_auth_provider AS ENUM ('local', 'clerk');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await sequelize.query(`
      ALTER TABLE "Users"
      ADD COLUMN IF NOT EXISTS "authProvider" user_auth_provider DEFAULT 'local';
    `);
    console.log('✅ authProvider column added');

    // Make password optional
    await sequelize.query(`
      ALTER TABLE "Users"
      ALTER COLUMN "password" DROP NOT NULL;
    `);
    console.log('✅ Password column made optional');

    // Add index for clerkId
    await sequelize.query(`
      CREATE UNIQUE INDEX IF NOT EXISTS "users_clerk_id_index"
      ON "Users" ("clerkId");
    `);
    console.log('✅ Index added for clerkId');

    console.log('🎉 All Clerk columns added successfully!');

  } catch (error) {
    console.error('❌ Error adding Clerk columns:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await sequelize.close();
    console.log('🔍 Database connection closed');
  }
}

addClerkColumn();
