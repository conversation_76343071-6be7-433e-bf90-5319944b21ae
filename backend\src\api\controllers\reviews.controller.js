const reviewService = require('../../services/review.service');
const { NotFoundError, ValidationError } = require('../../utils/errors');

/**
 * Create a new review
 */
exports.createReview = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const reviewData = req.body;
    
    // Validate required fields
    if (!reviewData.productId) {
      throw new ValidationError('Product ID is required');
    }
    
    if (!reviewData.rating) {
      throw new ValidationError('Rating is required');
    }
    
    if (!reviewData.comment) {
      throw new ValidationError('Comment is required');
    }
    
    const review = await reviewService.createReview(userId, reviewData);
    
    res.status(201).json(review);
  } catch (error) {
    next(error);
  }
};

/**
 * Get review by ID
 */
exports.getReview = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const review = await reviewService.getReviewById(id);
    
    res.status(200).json(review);
  } catch (error) {
    next(error);
  }
};

/**
 * Update a review
 */
exports.updateReview = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const updateData = req.body;
    
    const review = await reviewService.updateReview(id, userId, updateData);
    
    res.status(200).json(review);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete a review
 */
exports.deleteReview = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const isAdmin = req.user.role === 'admin';
    
    await reviewService.deleteReview(id, userId, isAdmin);
    
    res.status(204).send();
  } catch (error) {
    next(error);
  }
};

/**
 * Get reviews for a product
 */
exports.getProductReviews = async (req, res, next) => {
  try {
    const { productId } = req.params;
    const { page, limit, sort, order } = req.query;
    
    const reviews = await reviewService.getProductReviews(productId, {
      page: parseInt(page) || 1,
      limit: parseInt(limit) || 10,
      sort: sort || 'createdAt',
      order: order || 'DESC'
    });
    
    res.status(200).json(reviews);
  } catch (error) {
    next(error);
  }
};

/**
 * Get reviews by a user
 */
exports.getUserReviews = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { page, limit } = req.query;
    
    const reviews = await reviewService.getUserReviews(userId, {
      page: parseInt(page) || 1,
      limit: parseInt(limit) || 10
    });
    
    res.status(200).json(reviews);
  } catch (error) {
    next(error);
  }
};

/**
 * Admin: Approve or reject a review
 */
exports.moderateReview = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { isApproved, adminResponse } = req.body;
    
    if (isApproved === undefined) {
      throw new ValidationError('Approval status is required');
    }
    
    const review = await reviewService.moderateReview(id, isApproved, adminResponse);
    
    res.status(200).json(review);
  } catch (error) {
    next(error);
  }
};

/**
 * Mark review as helpful or not helpful
 */
exports.markReviewHelpfulness = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { isHelpful } = req.body;
    
    if (isHelpful === undefined) {
      throw new ValidationError('Helpfulness status is required');
    }
    
    const review = await reviewService.markReviewHelpfulness(id, isHelpful);
    
    res.status(200).json(review);
  } catch (error) {
    next(error);
  }
};
