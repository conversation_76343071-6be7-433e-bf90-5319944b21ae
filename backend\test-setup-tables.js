const axios = require('axios');

async function setupTables() {
  try {
    console.log('🔄 Setting up import tables...');
    
    const response = await axios.post('http://localhost:3000/api/admin/setup/import-tables', {}, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Setup response:', response.data);
    
  } catch (error) {
    console.error('❌ Setup failed:', error.response?.data || error.message);
  }
}

setupTables();
