/**
 * Test script to verify ngrok setup for M-Pesa
 * Run this after setting up ngrok and updating .env
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../../.env') });
const axios = require('axios');

console.log('🔧 Testing ngrok setup for M-Pesa...\n');

// Check environment variables
console.log('📋 Current M-Pesa Configuration:');
console.log('Environment:', process.env.MPESA_ENVIRONMENT);
console.log('Use Mock:', process.env.MPESA_USE_MOCK);
console.log('Callback URL:', process.env.MPESA_CALLBACK_URL);
console.log('Consumer Key:', process.env.MPESA_CONSUMER_KEY ? 'SET' : 'NOT SET');
console.log('Consumer Secret:', process.env.MPESA_CONSUMER_SECRET ? 'SET' : 'NOT SET');
console.log('');

// Test 1: Check if ngrok URL is accessible
async function testNgrokUrl() {
  try {
    const callbackUrl = process.env.MPESA_CALLBACK_URL;

    if (!callbackUrl || callbackUrl.includes('YOUR_NGROK_URL')) {
      console.log('❌ Please update YOUR_NGROK_URL in .env file with your actual ngrok URL');
      return false;
    }

    if (!callbackUrl.startsWith('https://')) {
      console.log('❌ Callback URL must be HTTPS for M-Pesa');
      return false;
    }

    console.log('🌐 Testing ngrok URL accessibility...');

    // Extract base URL
    const baseUrl = callbackUrl.replace('/api/mpesa/callback', '');

    // Test health endpoint
    const response = await axios.get(`${baseUrl}/health`, { timeout: 5000 });

    console.log('✅ ngrok URL is accessible!');
    console.log('Response:', response.data);
    return true;

  } catch (error) {
    console.log('❌ ngrok URL test failed!');

    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure:');
      console.log('   1. ngrok is running: ngrok http 3000');
      console.log('   2. Your backend server is running on port 3000');
      console.log('   3. Update .env with your actual ngrok URL');
    } else if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }

    return false;
  }
}

// Test 2: Test M-Pesa access token generation
async function testMpesaAuth() {
  try {
    console.log('🔑 Testing M-Pesa authentication...');

    const consumerKey = process.env.MPESA_CONSUMER_KEY;
    const consumerSecret = process.env.MPESA_CONSUMER_SECRET;

    if (!consumerKey || !consumerSecret) {
      console.log('❌ M-Pesa credentials not configured');
      return false;
    }

    const auth = Buffer.from(`${consumerKey}:${consumerSecret}`).toString('base64');
    const baseUrl = process.env.MPESA_ENVIRONMENT === 'production'
      ? 'https://api.safaricom.co.ke'
      : 'https://sandbox.safaricom.co.ke';

    const response = await axios.get(
      `${baseUrl}/oauth/v1/generate?grant_type=client_credentials`,
      {
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      }
    );

    console.log('✅ M-Pesa authentication successful!');
    console.log('Token expires in:', response.data.expires_in, 'seconds');
    return true;

  } catch (error) {
    console.log('❌ M-Pesa authentication failed!');

    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }

    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting ngrok and M-Pesa setup tests...\n');

  // Test 1: ngrok URL
  const ngrokOk = await testNgrokUrl();
  console.log('');

  // Test 2: M-Pesa auth
  const mpesaOk = await testMpesaAuth();
  console.log('');

  // Summary
  console.log('📊 Test Summary:');
  console.log('ngrok URL:', ngrokOk ? '✅ Working' : '❌ Failed');
  console.log('M-Pesa Auth:', mpesaOk ? '✅ Working' : '❌ Failed');

  if (ngrokOk && mpesaOk) {
    console.log('\n🎉 Setup is ready! You can now test M-Pesa payments.');
  } else {
    console.log('\n⚠️ Please fix the issues above before testing M-Pesa payments.');
  }
}

// Run the tests
runTests().catch(console.error);
