import React from 'react';
import { SignInButton as ClerkSignInButton, useAuth } from '@clerk/clerk-react';

export const SignInButton: React.FC = () => {
  const { isSignedIn } = useAuth();

  if (isSignedIn) {
    return null;
  }

  return (
    <ClerkSignInButton mode="modal">
      <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
        Sign In
      </button>
    </ClerkSignInButton>
  );
};
