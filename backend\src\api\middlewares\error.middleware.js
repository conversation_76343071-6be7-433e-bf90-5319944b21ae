const logger = require('../../utils/logger');
const { AppError } = require('../../utils/errors');

module.exports = (err, req, res, next) => {
  // Log the error
  logger.error(err);

  // Default error values
  let statusCode = 500;
  let message = 'Internal Server Error';
  let errors = [];

  // Handle known errors
  if (err instanceof AppError) {
    statusCode = err.statusCode;
    message = err.message;

    // Handle ValidationError with detailed errors
    if (err.name === 'ValidationError' && err.errors) {
      errors = Array.isArray(err.errors) ? err.errors : Object.values(err.errors);
    }
  }

  // Handle Sequelize validation errors
  if (err.name === 'SequelizeValidationError' || err.name === 'SequelizeUniqueConstraintError') {
    statusCode = 400;
    message = 'Validation Error';
    errors = err.errors.map(e => ({
      field: e.path,
      message: e.message
    }));
  }

  // Handle express-validator errors
  if (err.array && typeof err.array === 'function') {
    statusCode = 400;
    message = 'Validation Error';
    errors = err.array();
  }

  // Send response
  res.status(statusCode).json({
    status: 'error',
    message,
    errors: errors.length ? errors : undefined,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });
};
