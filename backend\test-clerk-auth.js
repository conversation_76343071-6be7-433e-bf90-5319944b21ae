const axios = require('axios');
const fs = require('fs');
const path = require('path');

// API base URL
const API_BASE_URL = 'http://localhost:3000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Helper function to log results
function logResult(testName, data) {
  const timestamp = new Date().toISOString();
  console.log(`\n✅ ${testName} - SUCCESS`);
  console.log(`Timestamp: ${timestamp}`);
  console.log('Response:', JSON.stringify(data, null, 2));
  
  // Save to file
  const resultsDir = path.join(__dirname, 'test-results');
  if (!fs.existsSync(resultsDir)) {
    fs.mkdirSync(resultsDir);
  }
  
  fs.writeFileSync(
    path.join(resultsDir, `${testName}.json`),
    JSON.stringify({ timestamp, data }, null, 2)
  );
}

// Helper function to log errors
function logError(testName, error) {
  const timestamp = new Date().toISOString();
  console.log(`\n❌ ${testName} - ERROR`);
  console.log(`Timestamp: ${timestamp}`);
  
  if (error.response) {
    console.log('Status:', error.response.status);
    console.log('Response:', JSON.stringify(error.response.data, null, 2));
    
    // Save error to file
    const resultsDir = path.join(__dirname, 'test-results');
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir);
    }
    
    fs.writeFileSync(
      path.join(resultsDir, `${testName}-error.json`),
      JSON.stringify({
        timestamp,
        status: error.response.status,
        data: error.response.data
      }, null, 2)
    );
  } else {
    console.log('Error:', error.message);
  }
}

// Clerk Authentication Tests
const clerkAuthTests = {
  // Test 1: Access admin route without authentication (should fail)
  async testAdminRouteWithoutAuth() {
    try {
      const response = await api.get('/admin/dashboard');
      logResult('admin-route-without-auth', response.data);
      return response.data;
    } catch (error) {
      logError('admin-route-without-auth', error);
      // This is expected to fail, so we'll treat it as a success
      if (error.response && error.response.status === 401) {
        console.log('✅ Expected 401 error - Clerk auth is working correctly');
        return { expected: 'unauthorized', status: error.response.status };
      }
      throw error;
    }
  },

  // Test 2: Access admin route with invalid token (should fail)
  async testAdminRouteWithInvalidAuth() {
    try {
      const response = await api.get('/admin/dashboard', {
        headers: {
          'Authorization': 'Bearer invalid_token_here'
        }
      });
      logResult('admin-route-invalid-auth', response.data);
      return response.data;
    } catch (error) {
      logError('admin-route-invalid-auth', error);
      // This is expected to fail, so we'll treat it as a success
      if (error.response && error.response.status === 401) {
        console.log('✅ Expected 401 error - Clerk auth validation is working correctly');
        return { expected: 'unauthorized', status: error.response.status };
      }
      throw error;
    }
  },

  // Test 3: Check if Clerk middleware is properly loaded
  async testClerkMiddlewareLoading() {
    try {
      // Test a route that uses optional Clerk auth (should not fail)
      const response = await api.get('/products');
      logResult('clerk-middleware-loading', {
        message: 'Clerk middleware loaded successfully',
        productsEndpoint: 'accessible',
        status: response.status
      });
      return response.data;
    } catch (error) {
      logError('clerk-middleware-loading', error);
      throw error;
    }
  },

  // Test 4: Test health endpoint (should work without auth)
  async testHealthEndpoint() {
    try {
      const response = await api.get('/health');
      logResult('health-endpoint', response.data);
      return response.data;
    } catch (error) {
      logError('health-endpoint', error);
      throw error;
    }
  },

  // Test 5: Test protected route structure
  async testProtectedRouteStructure() {
    try {
      // Try to access user profile without auth
      const response = await api.get('/users/profile');
      logResult('protected-route-structure', response.data);
      return response.data;
    } catch (error) {
      logError('protected-route-structure', error);
      if (error.response && error.response.status === 401) {
        console.log('✅ Expected 401 error - Protected routes are properly secured');
        return { expected: 'unauthorized', status: error.response.status };
      }
      throw error;
    }
  }
};

// Main test runner
async function runClerkAuthTests() {
  console.log('🔐 Starting Clerk Authentication Tests...\n');
  console.log('='.repeat(50));
  
  const results = {};
  let passedTests = 0;
  let totalTests = 0;
  
  for (const [testName, testFunction] of Object.entries(clerkAuthTests)) {
    totalTests++;
    try {
      console.log(`\n🧪 Running: ${testName}`);
      const result = await testFunction();
      results[testName] = { status: 'passed', data: result };
      passedTests++;
    } catch (error) {
      results[testName] = { status: 'failed', error: error.message };
      console.log(`❌ ${testName} failed:`, error.message);
    }
  }
  
  // Summary
  console.log('\n' + '='.repeat(50));
  console.log('📊 CLERK AUTHENTICATION TEST SUMMARY');
  console.log('='.repeat(50));
  console.log(`✅ Passed: ${passedTests}/${totalTests}`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All Clerk authentication tests passed!');
  } else {
    console.log('\n⚠️  Some tests failed. Check the logs above for details.');
  }
  
  // Save summary
  const resultsDir = path.join(__dirname, 'test-results');
  if (!fs.existsSync(resultsDir)) {
    fs.mkdirSync(resultsDir);
  }
  
  fs.writeFileSync(
    path.join(resultsDir, 'clerk-auth-test-summary.json'),
    JSON.stringify({
      timestamp: new Date().toISOString(),
      totalTests,
      passedTests,
      failedTests: totalTests - passedTests,
      results
    }, null, 2)
  );
  
  return results;
}

// Run tests if this file is executed directly
if (require.main === module) {
  runClerkAuthTests()
    .then(() => {
      console.log('\n✨ Clerk authentication testing completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test runner failed:', error);
      process.exit(1);
    });
}

module.exports = { runClerkAuthTests, clerkAuthTests };
