const orderService = require('../../services/order.service');
const { NotFoundError, ValidationError } = require('../../utils/errors');

/**
 * Create a new order
 */
exports.createOrder = async (req, res, next) => {
  try {
    const userId = req.user ? req.user.id : null;
    const sessionId = req.cookies.sessionId || req.headers['x-session-id'];

    // Debug logging (can be removed in production)
    console.log('Order creation request - userId:', userId, 'sessionId:', sessionId);

    if (!userId && !sessionId) {
      throw new ValidationError('User ID or Session ID is required');
    }

    const orderData = req.body;

    // Validate required fields
    if (!orderData.shippingAddress) {
      throw new ValidationError('Shipping address is required');
    }

    if (!orderData.paymentMethod) {
      throw new ValidationError('Payment method is required');
    }

    // For guest checkout, validate email
    if (!userId && !orderData.email) {
      throw new ValidationError('Email is required for guest checkout');
    }

    const order = await orderService.createOrder(orderData, userId, sessionId);

    res.status(201).json(order);
  } catch (error) {
    next(error);
  }
};

/**
 * Get order by ID
 */
exports.getOrder = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user ? req.user.id : null;

    // If not admin, only allow access to own orders
    const order = req.user.role === 'admin'
      ? await orderService.getOrderById(id)
      : await orderService.getOrderById(id, userId);

    res.status(200).json(order);
  } catch (error) {
    next(error);
  }
};

/**
 * Get user orders
 */
exports.getUserOrders = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { page, limit, status } = req.query;

    const orders = await orderService.getUserOrders(userId, {
      page: parseInt(page) || 1,
      limit: parseInt(limit) || 10,
      status
    });

    res.status(200).json(orders);
  } catch (error) {
    next(error);
  }
};

/**
 * Update order status (admin only)
 */
exports.updateOrderStatus = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!status) {
      throw new ValidationError('Status is required');
    }

    const order = await orderService.updateOrderStatus(id, status);

    res.status(200).json(order);
  } catch (error) {
    next(error);
  }
};

/**
 * Cancel order (user)
 */
exports.cancelOrder = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { cancelReason } = req.body;

    // Only allow cancellation if order is in pending or processing state
    const order = await orderService.getOrderById(id, userId);

    if (!['pending', 'processing'].includes(order.status)) {
      throw new ValidationError('Order cannot be cancelled at this stage');
    }

    const updatedOrder = await orderService.updateOrderStatus(id, 'cancelled', userId);

    // Update cancel reason if provided
    if (cancelReason) {
      await updatedOrder.update({ cancelReason });
    }

    res.status(200).json(updatedOrder);
  } catch (error) {
    next(error);
  }
};

/**
 * Get all orders (admin only)
 */
exports.getAllOrders = async (req, res, next) => {
  try {
    const { page, limit, status, startDate, endDate } = req.query;

    const orders = await orderService.getAllOrders({
      page: parseInt(page) || 1,
      limit: parseInt(limit) || 10,
      status,
      startDate,
      endDate
    });

    res.status(200).json(orders);
  } catch (error) {
    next(error);
  }
};
