/**
 * Debug M-Pesa payment initiation with detailed logging
 * Run this with: node debug-mpesa-payment.js
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';
const TEST_SESSION_ID = 'test-session-debug';

// Test M-Pesa payment data with proper formatting
const testMpesaPayment = {
  orderId: "4cb4c5a0-24c5-44b6-a052-90f231210224", // Use existing order ID
  phoneNumber: "************", // Proper format
  amount: 75, // Round number, no decimals
  accountReference: "G20-4cb4c5a0", // Short reference (under 20 chars)
  transactionDesc: "G20Shop Order Payment"
};

async function debugMpesaPayment() {
  try {
    console.log('🔍 Debug M-Pesa payment initiation...');
    console.log('📤 Request URL:', `${API_BASE_URL}/mpesa/initiate-payment`);
    console.log('📤 Request headers:', {
      'Content-Type': 'application/json',
      'x-session-id': TEST_SESSION_ID
    });
    console.log('📤 Request body:', JSON.stringify(testMpesaPayment, null, 2));
    console.log('');
    
    const response = await axios.post(
      `${API_BASE_URL}/mpesa/initiate-payment`,
      testMpesaPayment,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-session-id': TEST_SESSION_ID
        },
        timeout: 30000
      }
    );
    
    console.log('✅ M-Pesa payment initiated successfully!');
    console.log('📥 Response status:', response.status);
    console.log('📥 Response data:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('❌ M-Pesa payment initiation failed!');
    console.log('');
    
    if (error.response) {
      console.log('📊 Response Status:', error.response.status);
      console.log('📊 Response Headers:', JSON.stringify(error.response.headers, null, 2));
      console.log('📊 Response Data:', JSON.stringify(error.response.data, null, 2));
      
      // Check for specific error patterns
      if (error.response.data) {
        const errorData = error.response.data;
        
        if (errorData.message) {
          console.log('');
          console.log('🔍 Error Analysis:');
          console.log('Message:', errorData.message);
          
          if (errorData.errors) {
            console.log('Validation Errors:');
            errorData.errors.forEach((err, index) => {
              console.log(`  ${index + 1}. Field: ${err.field}`);
              console.log(`     Message: ${err.message}`);
              console.log(`     Value: ${err.value}`);
            });
          }
          
          if (errorData.stack) {
            console.log('');
            console.log('📋 Stack Trace:');
            console.log(errorData.stack);
          }
        }
      }
    } else if (error.request) {
      console.log('🌐 Network Error - No response received');
      console.log('Request details:', error.request);
    } else {
      console.log('⚠️ Request Setup Error:', error.message);
    }
  }
}

// Test server connectivity first
async function testServerConnection() {
  try {
    console.log('🔗 Testing server connection...');
    const response = await axios.get(`${API_BASE_URL.replace('/api', '')}/health`);
    console.log('✅ Server is responding:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Server connection failed:', error.message);
    return false;
  }
}

// Test if order exists
async function testOrderExists() {
  try {
    console.log('📋 Testing if order exists...');
    const response = await axios.get(
      `${API_BASE_URL}/orders/${testMpesaPayment.orderId}`,
      {
        headers: {
          'x-session-id': TEST_SESSION_ID
        }
      }
    );
    
    console.log('✅ Order exists:', response.data.orderNumber);
    return true;
  } catch (error) {
    console.log('❌ Order not found or error:', error.response?.status, error.response?.data?.message);
    return false;
  }
}

// Main debug function
async function runDebug() {
  console.log('🚀 Starting M-Pesa payment debug...\n');
  
  // Test 1: Server connection
  const serverOk = await testServerConnection();
  if (!serverOk) {
    console.log('❌ Cannot proceed - server is not responding');
    return;
  }
  
  console.log(''); // Empty line
  
  // Test 2: Order exists
  const orderExists = await testOrderExists();
  
  console.log(''); // Empty line
  
  // Test 3: M-Pesa payment (proceed even if order doesn't exist for debugging)
  await debugMpesaPayment();
}

// Run the debug
runDebug().catch(console.error);
