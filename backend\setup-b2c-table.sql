-- Create B2C transactions table
CREATE TABLE IF NOT EXISTS b2c_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "conversationId" VARCHAR(255) NOT NULL UNIQUE,
    "mpesaConversationId" VARCHAR(255),
    "originatorConversationId" VARCHAR(255),
    "phoneNumber" VARCHAR(15) NOT NULL CHECK ("phoneNumber" ~ '^254[0-9]{9}$'),
    amount DECIMAL(10,2) NOT NULL CHECK (amount >= 1 AND amount <= 150000),
    "transactionType" VARCHAR(20) NOT NULL DEFAULT 'GENERAL' CHECK ("transactionType" IN ('REFUND', 'SALARY', 'PROMOTION', 'GENERAL')),
    "commandId" VARCHAR(30) NOT NULL DEFAULT 'BusinessPayment' CHECK ("commandId" IN ('BusinessPayment', 'SalaryPayment', 'PromotionPayment')),
    remarks VA<PERSON>HA<PERSON>(100) NOT NULL DEFAULT 'G20Shop Payment',
    occasion VARCHAR(100) NOT NULL DEFAULT 'Payment Processing',
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'SUBMITTED', 'COMPLETED', 'FAILED', 'TIMEOUT', 'CANCELLED')),
    "responseCode" VARCHAR(10),
    "responseDescription" TEXT,
    "resultCode" INTEGER,
    "resultDescription" TEXT,
    "mpesaReceiptNumber" VARCHAR(50),
    "transactionAmount" DECIMAL(10,2),
    "transactionDate" TIMESTAMP WITH TIME ZONE,
    "b2cCharges" DECIMAL(10,2),
    "receiverPartyName" VARCHAR(255),
    "b2cUtilityBalance" DECIMAL(15,2),
    "b2cWorkingBalance" DECIMAL(15,2),
    "orderId" UUID REFERENCES orders(id),
    "userId" VARCHAR(255),
    metadata JSONB,
    "callbackData" JSONB,
    "initiatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "completedAt" TIMESTAMP WITH TIME ZONE,
    "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_b2c_conversation_id ON b2c_transactions("conversationId");
CREATE INDEX IF NOT EXISTS idx_b2c_mpesa_conversation_id ON b2c_transactions("mpesaConversationId");
CREATE INDEX IF NOT EXISTS idx_b2c_phone_number ON b2c_transactions("phoneNumber");
CREATE INDEX IF NOT EXISTS idx_b2c_status ON b2c_transactions(status);
CREATE INDEX IF NOT EXISTS idx_b2c_transaction_type ON b2c_transactions("transactionType");
CREATE INDEX IF NOT EXISTS idx_b2c_order_id ON b2c_transactions("orderId");
CREATE INDEX IF NOT EXISTS idx_b2c_user_id ON b2c_transactions("userId");
CREATE INDEX IF NOT EXISTS idx_b2c_initiated_at ON b2c_transactions("initiatedAt");
CREATE INDEX IF NOT EXISTS idx_b2c_receipt_number ON b2c_transactions("mpesaReceiptNumber");

-- Insert sample B2C transactions for testing
INSERT INTO b2c_transactions (
    "conversationId",
    "phoneNumber",
    amount,
    "transactionType",
    remarks,
    occasion,
    status,
    "userId"
) VALUES 
(
    'G20-B2C-SAMPLE-001',
    '254708374149',
    1000.00,
    'REFUND',
    'Sample refund transaction',
    'Testing B2C refund',
    'PENDING',
    'sample-user-id'
),
(
    'G20-B2C-SAMPLE-002',
    '254711000000',
    5000.00,
    'SALARY',
    'Sample salary payment',
    'Monthly salary',
    'COMPLETED',
    'sample-user-id'
),
(
    'G20-B2C-SAMPLE-003',
    '254733000000',
    500.00,
    'PROMOTION',
    'Sample promotional payment',
    'Customer reward',
    'COMPLETED',
    'sample-user-id'
)
ON CONFLICT ("conversationId") DO NOTHING;

-- Create a view for transaction statistics
CREATE OR REPLACE VIEW b2c_transaction_stats AS
SELECT 
    "transactionType",
    status,
    COUNT(*) as transaction_count,
    SUM(amount) as total_amount,
    AVG(amount) as average_amount,
    MIN(amount) as min_amount,
    MAX(amount) as max_amount,
    COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as successful_count,
    COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed_count,
    COUNT(CASE WHEN status IN ('PENDING', 'SUBMITTED') THEN 1 END) as pending_count
FROM b2c_transactions
GROUP BY "transactionType", status;

-- Create a function to get daily transaction summary
CREATE OR REPLACE FUNCTION get_b2c_daily_summary(target_date DATE DEFAULT CURRENT_DATE)
RETURNS TABLE (
    transaction_type VARCHAR(20),
    total_transactions BIGINT,
    total_amount DECIMAL(15,2),
    successful_transactions BIGINT,
    failed_transactions BIGINT,
    pending_transactions BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t."transactionType",
        COUNT(*) as total_transactions,
        SUM(t.amount) as total_amount,
        COUNT(CASE WHEN t.status = 'COMPLETED' THEN 1 END) as successful_transactions,
        COUNT(CASE WHEN t.status = 'FAILED' THEN 1 END) as failed_transactions,
        COUNT(CASE WHEN t.status IN ('PENDING', 'SUBMITTED') THEN 1 END) as pending_transactions
    FROM b2c_transactions t
    WHERE DATE(t."initiatedAt") = target_date
    GROUP BY t."transactionType";
END;
$$ LANGUAGE plpgsql;

-- Verify table creation
SELECT 'b2c_transactions table created successfully' as status, COUNT(*) as sample_records FROM b2c_transactions;

-- Show table structure
\d b2c_transactions;

-- Show sample data
SELECT 
    "conversationId",
    "phoneNumber",
    amount,
    "transactionType",
    status,
    "initiatedAt"
FROM b2c_transactions 
ORDER BY "initiatedAt" DESC 
LIMIT 5;
