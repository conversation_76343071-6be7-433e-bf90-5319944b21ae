const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Create test results directory if it doesn't exist
const resultsDir = path.join(__dirname, 'test-results');
if (!fs.existsSync(resultsDir)) {
  fs.mkdirSync(resultsDir);
}

// Function to run a test script
function runTest(scriptName) {
  return new Promise((resolve, reject) => {
    console.log(`\n🚀 Running ${scriptName}...`);
    
    const testProcess = spawn('node', [path.join(__dirname, scriptName)], {
      stdio: 'inherit'
    });
    
    testProcess.on('close', (code) => {
      if (code === 0) {
        console.log(`\n✅ ${scriptName} completed successfully`);
        resolve();
      } else {
        console.error(`\n❌ ${scriptName} failed with code ${code}`);
        reject(new Error(`${scriptName} failed with code ${code}`));
      }
    });
    
    testProcess.on('error', (err) => {
      console.error(`\n❌ Failed to start ${scriptName}: ${err.message}`);
      reject(err);
    });
  });
}

// Main function to run all tests
async function runAllTests() {
  console.log('🧪 Starting test suite...');
  
  try {
    // Run model tests first
    await runTest('test-models.js');
    
    // Then run API tests
    await runTest('test-api.js');
    
    console.log('\n✅ All tests completed successfully!');
    console.log(`\nTest results are available in the ${resultsDir} directory.`);
  } catch (error) {
    console.error('\n❌ Test suite failed:', error.message);
    process.exit(1);
  }
}

// Run all tests
runAllTests();
