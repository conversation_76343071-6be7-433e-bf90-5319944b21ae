const express = require('express');
const router = express.Router();
const ordersController = require('../controllers/orders.controller');
const { authenticate, authorize, optionalAuth } = require('../middlewares/auth.middleware');
const { validateOrder, validateOrderStatus } = require('../validators/order.validator');

// Public routes (with optional auth)
router.post(
  '/',
  optionalAuth,
  validateOrder,
  ordersController.createOrder
);

// Protected routes (authenticated users)
router.get(
  '/user',
  authenticate,
  ordersController.getUserOrders
);

router.get(
  '/:id',
  authenticate,
  ordersController.getOrder
);

router.post(
  '/:id/cancel',
  authenticate,
  ordersController.cancelOrder
);

// Admin routes
router.get(
  '/',
  authenticate,
  authorize(['admin']),
  ordersController.getAllOrders
);

router.patch(
  '/:id/status',
  authenticate,
  authorize(['admin']),
  validateOrderStatus,
  ordersController.updateOrderStatus
);

module.exports = router;
