# G20 Shop Frontend

This is the frontend application for the G20 Shop e-commerce platform, built with React, TypeScript, and Tailwind CSS.

## Directory Structure

```
frontend/
├── public/                   # Static files
├── src/                      # Source code
│   ├── assets/               # Images, fonts, etc.
│   ├── components/           # Reusable UI components
│   │   ├── common/           # Common components used across the app
│   │   ├── layout/           # Layout components (<PERSON>er, Footer, etc.)
│   │   └── [feature]/        # Feature-specific components
│   ├── contexts/             # React context providers
│   ├── hooks/                # Custom React hooks
│   ├── pages/                # Page components
│   ├── services/             # API service functions
│   ├── types/                # TypeScript type definitions
│   ├── utils/                # Utility functions
│   ├── App.tsx               # Main App component
│   └── index.tsx             # Entry point
├── .env                      # Environment variables
├── package.json              # Frontend dependencies
└── ...
```

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Create a `.env` file with the following variables:
   ```
   VITE_API_URL=http://localhost:3000/api
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

## Building for Production

```bash
npm run build
```

This will create a `dist` folder with the compiled assets.

## Component Guidelines

### Component Structure

Each component should follow this structure:

```tsx
import React from 'react';
import './ComponentName.css'; // If using CSS modules

interface ComponentNameProps {
  // Props definition
}

export const ComponentName: React.FC<ComponentNameProps> = ({ prop1, prop2 }) => {
  // Component logic
  
  return (
    <div className="component-name">
      {/* Component JSX */}
    </div>
  );
};
```

### Component Organization

- Place shared components in the `components/common` directory
- Place layout components in the `components/layout` directory
- Place feature-specific components in their respective feature directories

## State Management

- Use React Context for global state management
- Use React hooks for component-level state management
- Place context providers in the `contexts` directory
- Place custom hooks in the `hooks` directory

## API Services

- Place API service functions in the `services` directory
- Use axios for API requests
- Handle errors consistently across all API calls

## TypeScript Guidelines

- Define interfaces for all props
- Use type inference where possible
- Define reusable types in the `types` directory
- Use strict type checking

## Styling Guidelines

- Use Tailwind CSS for styling
- Use CSS modules for component-specific styles
- Follow the design system for consistent UI
