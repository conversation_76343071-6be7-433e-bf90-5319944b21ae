const { body, param } = require('express-validator');
const { validate } = require('../middlewares/validation.middleware');

exports.validateOrder = [
  body('shippingAddress')
    .notEmpty().withMessage('Shipping address is required')
    .isObject().withMessage('Shipping address must be an object'),

  body('shippingAddress.fullName')
    .notEmpty().withMessage('Full name is required')
    .isString().withMessage('Full name must be a string'),

  body('shippingAddress.addressLine1')
    .notEmpty().withMessage('Address line 1 is required')
    .isString().withMessage('Address line 1 must be a string'),

  body('shippingAddress.city')
    .notEmpty().withMessage('City is required')
    .isString().withMessage('City must be a string'),

  body('shippingAddress.state')
    .notEmpty().withMessage('State is required')
    .isString().withMessage('State must be a string'),

  body('shippingAddress.postalCode')
    .notEmpty().withMessage('Postal code is required')
    .isString().withMessage('Postal code must be a string'),

  body('shippingAddress.country')
    .notEmpty().withMessage('Country is required')
    .isString().withMessage('Country must be a string'),

  body('shippingAddress.phone')
    .notEmpty().withMessage('Phone is required')
    .isString().withMessage('Phone must be a string'),

  body('billingAddress')
    .optional()
    .isObject().withMessage('Billing address must be an object'),

  body('paymentMethod')
    .notEmpty().withMessage('Payment method is required')
    .isString().withMessage('Payment method must be a string'),

  body('email')
    .notEmpty().withMessage('Email is required')
    .isEmail().withMessage('Email must be valid'),

  body('phone')
    .optional()
    .isString().withMessage('Phone must be a string'),

  body('notes')
    .optional()
    .isString().withMessage('Notes must be a string'),

  body('couponCode')
    .optional()
    .isString().withMessage('Coupon code must be a string'),

  validate
];

exports.validateOrderStatus = [
  param('id')
    .isUUID().withMessage('Order ID must be a valid UUID'),

  body('status')
    .notEmpty().withMessage('Status is required')
    .isIn(['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'])
    .withMessage('Invalid status value'),

  body('trackingNumber')
    .optional()
    .isString().withMessage('Tracking number must be a string'),

  body('trackingUrl')
    .optional()
    .isURL().withMessage('Tracking URL must be a valid URL'),

  body('cancelReason')
    .optional()
    .isString().withMessage('Cancel reason must be a string'),

  validate
];
