# M-Pesa Daraja API Configuration
# Copy this to your .env file and update with your actual credentials

# =============================================================================
# M-PESA ENVIRONMENT CONFIGURATION
# =============================================================================

# Environment: 'sandbox' for testing, 'production' for live
MPESA_ENVIRONMENT=sandbox

# =============================================================================
# SANDBOX CONFIGURATION (for testing)
# =============================================================================

# Sandbox Consumer Key (get from https://developer.safaricom.co.ke)
MPESA_CONSUMER_KEY=6HOHpsVk1RwJXSCf8mGTUHrfyYOyqJ3V7bqX10nA3ak47asr

# Sandbox Consumer Secret
MPESA_CONSUMER_SECRET=7dP9oVZ2QwJTns0VqLAr3v82HfG4hkNntmT2b7ki1cUO42ZyRPOj6hGBDeCE1cWJ

# Sandbox Business Short Code (use 174379 for testing)
MPESA_BUSINESS_SHORT_CODE=174379

# Sandbox Lipa Na M-Pesa Online Passkey
MPESA_LIPA_NA_MPESA_ONLINE_PASSKEY=bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919

# =============================================================================
# PRODUCTION CONFIGURATION (for live environment)
# =============================================================================

# Production Consumer Key (uncomment and use when going live)
# MPESA_PRODUCTION_CONSUMER_KEY=your_production_consumer_key_here

# Production Consumer Secret
# MPESA_PRODUCTION_CONSUMER_SECRET=your_production_consumer_secret_here

# Production Business Short Code (your actual PayBill number)
# MPESA_PRODUCTION_BUSINESS_SHORT_CODE=your_paybill_number

# Production Lipa Na M-Pesa Online Passkey
# MPESA_PRODUCTION_LIPA_NA_MPESA_ONLINE_PASSKEY=your_production_passkey

# =============================================================================
# CALLBACK URLS
# =============================================================================

# Base URL for your application (update with your domain)
MPESA_BASE_CALLBACK_URL=https://yourdomain.com/api/mpesa

# STK Push Callback URL
MPESA_CALLBACK_URL=${MPESA_BASE_CALLBACK_URL}/callback

# C2B Confirmation URL
MPESA_CONFIRMATION_URL=${MPESA_BASE_CALLBACK_URL}/confirmation

# C2B Validation URL
MPESA_VALIDATION_URL=${MPESA_BASE_CALLBACK_URL}/validation

# =============================================================================
# B2C CONFIGURATION (for refunds and payouts)
# =============================================================================

# Initiator Name (for B2C transactions)
MPESA_INITIATOR_NAME=bravin kipkulei

# Security Credential (encrypted password for B2C)
MPESA_SECURITY_CREDENTIAL=your_encrypted_security_credential

# =============================================================================
# API ENDPOINTS
# =============================================================================

# Sandbox Base URL
MPESA_SANDBOX_BASE_URL=https://sandbox.safaricom.co.ke

# Production Base URL
MPESA_PRODUCTION_BASE_URL=https://api.safaricom.co.ke

# =============================================================================
# ADDITIONAL CONFIGURATION
# =============================================================================

# Timeout for API requests (in milliseconds)
MPESA_API_TIMEOUT=30000

# Maximum retry attempts for failed requests
MPESA_MAX_RETRIES=3

# Enable/disable M-Pesa logging
MPESA_ENABLE_LOGGING=true

# =============================================================================
# WEBHOOK SECURITY
# =============================================================================

# IP addresses allowed to send callbacks (Safaricom IPs)
MPESA_ALLOWED_IPS=***************,***************,***************,***************,***************,**************,***************,***************,***************,***************,**************

# Webhook secret for additional security (optional)
MPESA_WEBHOOK_SECRET=your_webhook_secret_here

# =============================================================================
# RATE LIMITING
# =============================================================================

# Rate limit for payment requests (requests per minute)
MPESA_PAYMENT_RATE_LIMIT=10

# Rate limit for callback endpoints (requests per minute)
MPESA_CALLBACK_RATE_LIMIT=100

# =============================================================================
# NOTIFICATION SETTINGS
# =============================================================================

# Enable SMS notifications for successful payments
MPESA_ENABLE_SMS_NOTIFICATIONS=true

# Enable email notifications for failed payments
MPESA_ENABLE_EMAIL_NOTIFICATIONS=true

# Admin email for M-Pesa notifications
MPESA_ADMIN_EMAIL=<EMAIL>

# =============================================================================
# RECONCILIATION SETTINGS
# =============================================================================

# Enable automatic reconciliation
MPESA_ENABLE_AUTO_RECONCILIATION=false

# Reconciliation schedule (cron format)
MPESA_RECONCILIATION_SCHEDULE=0 2 * * *

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================

# Test phone numbers (sandbox only)
MPESA_TEST_PHONE_1=************
MPESA_TEST_PHONE_2=254711XXXXXX
MPESA_TEST_PHONE_3=254733XXXXXX

# Test amounts for different scenarios
MPESA_TEST_AMOUNT_SUCCESS=1
MPESA_TEST_AMOUNT_INSUFFICIENT_FUNDS=2
MPESA_TEST_AMOUNT_INVALID_ACCOUNT=3
MPESA_TEST_AMOUNT_GENERIC_ERROR=4

# =============================================================================
# INSTRUCTIONS FOR SETUP
# =============================================================================

# 1. Create a Safaricom Developer Account:
#    - Go to https://developer.safaricom.co.ke
#    - Register for an account
#    - Create a new app

# 2. Get Your Credentials:
#    - Consumer Key and Consumer Secret from your app
#    - For sandbox, use the provided test credentials
#    - For production, you'll need to apply for a PayBill number

# 3. Configure Callback URLs:
#    - Update MPESA_BASE_CALLBACK_URL with your domain
#    - Ensure your server can receive HTTPS requests
#    - Register your URLs with Safaricom (for C2B)

# 4. Test in Sandbox:
#    - Use the sandbox credentials provided
#    - Test with the provided test phone numbers
#    - Use different amounts to test various scenarios

# 5. Go Live:
#    - Apply for production credentials
#    - Update environment to 'production'
#    - Update all production credentials
#    - Test thoroughly before launching

# =============================================================================
# SECURITY NOTES
# =============================================================================

# - Never commit actual credentials to version control
# - Use environment variables in production
# - Implement IP whitelisting for callbacks
# - Use HTTPS for all callback URLs
# - Regularly rotate your credentials
# - Monitor for suspicious activity
# - Implement proper logging and alerting

# =============================================================================
# SUPPORT CONTACTS
# =============================================================================

# Safaricom Developer Support: <EMAIL>
# Developer Portal: https://developer.safaricom.co.ke
# Documentation: https://developer.safaricom.co.ke/Documentation
