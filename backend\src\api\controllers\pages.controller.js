/**
 * Pages Controller
 * Handles static page content requests
 */

/**
 * Get About page content
 */
exports.getAboutPage = async (req, res) => {
  try {
    const aboutContent = {
      title: "About G20Shop",
      content: {
        mission: "We are dedicated to providing high-quality laptop and PC accessories along with professional repair and maintenance services.",
        story: "Founded with a passion for technology, G20Shop has been serving customers with reliable products and expert services. Our team of experienced technicians ensures that your devices receive the best care possible.",
        values: [
          "Quality products and services",
          "Customer satisfaction",
          "Expert technical support",
          "Competitive pricing",
          "Fast and reliable service"
        ],
        services: [
          "Laptop and PC accessories sales",
          "Device repair and maintenance",
          "Technical consultation",
          "Warranty support",
          "Custom solutions"
        ]
      },
      contact: {
        email: "<EMAIL>",
        phone: "+****************",
        address: "123 Tech Street, Digital City, DC 12345"
      }
    };

    res.json({
      success: true,
      data: aboutContent
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to load about page content',
      error: error.message
    });
  }
};

/**
 * Get Support page content
 */
exports.getSupportPage = async (req, res) => {
  try {
    const supportContent = {
      title: "Customer Support",
      content: {
        description: "We're here to help you with any questions or issues you may have.",
        supportChannels: [
          {
            type: "Live Chat",
            description: "Chat with our support team in real-time",
            availability: "Monday - Friday, 9 AM - 6 PM EST",
            status: "available"
          },
          {
            type: "Email Support",
            description: "Send us an email and we'll respond within 24 hours",
            contact: "<EMAIL>",
            responseTime: "Within 24 hours"
          },
          {
            type: "Phone Support",
            description: "Call us for immediate assistance",
            contact: "+****************",
            availability: "Monday - Friday, 9 AM - 6 PM EST"
          }
        ],
        commonIssues: [
          {
            title: "Order Status",
            description: "Track your order or get updates on delivery"
          },
          {
            title: "Returns & Refunds",
            description: "Information about our return policy and refund process"
          },
          {
            title: "Product Information",
            description: "Get detailed information about our products"
          },
          {
            title: "Technical Support",
            description: "Help with device setup, troubleshooting, and repairs"
          },
          {
            title: "Account Issues",
            description: "Problems with login, password reset, or account settings"
          }
        ],
        repairServices: {
          description: "Professional repair and maintenance services for your devices",
          services: [
            "Laptop screen replacement",
            "Keyboard and trackpad repair",
            "Battery replacement",
            "Hardware upgrades",
            "Software troubleshooting",
            "Virus removal",
            "Data recovery"
          ],
          turnaroundTime: "Most repairs completed within 2-3 business days",
          warranty: "90-day warranty on all repair services"
        }
      }
    };

    res.json({
      success: true,
      data: supportContent
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to load support page content',
      error: error.message
    });
  }
};

/**
 * Get Contact page content
 */
exports.getContactPage = async (req, res) => {
  try {
    const contactContent = {
      title: "Contact Us",
      content: {
        description: "Get in touch with us for any inquiries or support needs.",
        contactInfo: {
          address: {
            street: "123 Tech Street",
            city: "Digital City",
            state: "DC",
            zipCode: "12345",
            country: "United States"
          },
          phone: "+****************",
          email: "<EMAIL>",
          businessHours: {
            weekdays: "Monday - Friday: 9:00 AM - 6:00 PM EST",
            weekends: "Saturday: 10:00 AM - 4:00 PM EST",
            sunday: "Closed"
          }
        },
        departments: [
          {
            name: "Sales",
            email: "<EMAIL>",
            description: "Product inquiries and purchase assistance"
          },
          {
            name: "Technical Support",
            email: "<EMAIL>",
            description: "Technical assistance and troubleshooting"
          },
          {
            name: "Repairs",
            email: "<EMAIL>",
            description: "Repair services and maintenance"
          },
          {
            name: "General Inquiries",
            email: "<EMAIL>",
            description: "General questions and information"
          }
        ]
      }
    };

    res.json({
      success: true,
      data: contactContent
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to load contact page content',
      error: error.message
    });
  }
};

/**
 * Get Privacy Policy page content
 */
exports.getPrivacyPage = async (req, res) => {
  try {
    const privacyContent = {
      title: "Privacy Policy",
      lastUpdated: "2024-01-01",
      content: {
        introduction: "This Privacy Policy describes how G20Shop collects, uses, and protects your personal information.",
        sections: [
          {
            title: "Information We Collect",
            content: "We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us for support."
          },
          {
            title: "How We Use Your Information",
            content: "We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you."
          },
          {
            title: "Information Sharing",
            content: "We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy."
          },
          {
            title: "Data Security",
            content: "We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction."
          },
          {
            title: "Your Rights",
            content: "You have the right to access, update, or delete your personal information. Contact us if you wish to exercise these rights."
          }
        ]
      }
    };

    res.json({
      success: true,
      data: privacyContent
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to load privacy policy content',
      error: error.message
    });
  }
};

/**
 * Get Terms of Service page content
 */
exports.getTermsPage = async (req, res) => {
  try {
    const termsContent = {
      title: "Terms of Service",
      lastUpdated: "2024-01-01",
      content: {
        introduction: "These Terms of Service govern your use of G20Shop's website and services.",
        sections: [
          {
            title: "Acceptance of Terms",
            content: "By accessing and using our services, you accept and agree to be bound by the terms and provision of this agreement."
          },
          {
            title: "Use License",
            content: "Permission is granted to temporarily use our website for personal, non-commercial transitory viewing only."
          },
          {
            title: "Disclaimer",
            content: "The materials on G20Shop's website are provided on an 'as is' basis. G20Shop makes no warranties, expressed or implied."
          },
          {
            title: "Limitations",
            content: "In no event shall G20Shop or its suppliers be liable for any damages arising out of the use or inability to use our services."
          },
          {
            title: "Governing Law",
            content: "These terms and conditions are governed by and construed in accordance with the laws of the jurisdiction in which G20Shop operates."
          }
        ]
      }
    };

    res.json({
      success: true,
      data: termsContent
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to load terms of service content',
      error: error.message
    });
  }
};

/**
 * Get FAQ page content
 */
exports.getFaqPage = async (req, res) => {
  try {
    const faqContent = {
      title: "Frequently Asked Questions",
      content: {
        categories: [
          {
            category: "Orders & Shipping",
            questions: [
              {
                question: "How long does shipping take?",
                answer: "Standard shipping takes 3-5 business days. Express shipping takes 1-2 business days."
              },
              {
                question: "Can I track my order?",
                answer: "Yes, you'll receive a tracking number via email once your order ships."
              },
              {
                question: "What is your return policy?",
                answer: "We offer a 30-day return policy for most items in original condition."
              }
            ]
          },
          {
            category: "Products",
            questions: [
              {
                question: "Do you offer warranties on products?",
                answer: "Yes, all products come with manufacturer warranties. Extended warranties are available for purchase."
              },
              {
                question: "Are your products genuine?",
                answer: "Yes, we only sell genuine products from authorized distributors."
              }
            ]
          },
          {
            category: "Repair Services",
            questions: [
              {
                question: "How long do repairs take?",
                answer: "Most repairs are completed within 2-3 business days."
              },
              {
                question: "Do you provide warranties on repairs?",
                answer: "Yes, all repairs come with a 90-day warranty."
              },
              {
                question: "Can I get a quote before repair?",
                answer: "Yes, we provide free diagnostics and quotes before any repair work."
              }
            ]
          }
        ]
      }
    };

    res.json({
      success: true,
      data: faqContent
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to load FAQ content',
      error: error.message
    });
  }
};
