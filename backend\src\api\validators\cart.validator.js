const { body } = require('express-validator');
const { validate } = require('../middlewares/validation.middleware');

exports.validateCartItem = [
  body('productId')
    .notEmpty().withMessage('Product ID is required')
    .isString().withMessage('Product ID must be a string')
    .custom((value) => {
      // Accept both UUID and numeric string formats
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value);
      const isNumeric = /^\d+$/.test(value.toString());

      if (!isUUID && !isNumeric) {
        throw new Error('Product ID must be a valid UUID or numeric ID');
      }
      return true;
    }),

  body('quantity')
    .optional()
    .isInt({ min: 1 }).withMessage('Quantity must be at least 1'),

  validate
];
