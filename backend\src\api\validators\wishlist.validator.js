const { body } = require('express-validator');
const { validate } = require('../middlewares/validation.middleware');

exports.validateWishlist = [
  body('name')
    .notEmpty().withMessage('Wishlist name is required')
    .isString().withMessage('Wishlist name must be a string')
    .isLength({ min: 1, max: 50 }).withMessage('Wishlist name must be between 1 and 50 characters'),
  
  body('isPublic')
    .optional()
    .isBoolean().withMessage('isPublic must be a boolean'),
  
  validate
];

exports.validateWishlistItem = [
  body('productId')
    .notEmpty().withMessage('Product ID is required')
    .isUUID().withMessage('Product ID must be a valid UUID'),
  
  body('notes')
    .optional()
    .isString().withMessage('Notes must be a string'),
  
  validate
];
