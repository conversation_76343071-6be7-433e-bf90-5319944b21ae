const express = require('express');
const router = express.Router();
const adminController = require('../controllers/admin.controller');
const { authenticateAdmin } = require('../middlewares/clerk-auth.middleware');
const { validateProductCreate, validateProductUpdate } = require('../validators/product.validator');
const upload = require('../middlewares/upload.middleware');

// All admin routes require admin authentication
router.use(authenticateAdmin);

// Dashboard
router.get('/dashboard', adminController.getDashboard);

// Inventory Management
router.get('/inventory', adminController.getInventory);
router.get('/inventory/stats', adminController.getInventoryStats);
router.post('/inventory/products', validateProductCreate, adminController.addProduct);
router.post('/inventory/products/bulk', adminController.bulkImportProducts);
router.get('/inventory/import-jobs', adminController.getImportJobs);
router.post('/inventory/import-jobs', adminController.createImportJob);
router.put('/inventory/import-jobs/:id', adminController.updateImportJob);
router.delete('/inventory/import-jobs/:id', adminController.deleteImportJob);
router.post('/inventory/import-jobs/:id/trigger', adminController.triggerImportJob);
router.get('/inventory/import-jobs/:id/logs', adminController.getImportJobLogs);
router.post('/setup/import-tables', adminController.setupImportTables);
router.put('/inventory/products/:id', validateProductUpdate, adminController.updateProduct);
router.delete('/inventory/products/:id', adminController.deleteProduct);
router.patch('/inventory/products/:id/stock', adminController.updateStock);

// User Management
router.get('/users', adminController.getUsers);
router.patch('/users/:id/role', adminController.updateUserRole);
router.patch('/users/:id/status', adminController.updateUserStatus);
router.patch('/users/:id', adminController.updateUser);

module.exports = router;
