import './index.css';
import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { <PERSON><PERSON>rovider } from '@clerk/clerk-react';
// Import the App component you want to use:
// - App: Simple landing page layout
// - AppWithRouting: Full application with routing
// - AppWithClerkRouting: Full application with Clerk authentication and routing
// import App from "./App";
// import { AppWithRouting as App } from "./AppWithRouting";
import { AppWithClerkRouting as App } from "./AppWithClerkRouting";
import axios from 'axios';

// Import Clerk publishable key
const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

if (!PUBLISHABLE_KEY) {
  throw new Error("Missing Publishable Key");
}

// Set base URL for API requests using Vite environment variables
axios.defaults.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

// Add request interceptor for Clerk authentication
axios.interceptors.request.use(
  async (config) => {
    // Get Clerk session token if available
    try {
      const clerkToken = await window.Clerk?.session?.getToken();
      if (clerkToken) {
        config.headers.Authorization = `Bearer ${clerkToken}`;
      }
    } catch (error) {
      console.warn('Failed to get Clerk token:', error);
    }
    return config;
  },
  (error) => Promise.reject(error)
);

const root = ReactDOM.createRoot(document.getElementById("root") as HTMLElement);
root.render(
  <React.StrictMode>
    <ClerkProvider publishableKey={PUBLISHABLE_KEY}>
      <App />
    </ClerkProvider>
  </React.StrictMode>
);
