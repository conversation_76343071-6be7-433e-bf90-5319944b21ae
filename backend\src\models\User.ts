import mongoose, { Document, Model, Schema } from 'mongoose';
import bcrypt from 'bcrypt';
import { IUser, UserRole } from '../types';
import config from '../config';
import { logger } from '../utils/logger';

/**
 * User Document interface extending both IUser and Mongoose Document
 */
export interface IUserDocument extends IUser, Document {
  createdAt: Date;
  updatedAt: Date;
}

/**
 * User Model interface for static methods
 */
export interface IUserModel extends Model<IUserDocument> {
  // Add static methods if needed
}

/**
 * Address schema for user
 */
const addressSchema = new Schema({
  street: { type: String, trim: true },
  city: { type: String, trim: true },
  state: { type: String, trim: true },
  zip: { type: String, trim: true },
  country: { type: String, trim: true },
}, { _id: false });

/**
 * User schema definition
 */
const userSchema = new Schema<IUserDocument>({
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    trim: true,
    lowercase: true,
    index: true,
    match: [
      /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
      'Please provide a valid email address'
    ],
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters long'],
    select: false, // Don't include password in query results by default
  },
  role: {
    type: String,
    enum: Object.values(UserRole),
    default: UserRole.CUSTOMER,
    index: true,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  lastLogin: {
    type: Date,
    default: null,
  },
  phone: {
    type: String,
    trim: true,
  },
  address: addressSchema,
  resetPasswordToken: String,
  resetPasswordExpires: Date,
}, {
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: (doc, ret) => {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      delete ret.password;
      return ret;
    }
  },
  toObject: {
    virtuals: true,
    transform: (doc, ret) => {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      delete ret.password;
      return ret;
    }
  }
});

/**
 * Hash password before saving
 */
userSchema.pre<IUserDocument>('save', async function (next) {
  // Only hash password if it was modified (or is new)
  if (!this.isModified('password')) return next();

  try {
    // Generate salt
    const salt = await bcrypt.genSalt(config.bcrypt.saltRounds);
    
    // Hash password with salt
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error: any) {
    logger.error(`Error hashing password: ${error.message}`);
    next(error);
  }
});

/**
 * Method to check if password is correct
 */
userSchema.methods.checkPassword = async function (
  candidatePassword: string
): Promise<boolean> {
  try {
    // Need to select password field explicitly since it's excluded by default
    const user = await this.constructor.findById(this._id).select('+password');
    
    if (!user || !user.password) {
      return false;
    }
    
    // Compare candidate password with hashed password
    return await bcrypt.compare(candidatePassword, user.password);
  } catch (error: any) {
    logger.error(`Error checking password: ${error.message}`);
    return false;
  }
};

// Create User model
const User = mongoose.model<IUserDocument, IUserModel>('User', userSchema);

export default User;

