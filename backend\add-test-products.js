const { Product, ProductImage } = require('./src/db/models');

async function addTestProducts() {
  try {
    console.log('🛍️ Adding test products for cart testing...\n');
    
    // Test products with simple numeric IDs for frontend compatibility
    const testProducts = [
      {
        id: '00000000-0000-0000-0000-000000000001',
        name: 'Gaming Laptop Pro X',
        brand: 'TechGear',
        model: 'Pro X',
        description: 'High-performance gaming laptop with RGB lighting',
        sku: 'TG-LAPTOP-001',
        regularPrice: 1299.99,
        salePrice: 1099.99,
        cost: 800.00,
        stockQuantity: 15,
        minOrderQuantity: 1,
        maxOrderQuantity: 2,
        weight: 2.5,
        featured: true,
        isActive: true,
        slug: 'gaming-laptop-pro-x'
      },
      {
        id: '00000000-0000-0000-0000-000000000002',
        name: 'Mechanical RGB Keyboard',
        brand: 'TechGear',
        model: 'K2',
        description: 'Mechanical keyboard with customizable RGB lighting',
        sku: 'TG-KB-002',
        regularPrice: 129.99,
        salePrice: null,
        cost: 65.00,
        stockQuantity: 50,
        minOrderQuantity: 1,
        maxOrderQuantity: 3,
        weight: 0.9,
        featured: true,
        isActive: true,
        slug: 'mechanical-rgb-keyboard'
      },
      {
        id: '00000000-0000-0000-0000-000000000003',
        name: 'Wireless Gaming Mouse',
        brand: 'TechGear',
        model: 'M3',
        description: 'High-precision wireless gaming mouse',
        sku: 'TG-MOUSE-003',
        regularPrice: 79.99,
        salePrice: 69.99,
        cost: 35.00,
        stockQuantity: 100,
        minOrderQuantity: 1,
        maxOrderQuantity: 5,
        weight: 0.2,
        featured: true,
        isActive: true,
        slug: 'wireless-gaming-mouse'
      },
      {
        id: '00000000-0000-0000-0000-000000000004',
        name: 'RTX 4070 Graphics Card',
        brand: 'TechGear',
        model: '4070',
        description: 'High-performance graphics card for gaming',
        sku: 'TG-GPU-004',
        regularPrice: 599.99,
        salePrice: null,
        cost: 400.00,
        stockQuantity: 25,
        minOrderQuantity: 1,
        maxOrderQuantity: 1,
        weight: 1.5,
        featured: true,
        isActive: true,
        slug: 'rtx-4070-graphics-card'
      }
    ];
    
    for (const productData of testProducts) {
      try {
        // Check if product already exists
        const existing = await Product.findByPk(productData.id);
        if (existing) {
          console.log(`⚠️ Product ${productData.name} already exists, skipping...`);
          continue;
        }
        
        // Create product
        const product = await Product.create(productData);
        console.log(`✅ Created product: ${product.name} (ID: ${product.id})`);
        
        // Add a primary image
        await ProductImage.create({
          productId: product.id,
          imageUrl: `https://placehold.co/300x300?text=${encodeURIComponent(product.name)}`,
          altText: product.name,
          isPrimary: true,
          displayOrder: 1
        });
        
      } catch (error) {
        console.error(`❌ Error creating product ${productData.name}:`, error.message);
      }
    }
    
    console.log('\n🎉 Test products setup complete!');
    console.log('\n💡 You can now test the cart with these product IDs:');
    console.log('   - 00000000-0000-0000-0000-000000000001 (Gaming Laptop)');
    console.log('   - 00000000-0000-0000-0000-000000000002 (Keyboard)');
    console.log('   - 00000000-0000-0000-0000-000000000003 (Mouse)');
    console.log('   - 00000000-0000-0000-0000-000000000004 (Graphics Card)');
    
  } catch (error) {
    console.error('❌ Error adding test products:', error.message);
  } finally {
    process.exit(0);
  }
}

addTestProducts();
