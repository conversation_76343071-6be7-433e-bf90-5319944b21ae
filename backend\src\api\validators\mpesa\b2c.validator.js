const { body, validationResult } = require('express-validator');

/**
 * M-Pesa B2C Validation Middleware
 * Validates B2C payment requests before processing
 */

// Helper function to format phone number for validation
const formatPhoneNumber = (phoneNumber) => {
  const cleaned = phoneNumber.replace(/[\s\-\(\)]/g, '');
  if (cleaned.startsWith('0')) {
    return `254${cleaned.substring(1)}`;
  } else if (cleaned.startsWith('254')) {
    return cleaned;
  } else if (cleaned.startsWith('+254')) {
    return cleaned.substring(1);
  } else {
    return `254${cleaned}`;
  }
};

// Custom phone number validator
const isValidKenyanPhone = (value) => {
  const formatted = formatPhoneNumber(value);
  return /^254[0-9]{9}$/.test(formatted);
};

// Handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array().map(error => ({
        field: error.path,
        message: error.msg,
        value: error.value
      }))
    });
  }
  next();
};

/**
 * Validate general B2C payment request
 */
const validateB2CPayment = [
  body('phoneNumber')
    .notEmpty()
    .withMessage('Phone number is required')
    .custom(isValidKenyanPhone)
    .withMessage('Invalid Kenyan phone number format'),
  
  body('amount')
    .isFloat({ min: 1, max: 150000 })
    .withMessage('Amount must be between KES 1 and KES 150,000')
    .toFloat(),
  
  body('transactionType')
    .optional()
    .isIn(['REFUND', 'SALARY', 'PROMOTION', 'GENERAL'])
    .withMessage('Invalid transaction type'),
  
  body('remarks')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Remarks must be between 1 and 100 characters'),
  
  body('occasion')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Occasion must be between 1 and 100 characters'),
  
  body('orderId')
    .optional()
    .isUUID()
    .withMessage('Invalid order ID format'),
  
  body('metadata')
    .optional()
    .isObject()
    .withMessage('Metadata must be an object'),
  
  handleValidationErrors
];

/**
 * Validate B2C refund request
 */
const validateB2CRefund = [
  body('orderId')
    .notEmpty()
    .withMessage('Order ID is required')
    .isUUID()
    .withMessage('Invalid order ID format'),
  
  body('phoneNumber')
    .notEmpty()
    .withMessage('Phone number is required')
    .custom(isValidKenyanPhone)
    .withMessage('Invalid Kenyan phone number format'),
  
  body('amount')
    .isFloat({ min: 1, max: 150000 })
    .withMessage('Refund amount must be between KES 1 and KES 150,000')
    .toFloat(),
  
  body('reason')
    .optional()
    .isLength({ min: 1, max: 200 })
    .withMessage('Reason must be between 1 and 200 characters'),
  
  handleValidationErrors
];

/**
 * Validate B2C salary payment request
 */
const validateB2CSalary = [
  body('employeePhone')
    .notEmpty()
    .withMessage('Employee phone number is required')
    .custom(isValidKenyanPhone)
    .withMessage('Invalid Kenyan phone number format'),
  
  body('amount')
    .isFloat({ min: 1, max: 150000 })
    .withMessage('Salary amount must be between KES 1 and KES 150,000')
    .toFloat(),
  
  body('employeeId')
    .notEmpty()
    .withMessage('Employee ID is required')
    .isLength({ min: 1, max: 50 })
    .withMessage('Employee ID must be between 1 and 50 characters'),
  
  body('payrollPeriod')
    .notEmpty()
    .withMessage('Payroll period is required')
    .isLength({ min: 1, max: 50 })
    .withMessage('Payroll period must be between 1 and 50 characters'),
  
  handleValidationErrors
];

/**
 * Validate B2C promotional payment request
 */
const validateB2CPromotion = [
  body('customerPhone')
    .notEmpty()
    .withMessage('Customer phone number is required')
    .custom(isValidKenyanPhone)
    .withMessage('Invalid Kenyan phone number format'),
  
  body('amount')
    .isFloat({ min: 1, max: 150000 })
    .withMessage('Promotional amount must be between KES 1 and KES 150,000')
    .toFloat(),
  
  body('promotionCode')
    .notEmpty()
    .withMessage('Promotion code is required')
    .isLength({ min: 1, max: 50 })
    .withMessage('Promotion code must be between 1 and 50 characters'),
  
  body('campaignId')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Campaign ID must be between 1 and 50 characters'),
  
  handleValidationErrors
];

/**
 * Validate transaction status query parameters
 */
const validateTransactionQuery = [
  body('status')
    .optional()
    .isIn(['PENDING', 'SUBMITTED', 'COMPLETED', 'FAILED', 'TIMEOUT', 'CANCELLED'])
    .withMessage('Invalid status'),
  
  body('transactionType')
    .optional()
    .isIn(['REFUND', 'SALARY', 'PROMOTION', 'GENERAL'])
    .withMessage('Invalid transaction type'),
  
  body('phoneNumber')
    .optional()
    .custom(isValidKenyanPhone)
    .withMessage('Invalid Kenyan phone number format'),
  
  body('startDate')
    .optional()
    .isISO8601()
    .withMessage('Invalid start date format'),
  
  body('endDate')
    .optional()
    .isISO8601()
    .withMessage('Invalid end date format'),
  
  body('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer')
    .toInt(),
  
  body('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100')
    .toInt(),
  
  handleValidationErrors
];

/**
 * Validate bulk B2C payment request
 */
const validateBulkB2CPayment = [
  body('payments')
    .isArray({ min: 1, max: 100 })
    .withMessage('Payments must be an array with 1-100 items'),
  
  body('payments.*.phoneNumber')
    .notEmpty()
    .withMessage('Phone number is required for each payment')
    .custom(isValidKenyanPhone)
    .withMessage('Invalid Kenyan phone number format'),
  
  body('payments.*.amount')
    .isFloat({ min: 1, max: 150000 })
    .withMessage('Amount must be between KES 1 and KES 150,000 for each payment')
    .toFloat(),
  
  body('payments.*.transactionType')
    .optional()
    .isIn(['REFUND', 'SALARY', 'PROMOTION', 'GENERAL'])
    .withMessage('Invalid transaction type'),
  
  body('payments.*.remarks')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Remarks must be between 1 and 100 characters'),
  
  body('batchId')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Batch ID must be between 1 and 50 characters'),
  
  handleValidationErrors
];

/**
 * Validate account balance query request
 */
const validateBalanceQuery = [
  body('identifierType')
    .optional()
    .isIn(['1', '2', '4'])
    .withMessage('Invalid identifier type (1=MSISDN, 2=Till Number, 4=Organization Short Code)'),
  
  body('identifier')
    .optional()
    .isLength({ min: 1, max: 20 })
    .withMessage('Identifier must be between 1 and 20 characters'),
  
  handleValidationErrors
];

module.exports = {
  validateB2CPayment,
  validateB2CRefund,
  validateB2CSalary,
  validateB2CPromotion,
  validateTransactionQuery,
  validateBulkB2CPayment,
  validateBalanceQuery,
  formatPhoneNumber,
  isValidKenyanPhone
};
