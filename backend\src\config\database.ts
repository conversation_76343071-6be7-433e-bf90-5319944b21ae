import mongoose from 'mongoose';
import config from './index';
import { logger } from '../utils/logger';

/**
 * MongoDB connection options
 */
const connectionOptions: mongoose.ConnectOptions = {
  // Index settings
  autoIndex: config.server.nodeEnv === 'development', // Only auto-index in development
  autoCreate: true,
  
  // Timeout settings
  serverSelectionTimeoutMS: 5000, // Give up initial connection after 5 seconds
  connectTimeoutMS: 10000, // How long to wait for server selection
  socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
  
  // Connection pool settings
  maxPoolSize: 10, // Maintain up to 10 socket connections
  minPoolSize: 2, // Maintain at least 2 socket connections
  
  // Network settings
  family: 4, // Use IPv4, skip trying IPv6
  
  // Retry settings
  retryWrites: true,
  retryReads: true,
};

/**
 * Setup MongoDB connection event listeners
 * @param connection Mongoose connection instance
 */
const setupConnectionListeners = (connection: mongoose.Connection): void => {
  // Connected
  connection.on('connected', () => {
    logger.info('MongoDB connection established');
  });

  // Disconnected
  connection.on('disconnected', () => {
    logger.warn('MongoDB disconnected');
  });

  // Error
  connection.on('error', (err) => {
    logger.error(`MongoDB connection error: ${err}`);
  });
  
  // Reconnected
  connection.on('reconnected', () => {
    logger.info('MongoDB reconnected successfully');
  });
  
  // Handle application termination
  process.on('SIGINT', async () => {
    await connection.close();
    logger.info('MongoDB connection closed due to app termination');
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    await connection.close();
    logger.info('MongoDB connection closed due to app termination');
    process.exit(0);
  });
};

/**
 * Connect to MongoDB
 * @returns Promise that resolves when connected
 */
const connectDB = async (): Promise<void> => {
  try {
    // Enable strict query for proper TypeScript support
    mongoose.set('strictQuery', true);
    
    // Connect to MongoDB
    const conn = await mongoose.connect(config.mongodb.uri, connectionOptions);
    
    logger.info(`MongoDB Connected: ${conn.connection.host}`);
    
    // Setup all event listeners
    setupConnectionListeners(conn.connection);
    
  } catch (error: any) {
    logger.error(`Error connecting to MongoDB: ${error.message}`);
    
    // In development, retry connection
    if (config.server.nodeEnv === 'development') {
      logger.info('Retrying connection in 5 seconds...');
      setTimeout(() => connectDB(), 5000);
    } else {
      // In production, exit the process
      process.exit(1);
    }
  }
};

export default connectDB;

