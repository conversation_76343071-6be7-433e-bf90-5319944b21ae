const express = require('express');
const router = express.Router();
const cartController = require('../controllers/cart.controller');
const { optionalAuth } = require('../middlewares/auth.middleware');
const { validateCartItem } = require('../validators/cart.validator');

// Simple middleware that skips authentication for testing
const skipAuth = (req, res, next) => {
  // Don't set req.user, let it be undefined for guest users
  next();
};

// Debug endpoint to test basic functionality
router.get('/debug', (req, res) => {
  res.json({
    success: true,
    message: 'Cart routes are working',
    timestamp: new Date().toISOString()
  });
});

// Test endpoint to check database connection and products
router.get('/test-db', async (req, res) => {
  try {
    const { Product } = require('../../db/models');
    const products = await Product.findAll({ limit: 3 });
    res.json({
      success: true,
      message: 'Database connection working',
      productCount: products.length,
      products: products.map(p => ({ id: p.id, name: p.name, price: p.regularPrice }))
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Database error',
      error: error.message
    });
  }
});

// Simple add to cart test without validation
router.post('/test-add', async (req, res) => {
  try {
    const { productId, quantity = 1 } = req.body;
    const sessionId = `test_${Date.now()}`;

    console.log('🧪 Test add to cart:', { productId, quantity, sessionId });

    const { Product, Cart, CartItem } = require('../../db/models');

    // Check if product exists
    const product = await Product.findByPk(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found',
        productId
      });
    }

    // Create a test cart
    const cart = await Cart.create({
      sessionId,
      status: 'active'
    });

    // Add item to cart
    const cartItem = await CartItem.create({
      cartId: cart.id,
      productId,
      quantity,
      price: product.salePrice || product.regularPrice,
      totalPrice: (product.salePrice || product.regularPrice) * quantity
    });

    res.json({
      success: true,
      message: 'Test add to cart successful',
      cartItem: {
        id: cartItem.id,
        productId: cartItem.productId,
        quantity: cartItem.quantity,
        price: cartItem.price,
        totalPrice: cartItem.totalPrice
      },
      product: {
        id: product.id,
        name: product.name,
        price: product.regularPrice
      }
    });
  } catch (error) {
    console.error('❌ Test add to cart error:', error);
    res.status(500).json({
      success: false,
      message: 'Test add to cart failed',
      error: error.message,
      stack: error.stack
    });
  }
});

// All routes use skip authentication for now to test cart functionality
router.get('/', skipAuth, cartController.getCart);

router.post(
  '/items',
  skipAuth,
  validateCartItem,
  cartController.addCartItem
);

router.put(
  '/items/:id',
  skipAuth,
  cartController.updateCartItem
);

router.delete(
  '/items/:id',
  skipAuth,
  cartController.removeCartItem
);

router.delete(
  '/',
  skipAuth,
  cartController.clearCart
);

module.exports = router;
