const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

async function testAuthEndpoints() {
  console.log('🔍 Testing Authentication Endpoints...\n');

  // Test 1: Users Profile
  try {
    console.log('Testing: GET /users/profile (without auth)');
    const response = await axios.get(`${API_BASE_URL}/users/profile`, {
      validateStatus: () => true // Don't throw on any status
    });
    
    console.log(`Status: ${response.status}`);
    console.log(`Response:`, JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log(`Error: ${error.message}`);
    if (error.response) {
      console.log(`Status: ${error.response.status}`);
      console.log(`Response:`, JSON.stringify(error.response.data, null, 2));
    }
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 2: Admin Dashboard
  try {
    console.log('Testing: GET /admin/dashboard (without auth)');
    const response = await axios.get(`${API_BASE_URL}/admin/dashboard`, {
      validateStatus: () => true // Don't throw on any status
    });
    
    console.log(`Status: ${response.status}`);
    console.log(`Response:`, JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log(`Error: ${error.message}`);
    if (error.response) {
      console.log(`Status: ${error.response.status}`);
      console.log(`Response:`, JSON.stringify(error.response.data, null, 2));
    }
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 3: Test with invalid Bearer token
  try {
    console.log('Testing: GET /users/profile (with invalid token)');
    const response = await axios.get(`${API_BASE_URL}/users/profile`, {
      headers: {
        'Authorization': 'Bearer invalid_token_here'
      },
      validateStatus: () => true
    });
    
    console.log(`Status: ${response.status}`);
    console.log(`Response:`, JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log(`Error: ${error.message}`);
    if (error.response) {
      console.log(`Status: ${error.response.status}`);
      console.log(`Response:`, JSON.stringify(error.response.data, null, 2));
    }
  }
}

testAuthEndpoints().catch(console.error);
