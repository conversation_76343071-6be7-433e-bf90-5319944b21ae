#!/usr/bin/env node

/**
 * <PERSON>-<PERSON><PERSON><PERSON> Backend Test Runner
 * Script to run M-Pesa backend tests with proper configuration
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-01-15
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`🧪 ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function checkPrerequisites() {
  logHeader('Checking Prerequisites');
  
  // Check if Jest is installed
  try {
    await runCommand('npx', ['jest', '--version'], { stdio: 'pipe' });
    logSuccess('Jest is installed');
  } catch (error) {
    logError('Jest is not installed. Please run: npm install --save-dev jest');
    process.exit(1);
  }
  
  // Check if test files exist
  const testFiles = [
    'tests/mpesa/mpesa-b2c.test.js',
    'tests/mpesa/mpesa-models.test.js',
    'tests/mpesa/mpesa-services.test.js'
  ];
  
  for (const testFile of testFiles) {
    if (fs.existsSync(path.join(__dirname, '../../', testFile))) {
      logSuccess(`Found ${testFile}`);
    } else {
      logWarning(`Test file not found: ${testFile}`);
    }
  }
  
  // Check if database is accessible
  logInfo('Database connection will be tested during test execution');
}

async function runMpesaTests() {
  logHeader('Running M-Pesa Backend Tests');
  
  const jestConfig = path.join(__dirname, 'jest.config.js');
  
  try {
    await runCommand('npx', [
      'jest',
      '--config', jestConfig,
      '--verbose',
      '--coverage',
      '--detectOpenHandles',
      '--forceExit'
    ]);
    
    logSuccess('All M-Pesa backend tests passed!');
    return true;
  } catch (error) {
    logError('Some M-Pesa backend tests failed');
    return false;
  }
}

async function generateTestReport() {
  logHeader('Generating Test Report');
  
  const coverageDir = path.join(__dirname, '../../coverage/mpesa');
  
  if (fs.existsSync(coverageDir)) {
    logSuccess('Coverage report generated');
    logInfo(`Coverage report available at: ${coverageDir}/lcov-report/index.html`);
  } else {
    logWarning('Coverage report not found');
  }
}

async function main() {
  const startTime = Date.now();
  
  log('\n🚀 Starting M-Pesa Backend Test Suite', 'bright');
  log(`📅 ${new Date().toLocaleString()}`, 'cyan');
  
  try {
    // Check prerequisites
    await checkPrerequisites();
    
    // Run tests
    const testsPassed = await runMpesaTests();
    
    // Generate report
    await generateTestReport();
    
    // Summary
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    
    logHeader('Test Summary');
    log(`⏱️  Duration: ${duration} seconds`, 'cyan');
    
    if (testsPassed) {
      logSuccess('M-Pesa Backend Tests: PASSED');
      log('\n🎉 All tests completed successfully!', 'green');
      process.exit(0);
    } else {
      logError('M-Pesa Backend Tests: FAILED');
      log('\n💥 Some tests failed. Please check the output above.', 'red');
      process.exit(1);
    }
    
  } catch (error) {
    logError(`Test execution failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle process signals
process.on('SIGINT', () => {
  log('\n\n⚠️  Test execution interrupted by user', 'yellow');
  process.exit(1);
});

process.on('SIGTERM', () => {
  log('\n\n⚠️  Test execution terminated', 'yellow');
  process.exit(1);
});

// Run the test suite
if (require.main === module) {
  main().catch((error) => {
    logError(`Unexpected error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runMpesaTests, checkPrerequisites };
