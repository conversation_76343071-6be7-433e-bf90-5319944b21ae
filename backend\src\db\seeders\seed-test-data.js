const { sequelize, User, Category, Product, ProductImage, ProductCategory } = require('../models');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');

// Test data
const categories = [
  {
    name: '<PERSON><PERSON>',
    slug: 'mice',
    description: 'Computer mice for gaming and productivity',
    imageUrl: 'https://example.com/categories/mice.jpg',
    displayOrder: 1,
    isActive: true
  },
  {
    name: 'Keyboards',
    slug: 'keyboards',
    description: 'Mechanical and membrane keyboards',
    imageUrl: 'https://example.com/categories/keyboards.jpg',
    displayOrder: 2,
    isActive: true
  },
  {
    name: 'Headsets',
    slug: 'headsets',
    description: 'Gaming and office headsets',
    imageUrl: 'https://example.com/categories/headsets.jpg',
    displayOrder: 3,
    isActive: true
  },
  {
    name: 'Monitors',
    slug: 'monitors',
    description: 'Gaming and productivity monitors',
    imageUrl: 'https://example.com/categories/monitors.jpg',
    displayOrder: 4,
    isActive: true
  }
];

const products = [
  {
    name: 'Gaming Mouse X1',
    brand: 'TechGear',
    model: 'X1',
    description: 'High-performance gaming mouse with RGB lighting',
    sku: 'TG-MOUSE-X1',
    regularPrice: 59.99,
    salePrice: 49.99,
    cost: 25.00,
    stockQuantity: 100,
    minOrderQuantity: 1,
    maxOrderQuantity: 5,
    weight: 0.2,
    dimensions: { length: 12, width: 7, height: 4 },
    featured: true,
    isActive: true,
    slug: 'gaming-mouse-x1',
    categories: ['mice'],
    images: [
      {
        imageUrl: 'https://example.com/products/mouse-x1-1.jpg',
        altText: 'Gaming Mouse X1 - Front View',
        isPrimary: true,
        displayOrder: 1
      },
      {
        imageUrl: 'https://example.com/products/mouse-x1-2.jpg',
        altText: 'Gaming Mouse X1 - Side View',
        isPrimary: false,
        displayOrder: 2
      }
    ]
  },
  {
    name: 'Mechanical Keyboard K2',
    brand: 'TechGear',
    model: 'K2',
    description: 'Mechanical keyboard with customizable RGB lighting',
    sku: 'TG-KB-K2',
    regularPrice: 129.99,
    salePrice: null,
    cost: 65.00,
    stockQuantity: 50,
    minOrderQuantity: 1,
    maxOrderQuantity: 3,
    weight: 0.9,
    dimensions: { length: 44, width: 14, height: 4 },
    featured: true,
    isActive: true,
    slug: 'mechanical-keyboard-k2',
    categories: ['keyboards'],
    images: [
      {
        imageUrl: 'https://example.com/products/keyboard-k2-1.jpg',
        altText: 'Mechanical Keyboard K2 - Top View',
        isPrimary: true,
        displayOrder: 1
      },
      {
        imageUrl: 'https://example.com/products/keyboard-k2-2.jpg',
        altText: 'Mechanical Keyboard K2 - Side View',
        isPrimary: false,
        displayOrder: 2
      }
    ]
  },
  {
    name: 'Gaming Headset H3',
    brand: 'TechGear',
    model: 'H3',
    description: 'Surround sound gaming headset with noise-cancelling microphone',
    sku: 'TG-HS-H3',
    regularPrice: 89.99,
    salePrice: 79.99,
    cost: 40.00,
    stockQuantity: 75,
    minOrderQuantity: 1,
    maxOrderQuantity: 3,
    weight: 0.35,
    dimensions: { length: 20, width: 18, height: 9 },
    featured: false,
    isActive: true,
    slug: 'gaming-headset-h3',
    categories: ['headsets'],
    images: [
      {
        imageUrl: 'https://example.com/products/headset-h3-1.jpg',
        altText: 'Gaming Headset H3 - Front View',
        isPrimary: true,
        displayOrder: 1
      },
      {
        imageUrl: 'https://example.com/products/headset-h3-2.jpg',
        altText: 'Gaming Headset H3 - Side View',
        isPrimary: false,
        displayOrder: 2
      }
    ]
  },
  {
    name: '27" Gaming Monitor M1',
    brand: 'TechGear',
    model: 'M1',
    description: '27-inch 144Hz gaming monitor with 1ms response time',
    sku: 'TG-MON-M1',
    regularPrice: 299.99,
    salePrice: 279.99,
    cost: 180.00,
    stockQuantity: 30,
    minOrderQuantity: 1,
    maxOrderQuantity: 2,
    weight: 5.2,
    dimensions: { length: 61, width: 37, height: 21 },
    featured: true,
    isActive: true,
    slug: '27-gaming-monitor-m1',
    categories: ['monitors'],
    images: [
      {
        imageUrl: 'https://example.com/products/monitor-m1-1.jpg',
        altText: '27" Gaming Monitor M1 - Front View',
        isPrimary: true,
        displayOrder: 1
      },
      {
        imageUrl: 'https://example.com/products/monitor-m1-2.jpg',
        altText: '27" Gaming Monitor M1 - Side View',
        isPrimary: false,
        displayOrder: 2
      }
    ]
  }
];

const users = [
  {
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'Admin@123',
    role: 'admin'
  },
  {
    name: 'Test Customer',
    email: '<EMAIL>',
    password: 'Customer@123',
    role: 'customer'
  }
];

// Seed function
async function seedData() {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Start a transaction
    const transaction = await sequelize.transaction();
    
    try {
      // Create categories
      console.log('Creating categories...');
      const categoryMap = {};
      
      for (const categoryData of categories) {
        const category = await Category.create(categoryData, { transaction });
        categoryMap[category.slug] = category.id;
      }
      
      // Create users
      console.log('Creating users...');
      for (const userData of users) {
        const hashedPassword = await bcrypt.hash(userData.password, 10);
        await User.create({
          ...userData,
          password: hashedPassword
        }, { transaction });
      }
      
      // Create products with images and categories
      console.log('Creating products...');
      for (const productData of products) {
        const { categories: productCategories, images, ...data } = productData;
        
        // Create product
        const product = await Product.create(data, { transaction });
        
        // Add categories
        if (productCategories && productCategories.length > 0) {
          for (const categorySlug of productCategories) {
            const categoryId = categoryMap[categorySlug];
            if (categoryId) {
              await ProductCategory.create({
                productId: product.id,
                categoryId,
                isPrimary: productCategories.indexOf(categorySlug) === 0
              }, { transaction });
            }
          }
        }
        
        // Add images
        if (images && images.length > 0) {
          for (const imageData of images) {
            await ProductImage.create({
              ...imageData,
              productId: product.id
            }, { transaction });
          }
        }
      }
      
      // Commit transaction
      await transaction.commit();
      console.log('✅ Database seeding completed successfully!');
    } catch (error) {
      // Rollback transaction on error
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
  } finally {
    // Close database connection
    await sequelize.close();
  }
}

// Run the seed function
seedData();
