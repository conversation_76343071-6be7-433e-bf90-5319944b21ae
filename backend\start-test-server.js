const { spawn } = require('child_process');
const path = require('path');

// Set environment variables for testing
process.env.NODE_ENV = 'test';
process.env.PORT = process.env.TEST_PORT || 3001;
process.env.DB_NAME = process.env.TEST_DB_NAME || 'techgear_test';

console.log(`🚀 Starting test server on port ${process.env.PORT}...`);
console.log(`📊 Using test database: ${process.env.DB_NAME}`);

// Start the server
const serverProcess = spawn('node', [path.join(__dirname, 'src/server.js')], {
  stdio: 'inherit',
  env: process.env
});

// Handle server process events
serverProcess.on('close', (code) => {
  if (code !== 0) {
    console.error(`\n❌ Server process exited with code ${code}`);
  }
});

serverProcess.on('error', (err) => {
  console.error(`\n❌ Failed to start server: ${err.message}`);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping test server...');
  serverProcess.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Stopping test server...');
  serverProcess.kill('SIGTERM');
  process.exit(0);
});
