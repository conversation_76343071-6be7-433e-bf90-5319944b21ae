const { validationResult } = require('express-validator');
const { ValidationError } = require('../../utils/errors');

/**
 * Middleware to validate request data using express-validator
 */
exports.validate = (req, res, next) => {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.path,
      message: error.msg,
      value: error.value
    }));

    // Log validation errors for debugging
    console.log('Validation errors:', errorMessages);
    console.log('Request body:', JSON.stringify(req.body, null, 2));

    return next(new ValidationError('Validation failed', errorMessages));
  }

  next();
};
