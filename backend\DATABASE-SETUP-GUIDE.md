# Database Setup Guide for TechGear E-commerce Platform

This guide will help you set up the PostgreSQL database for the TechGear e-commerce platform using pgAdmin.

## Prerequisites

- PostgreSQL installed on your system
- pgAdmin 4 installed and configured
- Access to create databases and tables

## Step 1: Create the Database

1. Open pgAdmin 4
2. In the browser tree, right-click on "Databases" under your PostgreSQL server
3. Select "Create" > "Database..."
4. Enter "techgear" as the database name
5. Click "Save"

## Step 2: Create the Database Schema

There are two ways to create the database schema:

### Option 1: Using the SQL Script (Recommended)

1. In pgAdmin, select the "techgear" database you just created
2. Click on the "Query Tool" button in the top toolbar (or right-click on the database and select "Query Tool")
3. Open the file `database-setup.sql` from this project
4. Copy and paste the entire SQL script into the query editor
5. Click the "Execute/Refresh" button (or press F5)
6. Wait for the script to complete execution

### Option 2: Using Sequelize Migrations (Advanced)

If you prefer to use Sequelize migrations:

1. Make sure you have Node.js and npm installed
2. Install Sequelize CLI globally:
   ```
   npm install -g sequelize-cli
   ```
3. Update the `.env` file with your PostgreSQL credentials:
   ```
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=techgear
   DB_USER=your_postgres_username
   DB_PASSWORD=your_postgres_password
   ```
4. Run the migrations:
   ```
   cd backend
   npx sequelize-cli db:migrate
   ```

## Step 3: Verify the Database Setup

1. In pgAdmin, expand the "techgear" database
2. Expand "Schemas" > "public" > "Tables"
3. You should see the following tables:
   - Users
   - Categories
   - Products
   - ProductImages
   - ProductCategories
   - Carts
   - CartItems
   - Wishlists
   - WishlistItems
   - Orders
   - OrderItems
   - Payments
   - Reviews
   - Coupons
   - CouponUsages

## Step 4: Seed the Database with Test Data (Optional)

To add some test data to the database:

1. In pgAdmin, select the "techgear" database
2. Click on the "Query Tool" button
3. Execute the following SQL to create an admin user:

```sql
INSERT INTO "Users" (
  "name", "email", "password", "role", "isActive"
) VALUES (
  'Admin User',
  '<EMAIL>',
  '$2b$10$XtMTHZpfxhBjt0Awj1O5/.0TrF7UZQx0xkUEjsWXNYkuRz0B3HBXO', -- Password: Admin@123
  'admin',
  true
);

INSERT INTO "Users" (
  "name", "email", "password", "role", "isActive"
) VALUES (
  'Test Customer',
  '<EMAIL>',
  '$2b$10$XtMTHZpfxhBjt0Awj1O5/.0TrF7UZQx0xkUEjsWXNYkuRz0B3HBXO', -- Password: Customer@123
  'customer',
  true
);

-- Insert some categories
INSERT INTO "Categories" (
  "name", "description", "slug", "isActive"
) VALUES
  ('Mice', 'Computer mice for gaming and productivity', 'mice', true),
  ('Keyboards', 'Mechanical and membrane keyboards', 'keyboards', true),
  ('Headsets', 'Gaming and office headsets', 'headsets', true),
  ('Monitors', 'Gaming and productivity monitors', 'monitors', true);

-- Insert a sample product
INSERT INTO "Products" (
  "name", "brand", "model", "description", "sku", "regularPrice", 
  "salePrice", "cost", "stockQuantity", "slug", "isActive"
) VALUES (
  'Gaming Mouse X1', 
  'TechGear', 
  'X1', 
  'High-performance gaming mouse with RGB lighting', 
  'TG-MOUSE-X1', 
  59.99, 
  49.99, 
  25.00, 
  100, 
  'gaming-mouse-x1', 
  true
);

-- Get the product ID
DO $$
DECLARE
  product_id UUID;
  category_id UUID;
BEGIN
  SELECT "id" INTO product_id FROM "Products" WHERE "slug" = 'gaming-mouse-x1';
  SELECT "id" INTO category_id FROM "Categories" WHERE "slug" = 'mice';
  
  -- Insert product image
  INSERT INTO "ProductImages" (
    "productId", "imageUrl", "altText", "isPrimary"
  ) VALUES (
    product_id, 
    'https://example.com/products/mouse-x1-1.jpg', 
    'Gaming Mouse X1 - Front View', 
    true
  );
  
  -- Link product to category
  INSERT INTO "ProductCategories" (
    "productId", "categoryId", "isPrimary"
  ) VALUES (
    product_id,
    category_id,
    true
  );
END $$;
```

## Troubleshooting

### Connection Issues

If you encounter connection issues:

1. Verify that PostgreSQL is running
2. Check your connection details in pgAdmin
3. Make sure the user has appropriate permissions

### SQL Errors

If you encounter SQL errors when running the script:

1. Check if the database already has tables with the same names
2. Verify that the SQL syntax is compatible with your PostgreSQL version
3. Try running the statements one by one to identify the problematic statement

## Next Steps

After setting up the database:

1. Configure the backend to connect to the database
2. Start the backend server
3. Test the API endpoints

For more information, refer to the main README.md file.
